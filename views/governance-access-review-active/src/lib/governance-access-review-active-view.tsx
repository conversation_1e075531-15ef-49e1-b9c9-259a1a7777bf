import { noop } from 'lodash-es';
import {
    AccessReviewActiveDataTable,
    AccessReviewPeriodRangeComponent,
} from '@components/access-review';
import { sharedActiveAccessReviewPeriodsController } from '@controllers/access-reviews';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Stack } from '@cosmos/components/stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

export const GovernanceAccessReviewActiveView = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();

        const MINIMUM_TOTAL_APPLICATIONS = 1;
        const { isLoading, totalApplications, hasActivePeriod } =
            sharedActiveAccessReviewPeriodsController;
        const { hasLimitedAccess } = sharedFeatureAccessModel;

        const { currentWorkspace } = sharedWorkspacesController;

        if (!hasActivePeriod && !isLoading) {
            return (
                <EmptyState
                    isStacked
                    title={t`Use review periods to carry out time bound access reviews`}
                    description={t`Initiate periodic reviews and upon completion, evidence generated is automatically linked to relevant controls, ensuring compliance and saving you time.`}
                    illustrationName="AddCircle"
                    leftAction={
                        !hasLimitedAccess && (
                            <Button
                                colorScheme="primary"
                                size="md"
                                label={t`Create review period`}
                                onClick={() => {
                                    navigate(
                                        `/workspaces/${currentWorkspace?.id}/governance/access-review/create-period`,
                                    );
                                }}
                            />
                        )
                    }
                    rightAction={
                        <AppLink
                            isExternal
                            size="md"
                            href="https://help.drata.com/en/articles/8895897-access-reviews#h_4d04aa7427"
                            label={t`Learn about access review periods`}
                        />
                    }
                />
            );
        }

        if (totalApplications < MINIMUM_TOTAL_APPLICATIONS && !isLoading) {
            return (
                <EmptyState
                    isStacked
                    illustrationName="AddCircle"
                    title="No applications in the review period"
                    description={
                        hasLimitedAccess
                            ? t`Contact your administrator to review the issue`
                            : t`You can add applications to this active review period by editing it.`
                    }
                    rightAction={
                        hasLimitedAccess || (
                            <Button
                                label={t`Edit review period`}
                                onClick={noop}
                            />
                        )
                    }
                    leftAction={
                        hasLimitedAccess || (
                            <Button
                                label={t`Go to connections`}
                                level="secondary"
                                onClick={noop}
                            />
                        )
                    }
                />
            );
        }

        return (
            <Stack
                py="8x"
                direction="column"
                gap="xl"
                data-testid="GovernanceAccessReviewActiveView"
                data-id="yzcfZDE1"
            >
                <AccessReviewPeriodRangeComponent />
                <AccessReviewActiveDataTable />
            </Stack>
        );
    },
);
