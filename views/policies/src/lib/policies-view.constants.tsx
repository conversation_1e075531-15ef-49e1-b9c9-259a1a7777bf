import type { ComponentProps } from 'react';
import {
    type PoliciesData,
    PoliciesTableApprovedDateCell,
    PoliciesTableArchivedActionsCell,
    PoliciesTableCellEditComponent,
    PoliciesTableCellExternalSourceComponent,
    PoliciesTableCellOwnerComponent,
    PoliciesTableCellPersonnelComponent,
    PoliciesTableCellSLAComponent,
    PoliciesTableCellStatusComponent,
    PoliciesTableCellTextComponent,
    PoliciesTableCellVersionComponent,
    PoliciesTablePublishedDateCell,
    PoliciesTableRenewalDateCell,
} from '@components/policies';
import type { Datatable, DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';

export const getPoliciesActiveColumns = (
    hasBambooHrConnection: boolean,
    externalPolicyConnection: boolean,
): DatatableProps<PoliciesData>['columns'] => {
    return [
        {
            id: 'ACTIONS',
            header: '',
            enableSorting: false,
            accessorKey: 'version',
            minSize: 40,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: PoliciesTableCellEditComponent,
        },
        {
            id: 'NAME',
            header: t({
                message: 'Name',
                comment: 'Table column header for policy name',
            }),
            enableSorting: true,
            accessorKey: 'name',
        },
        {
            id: 'VERSION',
            header: t({
                message: 'Version',
                comment: 'Table column header for policy version',
            }),
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellVersionComponent,
        },
        {
            id: 'STATUS',
            header: t({
                message: 'Status',
                comment: 'Table column header for policy status',
            }),
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellStatusComponent,
        },
        {
            id: 'CREATED',
            header: t({
                message: 'Created on',
                comment: 'Table column header for policy creation date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            minSize: 170,
            cell: PoliciesTableCellTextComponent,
        },
        {
            id: 'APPROVED_DATE',
            header: hasBambooHrConnection
                ? t({
                      message: 'Approved in Bamboo HR',
                      comment:
                          'Table column header for Bamboo HR approval date',
                  })
                : t({
                      message: 'Approved on',
                      comment: 'Table column header for policy approval date',
                  }),
            minSize: 170,
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableApprovedDateCell,
        },
        {
            id: 'PUBLISHED_DATE',
            header: t({
                message: 'Published on',
                comment: 'Table column header for policy publication date',
            }),
            minSize: 170,
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTablePublishedDateCell,
        },
        {
            id: 'RENEWAL_DATE',
            header: t({
                message: 'Renewal date',
                comment: 'Table column header for policy renewal date',
            }),
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableRenewalDateCell,
        },
        ...(externalPolicyConnection
            ? [
                  {
                      id: 'EXTERNAL_SOURCE',
                      header: t({
                          message: 'External source',
                          comment:
                              'Table column header for external policy source',
                      }),
                      enableSorting: false,
                      accessorKey: 'name',
                      accessorFn: () => false,
                      enableHiding: true,
                      cell: PoliciesTableCellExternalSourceComponent,
                  },
              ]
            : []),
        {
            id: 'OWNER',
            header: t({
                message: 'Owner',
                comment: 'Table column header for policy owner',
            }),
            enableSorting: false,
            accessorKey: 'currentOwner',
            cell: PoliciesTableCellOwnerComponent,
        },
        {
            id: 'GROUPS',
            header: t({
                message: 'Assignment',
                comment: 'Table column header for policy assignment',
            }),
            enableSorting: false,
            accessorKey: 'assignedTo',
            cell: PoliciesTableCellPersonnelComponent,
        },
        {
            id: 'SLA',
            header: t({
                message: 'SLA',
                comment: 'Table column header for policy SLA',
            }),
            enableSorting: true,
            accessorKey: 'version',
            minSize: 150,
            cell: PoliciesTableCellSLAComponent,
        },
    ] as const satisfies DatatableProps<PoliciesData>['columns'];
};

export const getPoliciesArchivedColumns = (
    hasBambooHrConnection: boolean,
    externalPolicyConnection: boolean,
): DatatableProps<PoliciesData>['columns'] => {
    return [
        {
            id: 'ACTIONS',
            header: '',
            enableSorting: false,
            accessorKey: 'version',
            minSize: 40,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: ({ row, column }) => (
                <PoliciesTableArchivedActionsCell
                    row={row}
                    column={column}
                    data-id="ZtDseXl2"
                />
            ),
        },
        {
            id: 'NAME',
            header: t({
                message: 'Name',
                comment: 'Table column header for policy name',
            }),
            enableSorting: true,
            accessorKey: 'name',
        },
        {
            id: 'REPLACED_BY',
            header: t({
                message: 'Replaced by',
                comment: 'Table column header for replacement policy',
            }),
            enableSorting: false,
            accessorKey: 'replacedBy',
        },
        {
            id: 'VERSION',
            header: 'Version',
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellVersionComponent,
        },
        {
            id: 'STATUS',
            header: 'Status',
            enableSorting: false,
            accessorKey: 'version',
            cell: PoliciesTableCellStatusComponent,
        },
        {
            id: 'CREATED',
            header: 'Created on',
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableCellTextComponent,
        },
        {
            id: 'APPROVED_DATE',
            header: hasBambooHrConnection
                ? 'Approved in Bamboo HR'
                : 'Approved on',
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableApprovedDateCell,
        },
        {
            id: 'PUBLISHED_DATE',
            header: 'Published on',
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTablePublishedDateCell,
        },
        {
            id: 'RENEWAL_DATE',
            header: 'Renewal date',
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableRenewalDateCell,
        },
        ...(externalPolicyConnection
            ? [
                  {
                      id: 'EXTERNAL_SOURCE',
                      header: 'External source',
                      enableSorting: false,
                      accessorKey: 'name',
                      accessorFn: () => false,
                      enableHiding: true,
                      cell: PoliciesTableCellExternalSourceComponent,
                  },
              ]
            : []),
        {
            id: 'OWNER',
            header: 'Owner',
            enableSorting: false,
            accessorKey: 'currentOwner',
            cell: PoliciesTableCellOwnerComponent,
        },
        {
            id: 'GROUPS',
            header: 'Assignment',
            enableSorting: false,
            accessorKey: 'assignedTo',
            cell: PoliciesTableCellPersonnelComponent,
        },
        {
            id: 'SLA',
            header: 'SLA',
            enableSorting: true,
            accessorKey: 'version',
            cell: PoliciesTableCellSLAComponent,
        },
    ];
};

export const getPoliciesTableActions = (
    canDownloadPolicies: boolean,
    hasPublishedPolicies: boolean,
    isDownloading: boolean,
    onDownloadAllPolicies: () => void,
): ComponentProps<typeof Datatable>['tableActions'] => {
    // Only show the download button when it's actually usable
    // This provides better UX than showing a disabled button
    if (!canDownloadPolicies || !hasPublishedPolicies) {
        return [];
    }

    return [
        {
            actionType: 'button',
            id: 'download-all-policies-button',
            typeProps: {
                label: t({
                    message: 'Download all',
                    comment: 'Button label to download all policies',
                }),
                colorScheme: 'neutral',
                level: 'secondary',
                startIconName: 'Download',
                isLoading: isDownloading,
                a11yLoadingLabel: t({
                    message: 'Downloading all policies',
                    comment: 'Loading label when downloading all policies',
                }),
                onClick: onDownloadAllPolicies,
            },
        },
    ];
};
