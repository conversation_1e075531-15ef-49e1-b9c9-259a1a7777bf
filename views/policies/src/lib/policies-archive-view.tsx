import { PoliciesRenewalDateBanner } from '@components/policies';
import { sharedPoliciesController } from '@controllers/policies';
import { Datatable } from '@cosmos/components/datatable';
import { Grid } from '@cosmos/components/grid';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import { useNavigate } from '@remix-run/react';
import { policiesAdaptor } from './policies-adaptors';
import { getPoliciesArchivedColumns } from './policies-view.constants';

export const PoliciesArchiveView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const {
        archivedPoliciesList,
        loadArchivedPolicies,
        isArchivedPoliciesLoading,
        archivedPoliciesTotal,
        overviewFilter,
    } = sharedPoliciesController;

    const archivedPolicies = policiesAdaptor(archivedPoliciesList);

    const { externalPolicyConnection, hasBambooHrConnection, filters } =
        sharedPoliciesPageHeaderModel;

    const columns = getPoliciesArchivedColumns(
        hasBambooHrConnection,
        externalPolicyConnection,
    );

    const navigateToPolicyDetail = (policyId: number) => {
        navigate(
            `/workspaces/1/governance/policies/builder/${policyId}/overview`,
        );
    };

    return (
        <Grid gap="lg" data-testid="PoliciesArchiveView" data-id="mGhO8JRX">
            <PoliciesRenewalDateBanner />
            <Datatable
                key={`datatable-policies-${overviewFilter || 'none'}`}
                isLoading={isArchivedPoliciesLoading}
                tableId="datatable-policies"
                data-id="datatable-policies"
                data={archivedPolicies}
                columns={columns}
                total={archivedPoliciesTotal}
                filterProps={filters}
                tableActions={[]}
                emptyStateProps={{
                    title: t({
                        message: 'No archived policies found',
                        comment:
                            'Title when no archived policies match the current filters',
                    }),
                    description: t({
                        message:
                            "Try adjusting your filters or search terms to find the archived policies you're looking for.",
                        comment:
                            'Description when no archived policies match the current filters',
                    }),
                    illustrationName: 'NoResults',
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t({
                        message: 'Search by name...',
                        comment: 'Placeholder text for policy name search',
                    }),
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t({
                            message: 'Pin filters to page',
                            comment: 'Label for pinning filters to page',
                        }),
                        toggleUnpinnedLabel: t({
                            message: 'Move filters to dropdown',
                            comment: 'Label for moving filters to dropdown',
                        }),
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                onFetchData={loadArchivedPolicies}
                onRowClick={({ row }) => {
                    navigateToPolicyDetail(row.id);
                }}
            />
        </Grid>
    );
});
