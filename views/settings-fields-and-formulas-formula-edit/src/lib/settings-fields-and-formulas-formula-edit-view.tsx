import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedFieldsAndFormulasFormulaDetailsController } from '@controllers/fields-and-formulas-formula-details';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { observer } from '@globals/mobx';
import { FormulaDetails } from './components/formula-details.component';
import { FormulaDetailsEdit } from './components/formula-details-edit.component';
import { FormulaExpression } from './components/formula-expression.component';
import { FormulaExpressionEdit } from './components/formula-expression-edit.component';
import { FormulaPlacement } from './components/formula-placement.component';
import { FormulaPlacementEdit } from './components/formula-placement-edit.component';

export const SettingsFieldsAndFormulasFormulaEditView = observer(
    (): React.JSX.Element => {
        const { formulaDetailsData, isLoading } =
            sharedFieldsAndFormulasFormulaDetailsController;

        if (isLoading || !formulaDetailsData) {
            return <>loading</>;
        }

        const {
            name,
            description,
            expression,
            isHidden,
            location: locations,
        } = formulaDetailsData;
        const [{ entity, section }] = locations ?? [];

        return (
            <Grid
                gap="6x"
                columns="repeat(2, 1fr)"
                data-testid="SettingsFieldsAndFormulasFormulaEditView"
                data-id="apP0aO6j"
            >
                <Box gridRowStart="1" gridRowEnd="span 2" gridColumn="1">
                    <ViewEditCardComponent
                        title="Details"
                        readOnlyComponent={
                            <FormulaDetails
                                name={name}
                                description={description ?? undefined}
                                isHidden={isHidden}
                            />
                        }
                        editComponent={
                            <FormulaDetailsEdit
                                name={name}
                                description={description ?? undefined}
                                isHidden={isHidden}
                            />
                        }
                        /**
                         * TODO: implement save logic.
                         */
                        onSave={() => Promise.resolve(true)}
                    />
                </Box>
                <Box gridRow="1" gridColumn="2">
                    <ViewEditCardComponent
                        title="Placement"
                        editComponent={
                            <FormulaPlacementEdit
                                entity={entity}
                                section={section}
                            />
                        }
                        readOnlyComponent={
                            <FormulaPlacement
                                entity={entity}
                                section={section}
                            />
                        }
                        /**
                         * TODO: implement save logic.
                         */
                        onSave={() => Promise.resolve(true)}
                    />
                </Box>
                <Box gridRow="2" gridColumn="2">
                    <ViewEditCardComponent
                        title="Formula"
                        readOnlyComponent={
                            <FormulaExpression expression={expression} />
                        }
                        editComponent={
                            <FormulaExpressionEdit expression={expression} />
                        }
                        /**
                         * TODO: implement save logic.
                         */
                        onSave={() => Promise.resolve(true)}
                    />
                </Box>
            </Grid>
        );
    },
);
