import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedApiKeysDetailsController } from '@controllers/api-keys';
import { sharedApiKeysPermissionsController } from '@controllers/api-keys-permissions';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { observer } from '@globals/mobx';
import { ApiKeysDetailsComponent } from './components/api-keys-details.component';
import { ApiKeysDetailsEditFormFieldsComponent } from './components/api-keys-details-edit-form-fields.component';
import { ApiKeysScopeComponent } from './components/api-keys-scope.component';
import { ApiKeysScopeEditFormFieldsComponent } from './components/api-keys-scope-edit-form-fields.component';

export const SettingsApiKeysDetailsView = observer((): React.JSX.Element => {
    const { apiKeysDetails, isLoading } = sharedApiKeysDetailsController;
    const { apiKeysPermissionList, isLoading: apiKeysPermissionsLoading } =
        sharedApiKeysPermissionsController;

    if (isLoading || apiKeysPermissionsLoading || !apiKeysDetails) {
        return <Loader isSpinnerOnly label={'Loading...'} />;
    }

    return (
        <Grid
            gap="6x"
            columns="2"
            data-testid="SettingsApiKeysDetailsView"
            data-id="apP0aO6j"
        >
            <Box>
                <ViewEditCardComponent
                    title="Details"
                    readOnlyComponent={
                        <ApiKeysDetailsComponent
                            name={apiKeysDetails.name}
                            publicApiKey={apiKeysDetails.publicApiKey}
                            accessType={apiKeysDetails.accessType}
                            expiresAt={apiKeysDetails.expiresAt}
                            allowListIPAddresses={
                                apiKeysDetails.allowListIPAddresses
                            }
                        />
                    }
                    editComponent={
                        <ApiKeysDetailsEditFormFieldsComponent
                            name={apiKeysDetails.name}
                            publicApiKey={apiKeysDetails.publicApiKey}
                            accessType={apiKeysDetails.accessType}
                            expiresAt={apiKeysDetails.expiresAt}
                            allowListIPAddresses={
                                apiKeysDetails.allowListIPAddresses
                            }
                        />
                    }
                    /**
                     * TODO: implement save logic.
                     */
                    onSave={() => Promise.resolve(true)}
                />
            </Box>
            <Box>
                <ViewEditCardComponent
                    title="Placement"
                    readOnlyComponent={
                        <ApiKeysScopeComponent
                            accessType={apiKeysDetails.accessType}
                            permissions={apiKeysPermissionList}
                        />
                    }
                    editComponent={
                        <ApiKeysScopeEditFormFieldsComponent
                            accessType={apiKeysDetails.accessType}
                            permissions={apiKeysPermissionList}
                        />
                    }
                    /**
                     * TODO: implement save logic.
                     */
                    onSave={() => Promise.resolve(true)}
                />
            </Box>
        </Grid>
    );
});
