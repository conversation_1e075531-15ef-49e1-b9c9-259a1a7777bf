import { isEmpty } from 'lodash-es';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedFieldsAndFormulasFieldDetailsController } from '@controllers/fields-and-formulas-field-details';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { observer } from '@globals/mobx';
import { FieldComponent } from './components/field.component';
import { FieldDetailsComponent } from './components/field-details.component';
import { FieldDetailsEditComponent } from './components/field-details-edit.component';
import { FieldEditComponent } from './components/field-edit.component';
import { FieldFormulaUsageComponent } from './components/field-formula-usage.component';
import { FieldPlacementComponent } from './components/field-placement.component';
import { FieldPlacementEditComponent } from './components/field-placement-edit.component';

type FieldProps = Readonly<{
    id: number;
    value: string;
    isHidden: boolean;
    isManual: boolean;
}>;
const FIELDS: FieldProps[] = Array.from({ length: 3 }, (_, index) => {
    const booleanValue = index % 2 !== 0;

    return {
        id: index,
        value: `Option ${index + 1}`,
        isManual: booleanValue,
        isHidden: !booleanValue,
    };
});

export const SettingsFieldsAndFormulasFieldsEditView = observer(
    (): React.JSX.Element => {
        const { fieldDetailsData, isLoading } =
            sharedFieldsAndFormulasFieldDetailsController;

        if (isLoading || !fieldDetailsData) {
            return <>loading</>;
        }

        const {
            name,
            description,
            fieldType,
            isHidden,
            isRequired,
            options,
            numericOptions,
            location,
            associatedFormulas,
        } = fieldDetailsData;

        return (
            <Grid
                columns="2"
                gap="2xl"
                data-testid="SettingsFieldsAndFormulasFieldsEditView"
                data-id="dWRzxMpa"
            >
                <Box gridColumn="1">
                    <Grid gap="2xl">
                        <ViewEditCardComponent
                            title="Description"
                            editComponent={
                                <FieldDetailsEditComponent
                                    name={name}
                                    description={description}
                                />
                            }
                            readOnlyComponent={
                                <FieldDetailsComponent
                                    name={name}
                                    description={description}
                                />
                            }
                            /**
                             * TODO: implement save logic.
                             */
                            onSave={() => Promise.resolve(true)}
                        />
                        <ViewEditCardComponent
                            title="Placement"
                            editComponent={
                                <FieldPlacementEditComponent
                                    placements={location ?? []}
                                />
                            }
                            readOnlyComponent={
                                <FieldPlacementComponent
                                    placements={location ?? []}
                                />
                            }
                            /**
                             * TODO: implement save logic.
                             */
                            onSave={() => Promise.resolve(true)}
                        />
                    </Grid>
                </Box>
                <Box gridColumn="2">
                    <Grid gap="2xl">
                        <ViewEditCardComponent
                            title="Field"
                            editComponent={
                                <FieldEditComponent
                                    requiredField
                                    fields={FIELDS}
                                    hideField={false}
                                />
                            }
                            readOnlyComponent={
                                <FieldComponent
                                    fieldType={fieldType}
                                    isHidden={isHidden}
                                    isRequired={isRequired}
                                    fieldOptions={
                                        fieldType === 'OPTIONS_NUMERIC'
                                            ? numericOptions
                                            : options
                                    }
                                />
                            }
                            /**
                             * TODO: implement save logic.
                             */
                            onSave={() => Promise.resolve(true)}
                        />
                        {!isEmpty(associatedFormulas) && (
                            <FieldFormulaUsageComponent
                                formulas={associatedFormulas}
                            />
                        )}
                    </Grid>
                </Box>
            </Grid>
        );
    },
);
