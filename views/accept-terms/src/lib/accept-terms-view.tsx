import { sharedAuth<PERSON>ontroller } from '@controllers/auth';
import { Box } from '@cosmos/components/box';
import { But<PERSON> } from '@cosmos/components/button';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { NoComponent } from '@cosmos/components/no-component';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { LogoLoader } from '@cosmos-lab/components/logo-loader';
import { sharedCurrentUserController } from '@globals/current-user';
import { t, Trans } from '@globals/i18n/macro';
import { Navigate, useNavigate } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

const FORM_ID = 'accept-terms-form';

export const AcceptTermsView = (): React.JSX.Element => {
    const navigate = useNavigate();
    const { isAuditor, hasAcceptedTerms } = sharedCurrentUserController;

    const { authMode, isAcceptTermsPending } = sharedAuthController;

    const handleSubmit = () => {
        if (isAuditor) {
            sharedAuthController.acceptTermsAndConditions();
        } else {
            // TODO:For isServiceUser and rest of the roles, use other accept terms endpoints. It's intended to implement those methods in the corresponding authenticators.
            // This navigation is temporary. The good one is intended to happen with the Navigate component.
            navigate('workspaces/1/quick-start', { replace: true });
        }
    };

    if (hasAcceptedTerms) {
        return (
            <Navigate
                replace
                data-testid="Redirect"
                data-id="zcE3bjjJ"
                to={
                    authMode === 'AUDITOR'
                        ? '/audit-hub/clients'
                        : '/workspaces/1/quick-start'
                }
            />
        );
    }

    return (
        <Stack
            gap="6x"
            direction="column"
            data-testid="AcceptTermsView"
            data-id="LktK_-ee"
        >
            <Box>
                <Text type="subheadline" size="400" as="p">
                    <Trans>Welcome</Trans>
                </Text>
            </Box>
            <Stack gap="3x" direction="column">
                <Box>
                    <Text type="title" size="200" as="p">
                        <Trans>Please accept terms to continue</Trans>
                    </Text>
                </Box>
                <Box>
                    <Text type="body" size="200" as="p">
                        {isAuditor ? (
                            <Trans>
                                Prior to using Drata for your Clients’
                                compliance audit needs, please review and agree
                                to the
                            </Trans>
                        ) : (
                            <Trans>
                                To use Drata for your company’s compliance
                                needs, please review then agree to the
                            </Trans>
                        )}{' '}
                        <AppLink
                            isExternal
                            href="https://drata.com/terms"
                            label="Terms of Service"
                        />
                        <Trans>and</Trans>{' '}
                        <AppLink
                            isExternal
                            href="https://drata.com/privacy"
                            label="Privacy Policy"
                        />
                    </Text>
                </Box>
            </Stack>
            <Box>
                <CheckboxField
                    label={t`I agree to Drata’s Privacy Notice`}
                    formId={FORM_ID}
                    name="acceptance"
                    data-id={`${FORM_ID}-checkbox`}
                    aria-labelledby={undefined}
                    value={''}
                ></CheckboxField>
            </Box>
            <Box>
                <Button
                    label="Continue"
                    type="submit"
                    data-id={`${FORM_ID}-submit`}
                    onClick={handleSubmit}
                />
            </Box>
            <LogoLoader
                fadeOut={!isAcceptTermsPending}
                ariaLabel="Logging In"
            />
            {/* {!isAcceptTermsPending && !hasAcceptTermsError && (
                            <Navigate
                                replace
                                data-testid="Redirect"
                                data-id="zcE3bjjJ"
                                to={
                                    authMode === 'AUDITOR'
                                        ? '/audit-hub/clients'
                                        : '/workspaces/1/quick-start'
                                }
                            />
                        )} */}
        </Stack>
    );
};
