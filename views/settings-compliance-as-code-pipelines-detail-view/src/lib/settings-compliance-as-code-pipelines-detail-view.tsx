import { isNil } from 'lodash-es';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedPipelineApiKeysController } from '@controllers/pipeline-api-keys';
import { Banner } from '@cosmos/components/banner';
import { Card } from '@cosmos/components/card';
import {
    DatePickerField,
    type TDateISODate,
} from '@cosmos/components/date-picker-field';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { TextField } from '@cosmos/components/text-field';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { DateTime } from '@cosmos-lab/components/date-time';
import { observer } from '@globals/mobx';
import { getCodePipelineCodeSnippet } from '@views/settings-compliance-as-code-pipelines-create';

const FORM_ID = 'settings-compliance-as-code-pipelines-detail-view-form';

export const SettingsComplianceAsCodePipelinesDetailView = observer(
    (): React.JSX.Element | undefined => {
        const { apiKeyDetails, isKeyLoading } = sharedPipelineApiKeysController;
        const [keyName, setKeyName] = useState('');
        const [expirationDate, setExpirationDate] = useState<TDateISODate>();
        const onSaveHandler = useCallback(() => Promise.resolve(true), []);
        const handleExpirationDateChange = useCallback(
            (selectedDate: TDateISODate | TDateISODate[]) => {
                if (!isNil(selectedDate) && !Array.isArray(selectedDate)) {
                    // API is going to expect a regular date object. During mutation work, will need to convert this "2025-01-01" type to a date object.
                    setExpirationDate(selectedDate);
                }
            },
            [],
        );

        const code = useMemo(() => getCodePipelineCodeSnippet(), []);

        const expiresAtDate: TDateISODate = useMemo(() => {
            if (!expirationDate && apiKeyDetails?.expiresAt) {
                const date = new Date(apiKeyDetails.expiresAt);
                const year = date.getFullYear();
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');

                return `${year}-${month}-${day}` as TDateISODate;
            }

            return expirationDate as TDateISODate;
        }, [apiKeyDetails, expirationDate]);

        useEffect(() => {
            if (apiKeyDetails?.name) {
                setKeyName(apiKeyDetails.name);
            }
        }, [apiKeyDetails]);

        if (isKeyLoading || !apiKeyDetails) {
            return;
        }

        return (
            <Stack direction="row" gap="4x" data-id="bAhasTOk">
                <Stack direction="column" gap="4x" width="50%">
                    <ViewEditCardComponent
                        title="Details"
                        editComponent={
                            <Stack direction="column" gap="4x">
                                <TextField
                                    required
                                    formId={FORM_ID}
                                    name="name"
                                    label="Key name"
                                    value={keyName}
                                    onChange={(e) => {
                                        setKeyName(e.target.value);
                                    }}
                                />
                                <DatePickerField
                                    required
                                    formId={FORM_ID}
                                    label="Expiration date"
                                    name="expirationDate"
                                    locale={'en-US'}
                                    monthSelectionFieldLabel="Select Month"
                                    yearSelectionFieldLabel="Select Year"
                                    defaultValue={expiresAtDate}
                                    onChange={handleExpirationDateChange}
                                />
                            </Stack>
                        }
                        readOnlyComponent={
                            <Stack direction="column" gap="4x">
                                <KeyValuePair
                                    label="Key name"
                                    value={apiKeyDetails.name}
                                />
                                <KeyValuePair
                                    label="Expiration"
                                    type="REACT_NODE"
                                    value={
                                        apiKeyDetails.expiresAt ? (
                                            <DateTime
                                                date={
                                                    new Date(
                                                        apiKeyDetails.expiresAt,
                                                    )
                                                }
                                            />
                                        ) : (
                                            <Text>Never</Text>
                                        )
                                    }
                                />
                            </Stack>
                        }
                        onSave={onSaveHandler}
                    />
                </Stack>
                <Stack direction="column" gap="4x" width="50%" height="100%">
                    <Card
                        title="Configure Github action"
                        data-id="JIQqgNiv"
                        body={
                            <Stack direction="column" gap="4x" width="100%">
                                <Banner title="You can find this configuration again later if you need it by selecting the pipeline key in your pipeline settings." />
                                <CodeViewer
                                    data-id="settings-compliance-as-code-pipelines-detail-view"
                                    language="yml"
                                    value={code}
                                />
                            </Stack>
                        }
                    />
                </Stack>
            </Stack>
        );
    },
);
