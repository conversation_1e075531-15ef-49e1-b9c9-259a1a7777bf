import { isNil } from 'lodash-es';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedNotificationRuleController } from '@controllers/notification-rule';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { observer } from '@globals/mobx';
import { NotificationComponent } from './components/notification.component';
import { NotificationEditComponent } from './components/notification-edit.component';
import { TargetComponent } from './components/target.component';
import { TargetEditComponent } from './components/target-edit.component';

export const SettingsOrganizationalNotificationsEditView = observer(
    (): React.JSX.Element | null => {
        const { notificationRule, isLoading } =
            sharedNotificationRuleController;

        if (isLoading || isNil(notificationRule)) {
            return null;
        }

        return (
            <Grid
                data-testid="NotificationRuleDetailView"
                data-id="7N5x0RKk"
                columns="2"
                gap="6x"
                align="start"
            >
                <Box height="auto">
                    <ViewEditCardComponent
                        title="Target"
                        editComponent={<TargetEditComponent />}
                        readOnlyComponent={
                            <TargetComponent
                                notificationRule={notificationRule}
                            />
                        }
                        /**
                         * TODO: implement save logic.
                         */
                        onSave={() => Promise.resolve(true)}
                    />
                </Box>
                <ViewEditCardComponent
                    title="Notification"
                    editComponent={<NotificationEditComponent />}
                    readOnlyComponent={
                        <NotificationComponent
                            notificationRule={notificationRule}
                        />
                    }
                    /**
                     * TODO: implement save logic.
                     */
                    onSave={() => Promise.resolve(true)}
                />
            </Grid>
        );
    },
);
