import { noop } from 'lodash-es';
import { useCallback, useState } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import {
    type IUpdateConnectionController,
    sharedConnectionsController,
} from '@controllers/connections';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { Form, type FormValues, useFormSubmit } from '@ui/forms';
import { OverviewChangeWorkspaceComponent } from './overview-change-workspace.component';
import { OverviewUarToggleComponent } from './overview-provider-type-toggles.component';

interface Props {
    /**
     * Unique identifier for the provider specific form.
     */
    formId: string;

    /**
     * Whether or not the form should include the workspace change component.
     */
    hasWorkspaces?: boolean;

    /**
     * Whether or not the form should include the UAR toggle component.
     */
    hasUar?: boolean;

    /**
     * The controller that handles the "setup details" form on the connection overview page.
     */
    providerConnectController: IUpdateConnectionController;

    /**
     * If implemented, this function will be used instead of the default "updateConnection" handler function defined in your controller.
     *
     * @param formValues - The values of the "setup details" form on the connection overview page.
     * @returns
     */
    customOnSaveSetupDetailsHandler?: (formValues: FormValues) => void;
}

export const SettingsConnectionsOverviewBaseFormComponent = observer(
    ({
        formId,
        providerConnectController,
        customOnSaveSetupDetailsHandler,
        hasWorkspaces = false,
        hasUar = false,
    }: Props): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const { singleConnection } = sharedConnectionsController;
        const [isFormReadonly, setIsFormReadonly] = useState(true);
        const { getSetupDetailsFormSchema, updateConnection } =
            providerConnectController;
        const setupDetailsSchema = getSetupDetailsFormSchema(isFormReadonly);

        const handleSaveSetupDetails = useCallback(
            (formValues: FormValues) => {
                if (!singleConnection) {
                    return;
                }

                updateConnection({
                    connectionId: singleConnection.id,
                    values: formValues,
                });
            },
            [singleConnection, updateConnection],
        );

        return (
            <Stack gap="8x" direction="column" data-id="f4VKufkV">
                {hasWorkspaces && (
                    <OverviewChangeWorkspaceComponent formId={formId} />
                )}
                {hasUar && <OverviewUarToggleComponent formId={formId} />}

                <ViewEditCardComponent
                    title="Setup details"
                    data-id="97est3kF"
                    editComponent={
                        <Form
                            hasExternalSubmitButton
                            data-id="46dq0yxZ"
                            ref={formRef}
                            formId={formId}
                            schema={setupDetailsSchema}
                            onSubmit={
                                customOnSaveSetupDetailsHandler ??
                                handleSaveSetupDetails
                            }
                        />
                    }
                    readOnlyComponent={
                        <Form
                            hasExternalSubmitButton
                            data-id="46dq0yxZ"
                            formId={formId}
                            schema={setupDetailsSchema}
                            onSubmit={noop}
                        />
                    }
                    onSave={async () => {
                        const isValid = await triggerSubmit();

                        if (isValid) {
                            setIsFormReadonly(true);
                        }

                        return isValid;
                    }}
                    onCancel={() => {
                        setIsFormReadonly(true);
                    }}
                    onEdit={() => {
                        setIsFormReadonly(false);
                    }}
                />
            </Stack>
        );
    },
);
