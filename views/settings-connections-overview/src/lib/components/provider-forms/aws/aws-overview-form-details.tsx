import { useMemo } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { Card } from '@cosmos/components/card';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { observer } from '@globals/mobx';
import type { FormSchema } from '@ui/forms';
import { getClientCopyText } from '../../../helpers/settings-connections-overview-setup-helpers';
import type { Detail } from '../../../types/temp-types';
import { SettingsConnectionsOverviewSetupDetailBody } from './settings-connections-overview-aws-setup-detail-body';

interface Props {
    formId: string;
    schema: FormSchema;
    detailList: Detail[];
    connectionProviderId: string;
}

export const SettingsConnectionsAwsOverviewFormDetails = observer(
    ({
        formId,
        schema,
        detailList,
        connectionProviderId,
    }: Props): React.JSX.Element => {
        const getSetupDetailBody = (setupDetail: Detail, canEdit: boolean) => {
            const filteredSubDetails = setupDetail.subDetails?.filter(
                // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- Todo: need to create our own conditional renderer or figure out how to use form conditions
                (subDetail: Detail) => {
                    // eslint-disable-next-line sonarjs/prefer-single-boolean-return -- Todo: need to create our own conditional renderer or figure out how to use form conditions
                    if (subDetail.displayConditions) {
                        return true;
                    }

                    return true;
                },
            );

            return (
                <Stack
                    data-testid="getSetupDetailBody"
                    data-id="QabOKs9f"
                    direction="column"
                    gap="2xl"
                >
                    {setupDetail.subDetails ? (
                        <Stack gap="2xl" direction="column">
                            {filteredSubDetails?.map((subDetail: Detail) => {
                                return (
                                    <Stack
                                        key={subDetail.title.textContentId}
                                        data-id="6No03NrF"
                                        gap="2xl"
                                        direction="column"
                                    >
                                        <Text>
                                            {getClientCopyText(
                                                connectionProviderId,
                                                subDetail.title.textContentId,
                                            )}
                                        </Text>
                                        <SettingsConnectionsOverviewSetupDetailBody
                                            setupDetail={subDetail}
                                            canEdit={canEdit}
                                            formId={formId}
                                            providerId={connectionProviderId}
                                            schema={schema}
                                        />
                                    </Stack>
                                );
                            })}
                        </Stack>
                    ) : (
                        <SettingsConnectionsOverviewSetupDetailBody
                            setupDetail={setupDetail}
                            canEdit={canEdit}
                            providerId={connectionProviderId}
                            formId={formId}
                            schema={schema}
                        />
                    )}
                </Stack>
            );
        };

        const detailsToShow = useMemo(() => {
            // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- Todo: need to create our own conditional renderer or figure out how to use form conditions
            return detailList.filter((setupDetail: Detail) => {
                // eslint-disable-next-line sonarjs/prefer-single-boolean-return -- Todo: need to create our own conditional renderer or figure out how to use form conditions
                if (setupDetail.displayConditions) {
                    return true;
                }

                return true;
            });
        }, [detailList]);

        return (
            <Stack direction="column" gap="6x" data-id="9FsGGtI8">
                {detailsToShow.map((setupDetail: Detail) => {
                    return setupDetail.canEdit ? (
                        <ViewEditCardComponent
                            key={setupDetail.title.textContentId}
                            data-id="juUOoIvU"
                            editComponent={getSetupDetailBody(
                                setupDetail,
                                true,
                            )}
                            readOnlyComponent={getSetupDetailBody(
                                setupDetail,
                                false,
                            )}
                            title={getClientCopyText(
                                connectionProviderId,
                                setupDetail.title.textContentId,
                            )}
                            /**
                             * TODO: implement save logic.
                             */
                            onSave={() => Promise.resolve(true)}
                        />
                    ) : (
                        <Card
                            data-id="FZx_lG78"
                            key={setupDetail.title.textContentId}
                            body={getSetupDetailBody(setupDetail, false)}
                            title={getClientCopyText(
                                connectionProviderId,
                                setupDetail.title.textContentId,
                            )}
                        />
                    );
                })}
            </Stack>
        );
    },
);
