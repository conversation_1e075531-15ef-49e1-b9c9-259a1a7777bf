import { isNull } from 'lodash-es';
import { ViewEditCardComponent } from '@components/view-edit-card';
import { sharedTicketAutomationDetailsController } from '@controllers/ticket-automation-details';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { observer } from '@globals/mobx';
import { DetailsComponent } from './components/details.component';
import { DetailsEditComponent } from './components/details-edit.component';
import { ScopeComponent } from './components/scope.component';
import { ScopeEditComponent } from './components/scope-edit.component';
import { TicketComponent } from './components/ticket.component';
import { TicketEditComponent } from './components/ticket-edit.component';

export const SettingsTicketAutomationEditView = observer(
    (): React.JSX.Element => {
        const { isLoading, ticketAutomationDetails } =
            sharedTicketAutomationDetailsController;

        if (isLoading || isNull(ticketAutomationDetails)) {
            return <Loader label={'Loading...'} />;
        }

        const {
            id,
            name,
            project,
            projectId,
            criteria,
            connection,
            product,
            eventType,
            issueType,
            issueTypeId,
            ticketMonitoringFields,
        } = ticketAutomationDetails;

        return (
            <Grid
                gap="6x"
                columns="2"
                data-testid="SettingsTicketAutomationEditView"
                data-id={`SettingsTicketAutomationEditView-${id}`}
            >
                <Box>
                    <ViewEditCardComponent
                        title="Details"
                        readOnlyComponent={
                            <DetailsComponent
                                id={id}
                                name={name}
                                connection={connection}
                                product={product}
                                eventType={eventType}
                            />
                        }
                        editComponent={
                            <DetailsEditComponent
                                id={id}
                                name={name}
                                connection={connection}
                                product={product}
                                eventType={eventType}
                            />
                        }
                        /**
                         * TODO: implement save logic.
                         */
                        onSave={() => Promise.resolve(true)}
                    />
                </Box>
                <Box>
                    <Grid gap="6x">
                        <ViewEditCardComponent
                            title="Ticket"
                            editComponent={
                                <TicketEditComponent
                                    id={id}
                                    project={project}
                                    projectId={projectId}
                                    issueType={issueType}
                                    issueTypeId={issueTypeId}
                                    connectionId={connection.id}
                                    ticketMonitoringFields={
                                        ticketMonitoringFields
                                    }
                                />
                            }
                            readOnlyComponent={
                                <TicketComponent
                                    id={id}
                                    project={project}
                                    projectId={projectId}
                                    issueType={issueType}
                                    issueTypeId={issueTypeId}
                                    connectionId={connection.id}
                                    ticketMonitoringFields={
                                        ticketMonitoringFields
                                    }
                                />
                            }
                            /**
                             * TODO: implement save logic.
                             */
                            onSave={() => Promise.resolve(true)}
                        />
                        <ViewEditCardComponent
                            title="Scope"
                            readOnlyComponent={
                                <ScopeComponent
                                    id={id}
                                    criteria={criteria}
                                    workspaceId={product.id}
                                    eventType={eventType}
                                />
                            }
                            editComponent={
                                <ScopeEditComponent
                                    id={id}
                                    criteria={criteria}
                                    workspaceId={product.id}
                                    eventType={eventType}
                                />
                            }
                            /**
                             * TODO: implement save logic.
                             */
                            onSave={() => Promise.resolve(true)}
                        />
                    </Grid>
                </Box>
            </Grid>
        );
    },
);
