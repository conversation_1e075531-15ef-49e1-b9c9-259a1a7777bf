import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import type { FrameworkDetailsCellProps } from '../../types/framework-details.type';

export const FrameworkRequirementScopeCellComponent = ({
    row: { original },
}: FrameworkDetailsCellProps): React.JSX.Element => {
    const { archivedAt } = original;
    const scope = archivedAt ? t`Out of scope` : t`In scope`;

    return (
        <Text
            as="span"
            data-testid="FrameworkRequirementScopeCellComponent"
            data-id="QVobP7t5"
        >
            {scope}
        </Text>
    );
};
