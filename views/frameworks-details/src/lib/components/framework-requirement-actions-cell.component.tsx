import { useMemo } from 'react';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { FrameworkDetailsCellProps } from '../../types/framework-details.type';
import { markRequirementInScope } from '../helpers/mark-requirement-in-scope.helper';
import {
    openChangeRequirementCategoryModal,
    openDeleteRequirementModal,
    openMarkRequirementOutOfScopeModal,
} from '../helpers/open-requirement-modals.helper';

export const FrameworkRequirementActionsCellComponent = observer(
    ({
        row: { original },
    }: FrameworkDetailsCellProps): React.JSX.Element | null => {
        const { frameworkDetails } = sharedFrameworkDetailsController;
        const isOutOfScope = Boolean(original.archivedAt);
        const isCustomFramework = frameworkDetails?.tag === 'CUSTOM';

        const scopeAction = useMemo(
            () =>
                isOutOfScope
                    ? {
                          id: 'in-scope-framework-option',
                          label: t`Mark in scope`,
                          onClick: () => {
                              markRequirementInScope(original);
                          },
                      }
                    : {
                          id: 'out-of-scope-framework-option',
                          label: t`Mark out of scope`,
                          onClick: () => {
                              openMarkRequirementOutOfScopeModal([original]);
                          },
                      },
            [isOutOfScope, original],
        );

        // For custom frameworks: show all actions (scope, change category, delete)
        // For non-custom frameworks: show only scope action
        const items = useMemo(
            () =>
                isCustomFramework
                    ? [
                          scopeAction,
                          {
                              id: 'change-category-framework-option',
                              label: t`Change category`,
                              onClick: () => {
                                  openChangeRequirementCategoryModal([
                                      original,
                                  ]);
                              },
                          },
                          {
                              id: 'delete-requirement-group-option',
                              label: t`Delete requirement`,
                              colorScheme: 'critical' as const,
                              onClick: () => {
                                  openDeleteRequirementModal([original]);
                              },
                          },
                      ]
                    : [scopeAction],
            [isCustomFramework, scopeAction, original],
        );

        return (
            <SchemaDropdown
                isIconOnly
                startIconName="HorizontalMenu"
                label={t`Options`}
                level="tertiary"
                data-id="RFE55P"
                data-testid="FrameworkRequirementActionsCellComponent"
                items={items}
            />
        );
    },
);
