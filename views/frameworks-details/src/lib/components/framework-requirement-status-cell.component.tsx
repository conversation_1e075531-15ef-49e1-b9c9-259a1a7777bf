import { Metadata } from '@cosmos/components/metadata';
import type { FrameworkDetailsCellProps } from '../../types/framework-details.type';

export const FrameworkRequirementStatusCellComponent = ({
    row: { original },
}: FrameworkDetailsCellProps): React.JSX.Element => {
    const { isReady } = original;
    const label = isReady ? 'Ready' : 'Not Ready';

    return (
        <Metadata
            colorScheme={isReady ? 'success' : 'critical'}
            iconName={isReady ? 'CheckCircle' : 'Cancel'}
            label={label}
            type="tag"
            data-id="sfJW27Us"
            data-testid="FrameworkRequirementStatusCellComponent"
        />
    );
};
