import { isEmpty } from 'lodash-es';
import { Feedback } from '@cosmos/components/feedback';
import { DataDonut } from '@cosmos-lab/components/data-donut';
import { t } from '@globals/i18n/macro';
import type { FrameworkDetailsCellProps } from '../types/framework-details.type';
import { frameworkDetailsDonutValuesHelper } from './helpers/framework-details-donut-values-helper';

export const FrameworkDetailsRequirementListControlsCell = ({
    row: { original },
}: FrameworkDetailsCellProps): React.JSX.Element => {
    const { controls } = original;

    const donutValues = frameworkDetailsDonutValuesHelper({ controls });

    return (
        <>
            {isEmpty(donutValues) ? (
                <Feedback
                    title={t`No controls mapped`}
                    severity="critical"
                    data-id="framework-details-controls-no-controls"
                />
            ) : (
                <DataDonut
                    showLegend
                    data-id="framework-details-controls-data-donut"
                    size="md"
                    iconName="Controls"
                    unit={t`status`}
                    values={donutValues}
                    legendPosition="right"
                />
            )}
        </>
    );
};
