import {
    criticalBackgroundStrongInitial,
    successBackgroundModerate,
} from '@cosmos/constants/tokens';
import type { DataDonutSliceData } from '@cosmos-lab/components/data-donut';
import type { RequirementListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

interface FrameworkDetailsDonutValuesHelperProps {
    controls: RequirementListResponseDto['controls'];
}

export const frameworkDetailsDonutValuesHelper = ({
    controls,
}: FrameworkDetailsDonutValuesHelperProps): DataDonutSliceData[] => {
    const readyCount = controls.filter((control) => control.isReady).length;
    const notReadyCount = controls.filter((control) => !control.isReady).length;

    return [
        {
            label: t`Ready`,
            value: readyCount,
            color: successBackgroundModerate,
        },
        {
            label: t`Not ready`,
            value: notReadyCount,
            color: criticalBackgroundStrongInitial,
        },
    ].filter((slice) => slice.value > 0);
};
