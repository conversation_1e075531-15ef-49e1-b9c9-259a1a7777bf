import type { DatatableProps } from '@cosmos/components/datatable';
import type { RequirementListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { FrameworkRequirementActionsCellComponent } from '../lib/components/framework-requirement-actions-cell.component';
import { FrameworkRequirementScopeCellComponent } from '../lib/components/framework-requirement-scope-cell.component';
import { FrameworkRequirementStatusCellComponent } from '../lib/components/framework-requirement-status-cell.component';
import { FrameworkDetailsRequirementListControlsCell } from '../lib/framework-details-requirement-list-controls-cell';

const COLUMN_SIZE = 45;

export const getFrameworksDetailsColumns =
    (): DatatableProps<RequirementListResponseDto>['columns'] => [
        {
            accessorKey: 'isReady',
            header: t`Actions`,
            isActionColumn: true,
            enableSorting: false,
            id: 'row-actions',
            meta: {
                shouldIgnoreRowClick: true,
            },
            size: COLUMN_SIZE,
            cell: FrameworkRequirementActionsCellComponent,
        },
        {
            accessorKey: 'isReady',
            header: t`Status`,
            id: 'is-ready',
            enableSorting: false,
            size: COLUMN_SIZE,
            cell: FrameworkRequirementStatusCellComponent,
        },
        {
            accessorKey: 'name',
            header: t`Code`,
            id: 'name',
            enableSorting: false,
            minSize: COLUMN_SIZE,
        },
        {
            header: t`Title`,
            id: 'description',
            accessorKey: 'description',
            enableSorting: false,
        },
        {
            accessorKey: 'controls',
            header: t`Controls`,
            id: 'controls',
            enableSorting: false,
            cell: FrameworkDetailsRequirementListControlsCell,
        },
        {
            accessorKey: 'name',
            header: t`Scope`,
            id: 'scope',
            enableSorting: false,
            cell: FrameworkRequirementScopeCellComponent,
        },
    ];
