import {
    type AddArtifactModalModeType,
    openAddArtifactModal,
} from '@components/evidence-library';
import { sharedConnectionsController } from '@controllers/connections';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { LibraryDocumentVersionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { EvidenceDetailsModel } from '@models/evidence-library-details';
import { getAddArtifactOptions } from '../helpers/get-add-artifact-options';

export class EvidenceDetailsCurrentArtifactsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tableActions(): DatatableProps<LibraryDocumentVersionResponseDto>['tableActions'] {
        const { allConfiguredConnections, isLoading: isConnectionsLoading } =
            sharedConnectionsController;
        const { currentWorkspace } = sharedWorkspacesController;
        const { evidenceDetails } = new EvidenceDetailsModel();

        const hasJiraConnection = allConfiguredConnections.some(
            (connection) =>
                connection.clientType === 'JIRA' &&
                connection.workspaces.some(
                    (workspace) => workspace.id === currentWorkspace?.id,
                ),
        );

        const showTicketProviderOption =
            !isConnectionsLoading && hasJiraConnection;
        const optionsToDisplay = getAddArtifactOptions(
            showTicketProviderOption,
        );

        return [
            {
                actionType: 'dropdown',
                id: 'add-artifact',
                typeProps: {
                    label: t`Add artifact`,
                    onSelectGlobalOverride: (option) => {
                        openAddArtifactModal(
                            option.id as AddArtifactModalModeType,
                            evidenceDetails,
                        );
                    },
                    items: optionsToDisplay,
                    endIconName: 'ChevronDown',
                },
            },
        ];
    }
}

export const sharedEvidenceDetailsCurrentArtifactsModel =
    new EvidenceDetailsCurrentArtifactsModel();
