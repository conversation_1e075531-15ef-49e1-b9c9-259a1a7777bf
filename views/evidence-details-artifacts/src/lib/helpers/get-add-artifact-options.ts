import type { SchemaDropdownProps } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';

export const getAddArtifactOptions = (
    showTicketProviderOption: boolean,
): SchemaDropdownProps['items'] => [
    {
        id: 'URL',
        label: t`URL`,
    },
    ...(showTicketProviderOption
        ? [
              {
                  id: 'TICKET_PROVIDER',
                  label: t`Ticket provider`,
              },
          ]
        : []),
    {
        id: 'FILE',
        label: t`File`,
    },
];
