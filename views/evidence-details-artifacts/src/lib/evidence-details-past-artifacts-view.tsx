import { sharedEvidenceDetailsArtifactsController } from '@controllers/evidence-library';
import { Datatable } from '@cosmos/components/datatable';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedEvidenceDetailsArtifactsModel } from '@models/evidence-library-details';
import { EVIDENCE_DETAILS_ARTIFACTS_TABLE_COLUMNS } from './constants/evidence-details-artifacts-table-columns.constant';

export const EvidenceDetailsPastArtifactsView = observer(
    (): React.JSX.Element => {
        const {
            evidenceDetailsArtifacts,
            isLoading,
            total,
            loadArtifactsPage,
        } = sharedEvidenceDetailsArtifactsController;
        const { filters, bulkActions, handleRowSelection } =
            sharedEvidenceDetailsArtifactsModel;
        const { hasWriteEvidenceLibraryPermission } = sharedFeatureAccessModel;

        return (
            <Datatable
                isRowSelectionEnabled={hasWriteEvidenceLibraryPermission}
                isLoading={isLoading}
                tableId="datatable-evidence-details-past-artifacts"
                total={total}
                data={evidenceDetailsArtifacts}
                columns={EVIDENCE_DETAILS_ARTIFACTS_TABLE_COLUMNS}
                data-testid="EvidenceDetailsPastArtifactsView"
                data-id="48FDfCWc"
                filterProps={filters}
                bulkActionDropdownItems={bulkActions}
                emptyStateProps={{
                    illustrationName: 'Warning',
                    title: t`Past artifacts`,
                    description: t`No artifacts were found.`,
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t`Search by artifact name.`,
                }}
                onRowSelection={handleRowSelection}
                onFetchData={(params) => {
                    loadArtifactsPage(params, { isCurrent: false });
                }}
            />
        );
    },
);
