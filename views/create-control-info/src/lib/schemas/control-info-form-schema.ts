import { isError } from 'lodash-es';
import { z } from 'zod';
import { sharedControlsController } from '@controllers/controls-owners-candidates';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { action, toJS } from '@globals/mobx';
import {
    type ControlInfoValues,
    sharedControlInfoFormModel,
} from '@models/controls';
import type { FormSchema } from '@ui/forms';

export const getControlInfoFormSchema = (
    storedValues: ControlInfoValues,
): FormSchema => ({
    name: {
        type: 'text',
        label: t`Name`,
        isOptional: false,
        validator: z
            .string()
            .min(1, { message: t`Name is required` })
            .refine(
                async (value) => {
                    if (!value.trim()) {
                        return true;
                    }

                    try {
                        const exists = await action(async () => {
                            return sharedControlInfoFormModel.checkControlNameExists(
                                value,
                            );
                        })();

                        return !exists;
                    } catch (error) {
                        snackbarController.addSnackbar({
                            id: 'control-name-validation-error',
                            props: {
                                title: t`Error validating control name`,
                                description: isError(error)
                                    ? error.message
                                    : t`An unexpected error occurred`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return true;
                    }
                },
                {
                    message: t`Control name already exists, please choose another control name.`,
                },
            ),
        initialValue: storedValues.name || '',
    },
    code: {
        type: 'text',
        label: t`Code`,
        isOptional: false,
        validator: z
            .string()
            .min(1, { message: t`Code is required` })
            .refine(
                async (value) => {
                    if (!value.trim()) {
                        return true;
                    }

                    try {
                        const exists = await action(async () => {
                            return sharedControlInfoFormModel.checkControlCodeExists(
                                value,
                            );
                        })();

                        return !exists;
                    } catch (error) {
                        snackbarController.addSnackbar({
                            id: 'control-code-validation-error',
                            props: {
                                title: t`Error validating control code`,
                                description: isError(error)
                                    ? error.message
                                    : t`An unexpected error occurred`,
                                severity: 'critical',
                                closeButtonAriaLabel: t`Close`,
                            },
                        });

                        return true;
                    }
                },
                {
                    message: t`Control code already exists, please choose another code.`,
                },
            ),
        initialValue: storedValues.code || '',
        helpText: t`Ex: DRA-XXX`,
    },
    owner: {
        type: 'combobox',
        label: t`Owner`,
        isOptional: true,
        isMultiSelect: true,
        helpText: t`You can add more than one owner`,
        hasMore: toJS(sharedControlsController.hasMore),
        loaderLabel: t`Loading...`,
        removeAllSelectedItemsLabel: t`Remove all owners`,
        isLoading: toJS(sharedControlsController.isLoading),
        getRemoveIndividualSelectedItemClickLabel: ({ itemLabel }) =>
            t`Remove ${itemLabel}`,
        getSearchEmptyState: () => t`No results found`,
        options: toJS(sharedControlsController.accumulatedData),
        onFetchOptions: sharedControlsController.loadControlOwnersCandidates,
        initialValue: storedValues.owner ?? [],
        placeholder: t`Search by name`,
    },
    description: {
        type: 'textarea',
        label: t`Description`,
        isOptional: false,
        validator: z.string().min(1, { message: t`Description is required` }),
        initialValue: storedValues.description || '',
        placeholder: t`Explains the purpose of this control`,
        maxCharacters: 400,
    },
    question: {
        type: 'textarea',
        label: t`Question`,
        isOptional: true,
        initialValue: storedValues.question || '',
        placeholder: t`Asks what the control needs to accomplish`,
        maxCharacters: 400,
    },
    activity: {
        type: 'textarea',
        label: t`Activities`,
        isOptional: true,
        initialValue: storedValues.activity || '',
        placeholder: t`Answers the control question and outlines what the control accomplishes`,
        maxCharacters: 400,
    },
});
