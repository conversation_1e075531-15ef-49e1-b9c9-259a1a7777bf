import { isEmpty, size } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import { modalController } from '@controllers/modal';
import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedMapAutomatedTestsModel } from '@models/controls';

const MAP_AUTOMATED_TESTS_MODAL_ID = 'map-automated-tests-modal';
const FORM_ID = 'map-automated-tests-form';

const MapAutomatedTestsModal = observer((): React.JSX.Element => {
    const currentSelectedMonitors = useMemo(() => {
        return sharedMapAutomatedTestsModel.getCurrentSelectedMonitors();
    }, []);

    const [selectedMonitors, setSelectedMonitors] = useState<
        ListBoxItemData[] | ListBoxItemData
    >(currentSelectedMonitors);

    const handleClose = useCallback(() => {
        modalController.closeModal(MAP_AUTOMATED_TESTS_MODAL_ID);
    }, []);

    const handleSave = useCallback(() => {
        sharedMonitorsInfiniteController.clearSelectedMonitors();

        if (!isEmpty(selectedMonitors)) {
            sharedMonitorsInfiniteController.addSelectedMonitors(
                Array.isArray(selectedMonitors)
                    ? selectedMonitors
                    : [selectedMonitors],
            );
        }

        handleClose();
    }, [selectedMonitors, handleClose]);

    const handleGetSearchEmptyState = () => {
        return (
            <Text
                type="body"
                size="200"
                colorScheme="neutral"
                align="center"
                data-id="uStWbSZD"
                data-testid="handleGetSearchEmptyState"
            >
                <Trans>No tests found matching your search criteria.</Trans>
            </Text>
        );
    };

    const handleOnChange = useCallback(
        (selected: ListBoxItemData[] | ListBoxItemData) => {
            setSelectedMonitors(selected);
        },
        [],
    );

    const handleFetchOptions = useCallback(
        ({
            search,
            increasePage,
        }: {
            search?: string;
            increasePage?: boolean;
        }) => {
            if (increasePage) {
                sharedMonitorsInfiniteController.loadNextPage({
                    search,
                });
            } else {
                sharedMonitorsInfiniteController.search(search || '');
            }
        },
        [],
    );

    return (
        <>
            <Modal.Header
                title={t`Map automated test`}
                closeButtonAriaLabel={t`Close map automated tests modal`}
                description={(() => {
                    const count = size(selectedMonitors);

                    return count === 1
                        ? t`1 Test selected`
                        : t`${count} Tests selected`;
                })()}
                onClose={handleClose}
            />
            <Modal.Body>
                <Stack direction="column" gap="lg">
                    <Box>
                        <ComboboxField
                            isMultiSelect
                            label={t`Search test`}
                            formId={FORM_ID}
                            getSearchEmptyState={handleGetSearchEmptyState}
                            loaderLabel={t`Loading...`}
                            name="automatedTests"
                            removeAllSelectedItemsLabel={t`Clear all`}
                            placeholder={t`Search by test name...`}
                            data-id="map-automated-tests-combobox"
                            defaultSelectedOptions={currentSelectedMonitors}
                            options={
                                sharedMonitorsInfiniteController.monitorsComboboxOptions
                            }
                            isLoading={
                                sharedMonitorsInfiniteController.isLoading
                            }
                            hasMore={
                                sharedMonitorsInfiniteController.hasNextPage
                            }
                            getRemoveIndividualSelectedItemClickLabel={({
                                itemLabel,
                            }) => t`Remove ${itemLabel}`}
                            onChange={handleOnChange}
                            onFetchOptions={handleFetchOptions}
                        />
                    </Box>
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        onClick: handleSave,
                    },
                ]}
            />
        </>
    );
});

export const openMapAutomatedTestModal = (): void => {
    action(() => {
        sharedMonitorsInfiniteController.loadInfiniteMonitors();
    })();

    modalController.openModal({
        id: MAP_AUTOMATED_TESTS_MODAL_ID,
        content: () => <MapAutomatedTestsModal data-id="u1K60di4" />,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};
