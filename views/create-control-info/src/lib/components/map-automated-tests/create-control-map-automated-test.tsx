import { isEmpty } from 'lodash-es';
import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import { ActionStack } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getMapAutomatedTestActions } from '../../helpers/get-map-automated-test-actions.helper';

export const CreateControlInfoWizardMapAutomatedTestStep = observer(
    (): React.JSX.Element => {
        const { selectedMonitors } = sharedMonitorsInfiniteController;

        return (
            <Stack
                flexGrow="4"
                gap="6x"
                direction="column"
                data-testid="CreateControlInfoWizardCreateStep"
                data-id="NnL2m3_T"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Map control{"'"}s tests</Trans>
                        </Text>
                    </Box>
                    <Stack direction="row" justify="between">
                        <Stack
                            direction="row"
                            gap="md"
                            align="center"
                            pt="4x"
                            pb="4x"
                        >
                            <Text type="title" size="100">
                                <Trans>Mapped tests</Trans>
                            </Text>
                            <Metadata
                                label={selectedMonitors.length.toString()}
                                type="number"
                            />
                        </Stack>

                        <Stack>
                            <ActionStack
                                gap="3x"
                                data-id="map-tests-action-stack"
                                actions={getMapAutomatedTestActions(
                                    selectedMonitors,
                                )}
                            />
                        </Stack>
                    </Stack>

                    {isEmpty(selectedMonitors) ? (
                        <Box p="4x" data-id="empty-tests-message">
                            <Text type="body" size="200" colorScheme="neutral">
                                <Trans>
                                    No tests mapped yet. Use the button above to
                                    add them.
                                </Trans>
                            </Text>
                        </Box>
                    ) : (
                        selectedMonitors.map((monitor) => (
                            <Box
                                key={monitor.id}
                                borderColor="neutralBorderFaded"
                                borderWidth="borderWidthSm"
                                borderRadius="borderRadiusLg"
                                mb="2x"
                                data-id="7I9C1Gwg"
                                p="lg"
                            >
                                <Stack
                                    direction="row"
                                    justify="between"
                                    p="2x"
                                    data-id="FkZppluK"
                                >
                                    <Metadata
                                        colorScheme="success"
                                        type="status"
                                        label={monitor.checkResultStatus || ''}
                                    />
                                    <Text type="title" size="200">
                                        <Truncation>{monitor.name}</Truncation>
                                    </Text>
                                    <Button
                                        label={t`Unlink`}
                                        colorScheme="danger"
                                        level="tertiary"
                                        size="sm"
                                        onClick={() => {
                                            sharedMonitorsInfiniteController.removeSelectedMonitor(
                                                monitor.id,
                                            );
                                        }}
                                    />
                                </Stack>
                            </Box>
                        ))
                    )}
                </Stack>
            </Stack>
        );
    },
);
