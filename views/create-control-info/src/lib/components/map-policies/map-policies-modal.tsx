import { size } from 'lodash-es';
import { useCallback, useState } from 'react';
import { modalController } from '@controllers/modal';
import {
    MAP_POLICIES_MODAL_ID,
    sharedPoliciesLibraryInfiniteListController,
} from '@controllers/policies';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedMapPoliciesModel } from '@models/controls';

const FORM_ID = 'map-policies-form';

const MapPoliciesModal = observer((): React.JSX.Element => {
    const { policiesListAsItems, isLoading, hasNextPage } =
        sharedPoliciesLibraryInfiniteListController;

    const currentSelectedPolicies =
        sharedMapPoliciesModel.getCurrentSelectedPolicies();

    const [selectedPolicies, setSelectedPolicies] = useState<
        ListBoxItemData[] | ListBoxItemData
    >(currentSelectedPolicies);

    const handleClose = useCallback(() => {
        modalController.closeModal(MAP_POLICIES_MODAL_ID);
    }, []);

    const handleSave = useCallback(() => {
        sharedPoliciesLibraryInfiniteListController.saveSelectedPolicies(
            selectedPolicies,
        );
    }, [selectedPolicies]);

    const handleGetSearchEmptyState = () => {
        return (
            <Text
                type="body"
                size="200"
                colorScheme="neutral"
                align="center"
                data-id="emptyPoliciesSearch"
                data-testid="handleGetSearchEmptyState"
            >
                <Trans>No policies found matching your search criteria.</Trans>
            </Text>
        );
    };

    const handleOnChange = useCallback(
        (selected: ListBoxItemData[] | ListBoxItemData) => {
            setSelectedPolicies(selected);
        },
        [],
    );

    const handleFetchOptions = useCallback(
        (params: { search?: string; increasePage?: boolean }) => {
            sharedPoliciesLibraryInfiniteListController.fetchOptions(params);
        },
        [],
    );

    return (
        <>
            <Modal.Header
                title={t`Link Policies`}
                closeButtonAriaLabel={t`Close map policies modal`}
                description={(() => {
                    const count = size(selectedPolicies);

                    return count === 1
                        ? t`1 Policy selected`
                        : t`${count} Policies selected`;
                })()}
                onClose={handleClose}
            />
            <Modal.Body>
                <Stack direction="column" gap="lg">
                    <Box>
                        <ComboboxField
                            isMultiSelect
                            label={t`Search policies`}
                            formId={FORM_ID}
                            getSearchEmptyState={handleGetSearchEmptyState}
                            loaderLabel={t`Loading...`}
                            name="map-policies"
                            removeAllSelectedItemsLabel={t`Clear all`}
                            placeholder={t`Search by policy name...`}
                            data-id="map-policies-combobox"
                            defaultSelectedOptions={currentSelectedPolicies}
                            options={policiesListAsItems}
                            isLoading={isLoading}
                            hasMore={hasNextPage}
                            getRemoveIndividualSelectedItemClickLabel={({
                                itemLabel,
                            }) => t`Remove ${itemLabel}`}
                            onChange={handleOnChange}
                            onFetchOptions={handleFetchOptions}
                        />
                    </Box>
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        onClick: handleSave,
                    },
                ]}
            />
        </>
    );
});

export const openMapPoliciesModal = (): void => {
    action(() => {
        sharedPoliciesLibraryInfiniteListController.load();
    })();

    modalController.openModal({
        id: MAP_POLICIES_MODAL_ID,
        content: () => <MapPoliciesModal data-id="MapPoliciesModal" />,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};
