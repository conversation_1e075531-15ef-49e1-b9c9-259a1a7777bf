import { isEmpty } from 'lodash-es';
import { sharedPoliciesLibraryInfiniteListController } from '@controllers/policies';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMapPoliciesModel } from '@models/controls';
import { AppLink } from '@ui/app-link';
import { openMapPoliciesModal } from './map-policies-modal';

export const CreateControlInfoWizardMapPoliciesStep = observer(
    (): React.JSX.Element => {
        return (
            <Stack
                gap="6x"
                direction="column"
                data-testid="CreateControlInfoWizardMapPoliciesStep"
                data-id="MapPoliciesStep"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Map policies</Trans>
                        </Text>
                    </Box>
                    <Stack justify="between" pt="4x" pb="4x">
                        <Stack align="center" gap="2x">
                            <Text type="title" size="100">
                                <Trans>Mapped policies</Trans>
                            </Text>
                            <Metadata
                                label={sharedMapPoliciesModel.selectedPolicies.length.toString()}
                                type="number"
                            />
                        </Stack>
                        <Stack gap="3x">
                            {!isEmpty(
                                sharedMapPoliciesModel.selectedPolicies,
                            ) && (
                                <Button
                                    colorScheme="danger"
                                    label={t`Remove all`}
                                    level="tertiary"
                                    size="sm"
                                    onClick={() => {
                                        sharedPoliciesLibraryInfiniteListController.clearAllPolicies();
                                    }}
                                />
                            )}
                            <Button
                                label={t`Map policies`}
                                level="secondary"
                                size="sm"
                                onClick={openMapPoliciesModal}
                            />
                        </Stack>
                    </Stack>
                    {isEmpty(sharedMapPoliciesModel.selectedPolicies) ? (
                        <Box p="4x" data-id="empty-policies-message">
                            <Text type="body" size="200" colorScheme="neutral">
                                <Trans>
                                    No policies mapped yet. Use the button above
                                    to add them.
                                </Trans>
                            </Text>
                        </Box>
                    ) : (
                        <Stack direction="column" gap="2x">
                            {sharedMapPoliciesModel.selectedPolicies.map(
                                (policy) => {
                                    const policyId = policy.id;
                                    const isPublished =
                                        sharedMapPoliciesModel.isPolicyPublished(
                                            policy,
                                        );

                                    return (
                                        <Box
                                            key={policyId}
                                            borderColor="neutralBorderFaded"
                                            borderWidth="borderWidthSm"
                                            borderRadius="borderRadiusLg"
                                            p="lg"
                                            data-id={`policy-item-${policyId}`}
                                        >
                                            <Stack
                                                direction="row"
                                                justify="between"
                                                align="center"
                                            >
                                                <Stack
                                                    direction="column"
                                                    gap="1x"
                                                >
                                                    <Text
                                                        type="title"
                                                        size="200"
                                                    >
                                                        <Truncation>
                                                            {policy.name ||
                                                                'Unnamed Policy'}
                                                        </Truncation>
                                                    </Text>
                                                </Stack>
                                                <Stack direction="row" gap="2x">
                                                    {isPublished && (
                                                        <AppLink
                                                            isExternal
                                                            data-id={`policy-link-${policyId}`}
                                                            href={sharedPoliciesLibraryInfiniteListController.getPolicyUrl(
                                                                policyId,
                                                            )}
                                                        >
                                                            <Button
                                                                isIconOnly
                                                                label={t`External Link`}
                                                                size="sm"
                                                                colorScheme="primary"
                                                                level="tertiary"
                                                                startIconName="LinkOut"
                                                            />
                                                        </AppLink>
                                                    )}
                                                    <Button
                                                        label={t`Remove`}
                                                        level="tertiary"
                                                        size="sm"
                                                        colorScheme="danger"
                                                        onClick={() => {
                                                            sharedPoliciesLibraryInfiniteListController.removePolicy(
                                                                policyId,
                                                            );
                                                        }}
                                                    />
                                                </Stack>
                                            </Stack>
                                        </Box>
                                    );
                                },
                            )}
                        </Stack>
                    )}
                </Stack>
            </Stack>
        );
    },
);
