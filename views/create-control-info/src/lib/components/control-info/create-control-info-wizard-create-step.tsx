import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedControlInfoFormModel } from '@models/controls';
import { Form } from '@ui/forms';
import { getControlInfoFormSchema } from '../../schemas/control-info-form-schema';

const FORM_ID = 'create-control-info';

interface Props {
    formRef: React.ForwardedRef<HTMLFormElement>;
}

export const CreateControlInfoWizardCreateStep = observer(
    ({ formRef }: Props): React.JSX.Element => {
        const { handleSubmit, storedValues } = sharedControlInfoFormModel;
        const formSchema = getControlInfoFormSchema(storedValues);

        return (
            <Stack
                flexGrow="4"
                gap="6x"
                direction="column"
                data-testid="CreateControlInfoWizardCreateStep"
                data-id="NnL2m3_T"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Add your control basic information</Trans>
                        </Text>
                    </Box>
                </Stack>
                <Form
                    hasExternalSubmitButton
                    data-id="create-control-info-form"
                    ref={formRef}
                    formId={FORM_ID}
                    schema={formSchema}
                    onSubmit={handleSubmit}
                />
            </Stack>
        );
    },
);
