import { isEmpty, uniqBy } from 'lodash-es';
import { sharedEvidenceLibraryInfiniteListController } from '@controllers/evidence-library';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { TagGroup } from '@cosmos/components/tag-group';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMapEvidenceModel } from '@models/controls';
import { getEvidenceStatusMetadataNoIcon } from '../../helpers/get-evidence-status-metadata-no-icon.helper';
import { openMapEvidenceModal } from './map-evidence-modal';

export const CreateControlInfoWizardMapEvidenceStep = observer(
    (): React.JSX.Element => {
        return (
            <Stack
                flexGrow="4"
                gap="6x"
                direction="column"
                data-testid="CreateControlInfoWizardCreateStep"
                data-id="NnL2m3_T"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Add from Evidence Library</Trans>
                        </Text>
                    </Box>
                    <Stack justify="between" pt="4x" pb="4x">
                        <Stack align="center" gap="2x">
                            <Text type="title" size="100">
                                <Trans>Added evidence</Trans>
                            </Text>
                            <Metadata
                                label={sharedMapEvidenceModel.selectedEvidence.length.toString()}
                                type="number"
                            />
                        </Stack>
                        <Stack gap="3x">
                            {!isEmpty(
                                sharedMapEvidenceModel.selectedEvidence,
                            ) && (
                                <Button
                                    colorScheme="danger"
                                    label={t`Remove all`}
                                    level="tertiary"
                                    size="sm"
                                    onClick={() => {
                                        sharedEvidenceLibraryInfiniteListController.clearAllEvidence();
                                    }}
                                />
                            )}
                            <Button
                                label={t`Map evidence`}
                                level="secondary"
                                size="sm"
                                onClick={openMapEvidenceModal}
                            />
                        </Stack>
                    </Stack>

                    {isEmpty(sharedMapEvidenceModel.selectedEvidence) ? (
                        <Box p="4x" data-id="empty-evidence-message">
                            <Text type="body" size="200" colorScheme="neutral">
                                <Trans>
                                    No evidences mapped yet. Use the button
                                    above to add them.
                                </Trans>
                            </Text>
                        </Box>
                    ) : (
                        <Stack direction="column" gap="2x">
                            {sharedMapEvidenceModel.selectedEvidence.map(
                                (evidence) => {
                                    const tagMetadata =
                                        getEvidenceStatusMetadataNoIcon(
                                            evidence,
                                        );
                                    const { linkedWorkspaces, id } = evidence;

                                    return (
                                        <Box
                                            key={id}
                                            borderColor="neutralBorderFaded"
                                            borderWidth="borderWidthSm"
                                            borderRadius="borderRadiusLg"
                                            p="lg"
                                            data-id={`evidence-item-${id}`}
                                        >
                                            <Stack
                                                direction="row"
                                                justify="between"
                                                align="center"
                                            >
                                                <Stack
                                                    direction="column"
                                                    gap="1x"
                                                >
                                                    <Text
                                                        type="title"
                                                        size="200"
                                                    >
                                                        <Truncation>
                                                            {evidence.name ||
                                                                'Unnamed Evidence'}
                                                        </Truncation>
                                                    </Text>
                                                    {!isEmpty(
                                                        linkedWorkspaces,
                                                    ) && (
                                                        <TagGroup
                                                            maxVisibleTags={2}
                                                            data-id="evidence-workspaces"
                                                        >
                                                            {uniqBy(
                                                                linkedWorkspaces,
                                                                'id',
                                                            ).map(
                                                                (workspace) => (
                                                                    <Metadata
                                                                        colorScheme="neutral"
                                                                        type="tag"
                                                                        data-id={`workspace-${workspace.id}`}
                                                                        key={
                                                                            workspace.id
                                                                        }
                                                                        label={
                                                                            workspace.name
                                                                        }
                                                                    />
                                                                ),
                                                            )}
                                                        </TagGroup>
                                                    )}
                                                </Stack>
                                                <Stack direction="row" gap="2x">
                                                    <Metadata
                                                        type="status"
                                                        data-testid="EvidenceStatusMetadata"
                                                        data-id="EvidenceStatusMetadata"
                                                        label={
                                                            tagMetadata.label
                                                        }
                                                        colorScheme={
                                                            tagMetadata.colorScheme
                                                        }
                                                    />
                                                    <Button
                                                        label={t`Unlink`}
                                                        level="tertiary"
                                                        size="sm"
                                                        colorScheme="danger"
                                                        onClick={() => {
                                                            sharedEvidenceLibraryInfiniteListController.removeEvidence(
                                                                id,
                                                            );
                                                        }}
                                                    />
                                                </Stack>
                                            </Stack>
                                        </Box>
                                    );
                                },
                            )}
                        </Stack>
                    )}
                </Stack>
            </Stack>
        );
    },
);
