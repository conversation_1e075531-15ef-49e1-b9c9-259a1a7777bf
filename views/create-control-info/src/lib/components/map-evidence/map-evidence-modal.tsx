import { isEmpty, size } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import { sharedEvidenceLibraryInfiniteListController } from '@controllers/evidence-library';
import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { EvidenceResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import {
    MAP_EVIDENCE_MODAL_ID,
    sharedMapEvidenceModel,
} from '@models/controls';
import { getEvidenceStatusMetadataNoIcon } from '../../helpers/get-evidence-status-metadata-no-icon.helper';

const FORM_ID = 'map-evidence-form';

interface EvidenceItem extends ListBoxItemData {
    creationDate: string;
    evidenceData: EvidenceResponseDto;
}

const MapEvidenceModal = observer((): React.JSX.Element => {
    const { selectedEvidence } = sharedMapEvidenceModel;
    const { evidenceList, isLoading, hasNextPage } =
        sharedEvidenceLibraryInfiniteListController;

    const selectedEvidenceFromController = selectedEvidence;

    const currentSelectedEvidence = useMemo(() => {
        return selectedEvidenceFromController.map((evidence): EvidenceItem => {
            const tagMetadata = getEvidenceStatusMetadataNoIcon(evidence);

            return {
                id: `evidence-${evidence.id}`,
                value: String(evidence.id),
                label: evidence.name || 'Unnamed Evidence',
                description: `Creation date: ${formatDate('field', evidence.versions[0]?.filedAt ?? evidence.createdAt)}`,
                tag: tagMetadata,
                creationDate: `Creation date: ${formatDate('field', evidence.versions[0]?.filedAt ?? evidence.createdAt)}`,
                evidenceData: evidence,
            };
        });
    }, [selectedEvidenceFromController]);

    const [localSelectedEvidence, setLocalSelectedEvidence] = useState<
        ListBoxItemData[] | ListBoxItemData
    >(currentSelectedEvidence);
    const [validationError, setValidationError] = useState<string | null>(null);

    const evidenceOptions = useMemo(
        () =>
            evidenceList.map((evidence): EvidenceItem => {
                const tagMetadata = getEvidenceStatusMetadataNoIcon(evidence);

                return {
                    id: `evidence-${evidence.id}`,
                    value: String(evidence.id),
                    label: evidence.name || 'Unnamed Evidence',
                    description: `Creation date: ${formatDate('field', evidence.versions[0].filedAt ?? evidence.createdAt)}`,
                    tag: tagMetadata,
                    creationDate: `Creation date: ${formatDate('field', evidence.versions[0].filedAt ?? evidence.createdAt)}`,
                    evidenceData: evidence,
                };
            }),
        [evidenceList],
    );

    const handleClose = useCallback(() => {
        modalController.closeModal(MAP_EVIDENCE_MODAL_ID);
    }, []);

    const handleSave = useCallback(() => {
        sharedEvidenceLibraryInfiniteListController.clearAllEvidence();

        if (
            !(
                Array.isArray(localSelectedEvidence) &&
                isEmpty(localSelectedEvidence)
            )
        ) {
            const evidenceToAdd = Array.isArray(localSelectedEvidence)
                ? localSelectedEvidence.map(
                      (item) => (item as EvidenceItem).evidenceData,
                  )
                : [(localSelectedEvidence as EvidenceItem).evidenceData];

            sharedEvidenceLibraryInfiniteListController.addSelectedEvidence(
                evidenceToAdd,
            );
        }

        handleClose();
    }, [localSelectedEvidence, handleClose]);

    const handleGetSearchEmptyState = () => {
        return (
            <Text
                type="body"
                size="200"
                colorScheme="neutral"
                align="center"
                data-id="emptyEvidenceSearch"
                data-testid="handleGetSearchEmptyState"
            >
                <Trans>No evidence found matching your search criteria.</Trans>
            </Text>
        );
    };

    const handleOnChange = useCallback(
        (selected: ListBoxItemData[] | ListBoxItemData) => {
            setLocalSelectedEvidence(selected);
            if (
                validationError &&
                Array.isArray(selected) &&
                !isEmpty(selected)
            ) {
                setValidationError(null);
            }
        },
        [validationError],
    );

    const handleFetchOptions = useCallback(
        ({
            search,
            increasePage,
        }: {
            search?: string;
            increasePage?: boolean;
        }) => {
            sharedEvidenceLibraryInfiniteListController.fetchOptions({
                search,
                increasePage,
            });
        },
        [],
    );

    return (
        <>
            <Modal.Header
                title={t`Add from Evidence Library`}
                closeButtonAriaLabel="Close map evidence modal"
                description={(() => {
                    const count = size(localSelectedEvidence);

                    return count === 1
                        ? t`1 Evidence selected`
                        : t`${count} Evidences selected`;
                })()}
                onClose={handleClose}
            />
            <Modal.Body>
                <Stack direction="column" gap="lg">
                    <Box>
                        <ComboboxField
                            isMultiSelect
                            label={t`Search evidence`}
                            formId={FORM_ID}
                            getSearchEmptyState={handleGetSearchEmptyState}
                            loaderLabel={t`Loading...`}
                            name="evidence"
                            removeAllSelectedItemsLabel={t`Clear all`}
                            placeholder={t`Search by evidence name...`}
                            data-id="map-evidence-combobox"
                            defaultSelectedOptions={currentSelectedEvidence}
                            options={evidenceOptions}
                            isLoading={isLoading}
                            hasMore={hasNextPage}
                            getRemoveIndividualSelectedItemClickLabel={({
                                itemLabel,
                            }) => t`Remove ${itemLabel}`}
                            onChange={handleOnChange}
                            onFetchOptions={handleFetchOptions}
                        />
                    </Box>
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Close`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Confirm`,
                        level: 'primary',
                        onClick: handleSave,
                    },
                ]}
            />
        </>
    );
});

export const openMapEvidenceModal = (): void => {
    action(() => {
        sharedEvidenceLibraryInfiniteListController.load();
    })();

    modalController.openModal({
        id: MAP_EVIDENCE_MODAL_ID,
        content: () => <MapEvidenceModal data-id="OiCVGTvi" />,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};
