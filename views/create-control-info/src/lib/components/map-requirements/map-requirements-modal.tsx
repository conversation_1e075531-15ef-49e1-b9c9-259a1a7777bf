import { size } from 'lodash-es';
import { useCallback, useState } from 'react';
import { modalController } from '@controllers/modal';
import {
    sharedMapRequirementsController,
    sharedRequirementsInfiniteController,
} from '@controllers/requirements';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedMapRequirementsModel } from '@models/controls';

const MAP_REQUIREMENTS_MODAL_ID = 'map-requirements-modal';
const FORM_ID = 'map-requirements-form-id';

interface MapRequirementsModalProps {
    framework: FrameworkResponseDto;
}

const MapRequirementsModal = observer(
    ({ framework }: MapRequirementsModalProps): React.JSX.Element => {
        const { id, name } = framework;
        const { isLoading, hasNextPage } = sharedRequirementsInfiniteController;

        const currentSelectedOptions =
            sharedMapRequirementsModel.getFrameworkSelectedOptions(id);
        const formatOptions = sharedMapRequirementsModel.getFormattedOptions();

        const [selectedRequirements, setSelectedRequirements] = useState<
            ListBoxItemData[]
        >(currentSelectedOptions);

        const handleGetSearchEmptyState = () => {
            return (
                <Text
                    type="body"
                    size="200"
                    colorScheme="neutral"
                    align="center"
                    data-id="uBw3lvW_"
                    data-testid="handleGetSearchEmptyState"
                >
                    <Trans>
                        No requirements found matching your search criteria
                    </Trans>
                </Text>
            );
        };

        const handleFetchOptions = useCallback(
            ({
                search,
                increasePage,
            }: {
                search?: string;
                increasePage?: boolean;
            }) => {
                action(() => {
                    if (increasePage) {
                        sharedRequirementsInfiniteController.loadNextPage();
                    } else {
                        sharedRequirementsInfiniteController.search(
                            search || '',
                        );
                    }
                })();
            },
            [],
        );

        const handleMapRequirements = useCallback(() => {
            sharedMapRequirementsController.mapRequirementsToFramework(
                id,
                selectedRequirements,
                MAP_REQUIREMENTS_MODAL_ID,
            );
        }, [id, selectedRequirements]);

        return (
            <>
                <Modal.Header
                    title={t`Map ${name} requirements`}
                    closeButtonAriaLabel={t`Close map requirements modal`}
                    description={(() => {
                        const count = size(selectedRequirements);

                        return count === 1
                            ? t`1 Requirement selected`
                            : t`${count} Requirements selected`;
                    })()}
                    onClose={() => {
                        modalController.closeModal(MAP_REQUIREMENTS_MODAL_ID);
                    }}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Box>
                            <ComboboxField
                                isMultiSelect
                                label={t`Search requirement`}
                                formId={FORM_ID}
                                getSearchEmptyState={handleGetSearchEmptyState}
                                loaderLabel={t`Loading...`}
                                name="requirements"
                                removeAllSelectedItemsLabel={t`Clear all`}
                                placeholder={t`Search by requirement name...`}
                                isLoading={isLoading}
                                options={formatOptions}
                                defaultSelectedOptions={currentSelectedOptions}
                                hasMore={hasNextPage}
                                getRemoveIndividualSelectedItemClickLabel={({
                                    itemLabel,
                                }) => t`Remove ${itemLabel}`}
                                onFetchOptions={handleFetchOptions}
                                onChange={(selectedItems) => {
                                    const items = Array.isArray(selectedItems)
                                        ? selectedItems
                                        : [selectedItems];

                                    setSelectedRequirements(items);
                                }}
                            />
                        </Box>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            onClick: () => {
                                modalController.closeModal(
                                    MAP_REQUIREMENTS_MODAL_ID,
                                );
                            },
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            onClick: handleMapRequirements,
                        },
                    ]}
                />
            </>
        );
    },
);

export const openMapRequirementsModal = (
    framework: FrameworkResponseDto,
): void => {
    action(() => {
        sharedRequirementsInfiniteController.loadRequirements(
            framework.id,
            framework.slug,
        );
    })();

    modalController.openModal({
        id: MAP_REQUIREMENTS_MODAL_ID,
        content: () => (
            <MapRequirementsModal framework={framework} data-id="e8WpB5_I" />
        ),
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};
