import { sharedFrameworksController } from '@controllers/frameworks';
import { Accordion } from '@cosmos/components/accordion';
import { Box } from '@cosmos/components/box';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { getFrameworkBadge } from '@cosmos-lab/components/framework-badge';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedMapRequirementsModel } from '@models/controls';
import { MappedRequirements } from './mapped-requirements';

export const CreateControlInfoWizardMapRequirementStep = observer(
    (): React.JSX.Element => {
        const { productFrameworks } = sharedFrameworksController;

        return (
            <Stack
                direction="column"
                data-testid="CreateControlInfoWizardCreateStep"
                data-id="NnL2m3_T"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Map control{"'"}s requirements</Trans>
                        </Text>
                    </Box>

                    <Stack direction="column" p="lg" gap="lg">
                        {productFrameworks.map((framework) => {
                            const requirementsCount =
                                sharedMapRequirementsModel.getFrameworkMappedRequirements(
                                    framework.id,
                                ).length;

                            return (
                                <Accordion
                                    key={framework.id}
                                    title={framework.name}
                                    data-id={`${framework.name.toLowerCase().replaceAll(/\s+/g, '-')}-accordion`}
                                    body={
                                        <MappedRequirements
                                            framework={framework}
                                        />
                                    }
                                    supportingContent={
                                        <Stack
                                            direction="row"
                                            gap="md"
                                            align="center"
                                        >
                                            <Text type="body" size="100">
                                                <Trans>
                                                    Mapped Requirements
                                                </Trans>
                                            </Text>
                                            <Metadata
                                                label={requirementsCount.toString()}
                                                type="number"
                                                colorScheme="neutral"
                                            />
                                        </Stack>
                                    }
                                    iconSlot={{
                                        slotType: 'frameworkBadge',
                                        typeProps: {
                                            badgeName: getFrameworkBadge(
                                                framework.tag,
                                            ),
                                            colorScheme: 'primary',
                                            size: 'md',
                                        },
                                    }}
                                />
                            );
                        })}
                    </Stack>
                </Stack>
            </Stack>
        );
    },
);
