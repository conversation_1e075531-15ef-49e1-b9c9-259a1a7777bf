import { isEmpty } from 'lodash-es';
import { useCallback, useMemo } from 'react';
import { sharedRequirementsController } from '@controllers/requirements';
import { ActionStack } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { openMapRequirementsModal } from './map-requirements-modal';

interface MappedRequirementsProps {
    framework: FrameworkResponseDto;
}

export const MappedRequirements = observer(
    ({ framework }: MappedRequirementsProps): React.JSX.Element => {
        const { mappedRequirements = [] } = sharedRequirementsController;

        const frameworkRequirements = useMemo(() => {
            return mappedRequirements.filter(
                (req) => req.frameworkId === framework.id,
            );
        }, [mappedRequirements, framework.id]);

        const handleUnmapRequirement = useCallback(
            (requirementId: number) => {
                action(() => {
                    sharedRequirementsController.mappedRequirements =
                        mappedRequirements.filter(
                            (req) =>
                                !(
                                    req.id === requirementId &&
                                    req.frameworkId === framework.id
                                ),
                        );
                })();
            },
            [framework.id, mappedRequirements],
        );

        const handleUnmapAll = useCallback(() => {
            action(() => {
                sharedRequirementsController.mappedRequirements =
                    mappedRequirements.filter(
                        (req) => req.frameworkId !== framework.id,
                    );
            })();
        }, [framework.id, mappedRequirements]);

        const handleMapRequirements = useCallback(() => {
            openMapRequirementsModal(framework);
        }, [framework]);

        return (
            <Stack
                direction="column"
                gap="xl"
                data-testid="MappedRequirements"
                data-id="Q-OD2osl"
            >
                {isEmpty(frameworkRequirements) && (
                    <Text type="body" size="200" colorScheme="neutral">
                        <Trans>
                            No requirements mapped yet. Use the button below to
                            add them.
                        </Trans>
                    </Text>
                )}

                <ActionStack
                    gap="3x"
                    actions={
                        isEmpty(frameworkRequirements)
                            ? [
                                  {
                                      id: 'map-requirements',
                                      actionType: 'button',
                                      typeProps: {
                                          label: t`Map requirements`,
                                          level: 'secondary',
                                          size: 'sm',
                                          onClick: handleMapRequirements,
                                      },
                                  },
                              ]
                            : [
                                  {
                                      id: 'map-requirements',
                                      actionType: 'button',
                                      typeProps: {
                                          label: t`Map requirements`,
                                          level: 'secondary',
                                          size: 'sm',
                                          onClick: handleMapRequirements,
                                      },
                                  },
                                  {
                                      id: 'unmap-all',
                                      actionType: 'button',
                                      typeProps: {
                                          label: t`Unmap all`,
                                          colorScheme: 'danger',
                                          level: 'tertiary',
                                          size: 'sm',
                                          onClick: handleUnmapAll,
                                      },
                                  },
                              ]
                    }
                />

                {!isEmpty(frameworkRequirements) && (
                    <Stack gap={'md'} direction={'column'}>
                        {frameworkRequirements.map((requirement) => {
                            // Extract all needed values from the observable during the render phase
                            const requirementId = requirement.id;
                            const requirementName = requirement.name;
                            const requirementDescription =
                                requirement.description ||
                                'No description available';

                            return (
                                <Box
                                    key={requirementId}
                                    borderColor="neutralBorderFaded"
                                    borderWidth="borderWidthSm"
                                    borderRadius="borderRadiusLg"
                                    p="2xl"
                                    data-id="hfO2D9-w"
                                >
                                    <Stack
                                        direction="row"
                                        align="center"
                                        justify="between"
                                    >
                                        <Box>
                                            <Metadata
                                                label={requirementName}
                                                colorScheme="neutral"
                                                type="tag"
                                            />
                                        </Box>

                                        <Box>
                                            <Text
                                                type="title"
                                                size="200"
                                                align="left"
                                            >
                                                <Truncation
                                                    lineClamp={2}
                                                    mode="end"
                                                >
                                                    {requirementDescription}
                                                </Truncation>
                                            </Text>
                                        </Box>

                                        <Box minWidth="fit-content">
                                            <Button
                                                label={t`Unmap`}
                                                colorScheme="danger"
                                                level="tertiary"
                                                size="sm"
                                                onClick={() => {
                                                    handleUnmapRequirement(
                                                        requirementId,
                                                    );
                                                }}
                                            />
                                        </Box>
                                    </Stack>
                                </Box>
                            );
                        })}
                    </Stack>
                )}
            </Stack>
        );
    },
);
