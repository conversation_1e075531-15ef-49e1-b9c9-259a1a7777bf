import { isEmpty } from 'lodash-es';
import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import type {
    Action,
    BaseActionType,
    ButtonAction,
} from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { openMapAutomatedTestModal } from '../components/map-automated-tests/map-automated-test-modal';

/**
 * Gets the ActionStack for the map automated test component.
 * Only shows the "Remove all" button if there are selected monitors.
 * Otherwise, only shows the "Link tests" button.
 *
 * @param selectedMonitors - The list of selected monitors.
 * @returns An array of actions for the ActionStack component.
 */
export const getMapAutomatedTestActions = (
    selectedMonitors: { id: string }[],
): Action[] => {
    const actions: Action[] = [];

    if (!isEmpty(selectedMonitors)) {
        const removeAllAction: BaseActionType & ButtonAction = {
            id: 'remove-all-tests',
            actionType: 'button',
            typeProps: {
                colorScheme: 'danger',
                label: t`Remove all`,
                level: 'tertiary',
                size: 'sm',
                onClick: () => {
                    sharedMonitorsInfiniteController.clearSelectedMonitors();
                },
            },
        };

        actions.push(removeAllAction);
    }

    const linkTestsAction: BaseActionType & ButtonAction = {
        id: 'link-tests',
        actionType: 'button',
        typeProps: {
            label: t`Link tests`,
            level: 'secondary',
            size: 'sm',
            onClick: () => {
                openMapAutomatedTestModal();
            },
        },
    };

    actions.push(linkTestsAction);

    return actions;
};
