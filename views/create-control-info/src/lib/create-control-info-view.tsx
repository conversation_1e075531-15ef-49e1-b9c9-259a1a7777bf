import { useCallback, useMemo } from 'react';
import { sharedCreateControlController } from '@controllers/controls';
import { Wizard, type WizardProps } from '@cosmos-lab/components/wizard';
import { sharedEntitlementFlagController } from '@globals/entitlement-flag';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import { sharedControlInfoFormModel } from '@models/controls';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { CreateControlInfoWizardCreateStep } from './components/control-info/create-control-info-wizard-create-step';
import { CreateControlInfoWizardMapAutomatedTestStep } from './components/map-automated-tests/create-control-map-automated-test';
import { CreateControlInfoWizardMapEvidenceStep } from './components/map-evidence/create-control-map-evidence';
import { CreateControlInfoWizardMapPoliciesStep } from './components/map-policies/create-control-map-policies';
import { CreateControlInfoWizardMapRequirementStep } from './components/map-requirements/create-control-info-wizard-map-requirement';

export const CreateControlInfoView = observer((): React.JSX.Element => {
    const controlInfoForm = useFormSubmit();
    const navigate = useNavigate();
    const location = useLocation();

    const handleComplete = useCallback(async () => {
        const storedValues = action(
            () => sharedControlInfoFormModel.storedValues,
        )();

        const success = await action(() =>
            sharedCreateControlController.createControl(storedValues),
        )();

        if (success) {
            const parentPath = getParentRoute(location.pathname);

            navigate(parentPath);
        }
    }, [navigate, location.pathname]);

    const handleCancel = useCallback(() => {
        const parentPath = getParentRoute(location.pathname);

        navigate(parentPath);
    }, [navigate, location.pathname]);

    const wizardProps: WizardProps = useMemo(() => {
        const steps: WizardProps['steps'] = [
            {
                component: () => (
                    <CreateControlInfoWizardCreateStep
                        formRef={controlInfoForm.formRef}
                        data-id="oQY_Gr38"
                    />
                ),
                stepTitle: t`Control info`,
                isStepSkippable: false,
                onStepChange: controlInfoForm.triggerSubmit,
            },
            {
                component: CreateControlInfoWizardMapRequirementStep,
                stepTitle: t`Map requirements`,
                isStepSkippable: false,
                backButtonLabelOverride: t`Back`,
            },
        ];

        if (sharedEntitlementFlagController.isMapControlsTestsEnabled) {
            steps.push({
                component: CreateControlInfoWizardMapAutomatedTestStep,
                stepTitle: t`Map automated tests`,
                isStepSkippable: false,
                backButtonLabelOverride: t`Back`,
            });
        }

        steps.push(
            {
                component: CreateControlInfoWizardMapEvidenceStep,
                stepTitle: t`Map evidence`,
                isStepSkippable: false,
                backButtonLabelOverride: t`Back`,
            },
            {
                component: CreateControlInfoWizardMapPoliciesStep,
                stepTitle: t`Map policies`,
                isStepSkippable: false,
                forwardButtonLabelOverride: t`Finish`,
                backButtonLabelOverride: t`Back`,
            },
        );

        return {
            steps,
            onCancel: handleCancel,
            onComplete: handleComplete,
        };
    }, [controlInfoForm, handleCancel, handleComplete]);

    return (
        <Wizard
            steps={wizardProps.steps}
            data-testid="CreateControlInfoView"
            data-id="vpo-4LfT"
            onCancel={wizardProps.onCancel}
            onComplete={wizardProps.onComplete}
        />
    );
});
