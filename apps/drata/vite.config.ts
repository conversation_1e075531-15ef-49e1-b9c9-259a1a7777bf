import path from 'node:path';
import { visualizer } from 'rollup-plugin-visualizer';
import { defineConfig, mergeConfig } from 'vite';
import { vitePlugin as remix } from '@remix-run/dev';
import rootConfig from '../../vite.config';

const ANALYZE = Boolean(process.env.ANALYZE);

declare module '@remix-run/node' {
    interface Future {
        v3_singleFetch: true;
    }
}

const config = defineConfig({
    root: __dirname,
    resolve: {
        alias: {
            '@ui/app-link': path.resolve(
                import.meta.dirname,
                './app/overrides/app-link/index.ts',
            ),
        },
    },
    build: {
        sourcemap: ANALYZE,
        rollupOptions: {
            output: {
                manualChunks(id) {
                    if (id.includes('ckeditor')) {
                        return 'ckeditor';
                    }

                    return null;
                },
            },
        },
    },
    plugins: [
        remix({
            buildDirectory: 'dist',
            ssr: false,
            future: {
                v3_fetcherPersist: true,
                v3_relativeSplatPath: true,
                v3_throwAbortReason: true,
                v3_singleFetch: true,
                v3_lazyRouteDiscovery: true,
                // TODO: Turn this on when we move to folder routes
                // v3_folderRoutes: true,
            },
        }),
        ANALYZE
            ? visualizer({
                  emitFile: true,
                  openOptions: { allowNonzeroExitCode: true },
              })
            : undefined,
    ].filter(Boolean),
    ssr: {
        noExternal: ['react-dropzone'],
    },
});

export default mergeConfig(rootConfig, config);
