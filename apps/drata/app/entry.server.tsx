/**
 * By default, <PERSON> will handle generating the HTTP Response for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.server.
 */
import { PassThrough } from 'node:stream';
import { isbot } from 'isbot';
import { renderToPipeableStream } from 'react-dom/server';
import {
    type AppLoadContext,
    createReadableStreamFromReadable,
    type EntryContext,
} from '@remix-run/node';
import { RemixServer } from '@remix-run/react';
import { csp } from '../../../functions/csp.mjs';

const ABORT_DELAY = 5_000;

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types, import/no-default-export -- need this
export default function handleRequest(
    request: Request,
    responseStatusCode: number,
    responseHeaders: Headers,
    remixContext: EntryContext,

    // This is ignored so we can keep it in the template for visibility.  Feel
    // free to delete this parameter in your app if you're not using it!
    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- need this
    _loadContext: AppLoadContext,
) {
    responseHeaders.set('Content-Security-Policy', csp);

    return isbot(request.headers.get('user-agent') || '')
        ? // eslint-disable-next-line @typescript-eslint/no-use-before-define -- need this
          handleBotRequest(
              request,
              responseStatusCode,
              responseHeaders,
              remixContext,
          )
        : // eslint-disable-next-line @typescript-eslint/no-use-before-define -- need this
          handleBrowserRequest(
              request,
              responseStatusCode,
              responseHeaders,
              remixContext,
          );
}

function handleBotRequest(
    request: Request,
    responseStatusCode: number,
    responseHeaders: Headers,
    remixContext: EntryContext,
) {
    return new Promise((resolve, reject) => {
        let shellRendered = false;
        const { pipe, abort } = renderToPipeableStream(
            <RemixServer
                context={remixContext}
                url={request.url}
                abortDelay={ABORT_DELAY}
                nonce="remix-server"
            />,
            {
                onAllReady() {
                    shellRendered = true;
                    const body = new PassThrough();
                    const stream = createReadableStreamFromReadable(body);

                    responseHeaders.set('Content-Type', 'text/html');

                    resolve(
                        new Response(stream, {
                            headers: responseHeaders,
                            status: responseStatusCode,
                        }),
                    );

                    pipe(body);
                },
                onShellError(error: unknown) {
                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors -- need this
                    reject(error);
                },
                onError(error: unknown) {
                    // eslint-disable-next-line no-param-reassign -- need this
                    responseStatusCode = 500;
                    // Log streaming rendering errors from inside the shell.  Don't log
                    // errors encountered during initial shell rendering since they'll
                    // reject and get logged in handleDocumentRequest.
                    if (shellRendered) {
                        console.error(error);
                    }
                },
            },
        );

        setTimeout(abort, ABORT_DELAY);
    });
}

function handleBrowserRequest(
    request: Request,
    responseStatusCode: number,
    responseHeaders: Headers,
    remixContext: EntryContext,
) {
    return new Promise((resolve, reject) => {
        let shellRendered = false;
        const { pipe, abort } = renderToPipeableStream(
            <RemixServer
                context={remixContext}
                url={request.url}
                abortDelay={ABORT_DELAY}
                nonce="remix-server"
            />,
            {
                onShellReady() {
                    shellRendered = true;
                    const body = new PassThrough();
                    const stream = createReadableStreamFromReadable(body);

                    responseHeaders.set('Content-Type', 'text/html');

                    resolve(
                        new Response(stream, {
                            headers: responseHeaders,
                            status: responseStatusCode,
                        }),
                    );

                    pipe(body);
                },
                onShellError(error: unknown) {
                    // eslint-disable-next-line @typescript-eslint/prefer-promise-reject-errors -- need this
                    reject(error);
                },
                onError(error: unknown) {
                    // eslint-disable-next-line no-param-reassign -- need this
                    responseStatusCode = 500;
                    // Log streaming rendering errors from inside the shell.  Don't log
                    // errors encountered during initial shell rendering since they'll
                    // reject and get logged in handleDocumentRequest.
                    if (shellRendered) {
                        console.error(error);
                    }
                },
            },
        );

        setTimeout(abort, ABORT_DELAY);
    });
}
