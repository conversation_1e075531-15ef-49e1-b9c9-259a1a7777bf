import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import type { AuditorFrameworkResponseDto } from '@globals/api-sdk/types';
import { action, observer } from '@globals/mobx';
import { AuditDetailsPageHeaderModel } from '@models/audits-details';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditDetailsDownloadOnlyPageView } from '@views/audit-details-download-only-page';
import { AuditsDetailsPageView } from '@views/audits-details-page';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Audit details' }];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { auditId } = params;

        if (!auditId) {
            throw new Error(
                'params.auditId does not exist, and that is a catastrophe. Call the police',
            );
        }

        sharedAuditHubController.loadAuditById(auditId);
        sharedAuditHubController.loadAuditorFrameworkPersonnel(auditId);

        sharedAuditorController.auditSummaryByIdQuery.load({
            path: { auditorFrameworkId: auditId },
        });

        sharedCustomerRequestsController.frameworkId = auditId;
        sharedCustomerRequestsController.customerRequestListQuery.load({
            query: { framework: auditId },
        });

        return {
            pageHeader: new AuditDetailsPageHeaderModel(),
        };
    },
);

const AuditDetailsPage = observer((): React.JSX.Element => {
    const { auditByIdData } = sharedAuditHubController;

    // TODO: This is not ok, these need to be handled as separate routes.
    // Talk to Chris about options.
    return auditByIdData?.framework.auditType ===
        ('FULL_AUDIT' as const satisfies AuditorFrameworkResponseDto['auditType']) ? (
        <AuditsDetailsPageView
            data-testid="AuditDetailsPage"
            data-id="nkWc3xQp"
        />
    ) : (
        <AuditDetailsDownloadOnlyPageView
            data-testid="AuditDownloadOnly"
            data-id="D61xoR_R"
        />
    );
});

export default AuditDetailsPage;
