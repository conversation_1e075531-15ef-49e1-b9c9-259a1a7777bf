import { sharedRequirementCreateController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { CreateFrameworkRequirementView } from '@views/create-framework-requirement';
import type { ClientLoader } from '../types';

export const clientLoader = ({ params }: LoaderFunctionArgs): ClientLoader => {
    const { frameworkId, workspaceId } = params;

    if (!frameworkId) {
        throw new Error('Framework ID is required');
    }

    sharedRequirementCreateController.setFrameworkId(Number(frameworkId));
    sharedRequirementCreateController.loadRequirementCategories(
        Number(frameworkId),
    );

    return {
        pageHeader: {
            title: t`Create framework requirement`,
            backLink: (
                <AppLink
                    href={`/workspaces/${workspaceId}/compliance/frameworks/all/current/${frameworkId}/requirements`}
                    label={t`Back to Requirements`}
                />
            ),
        },
    };
};

const CreateFrameworkRequirement = (): React.JSX.Element => {
    return (
        <CreateFrameworkRequirementView
            data-testid="CreateFrameworkRequirement"
            data-id="XwTLzmhc"
        />
    );
};

export default CreateFrameworkRequirement;
