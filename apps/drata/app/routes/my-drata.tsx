import { sharedPersonnelMeController } from '@controllers/personnel-me';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'My Drata' }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedPersonnelMeController.loadMyPersonnel();

    return null;
});

const MyDrataFlat = (): React.JSX.Element => {
    return <Outlet data-testid="MyDrataFlat" data-id="A6uv9PPH" />;
};

export default MyDrataFlat;
