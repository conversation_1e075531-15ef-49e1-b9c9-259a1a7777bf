import { AppHeader } from '@components/app-header';
import { Stack } from '@cosmos/components/stack';
import { Outlet } from '@remix-run/react';

const WorkspacesFlat = (): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            data-testid="WorkspacesFlat"
            data-id="FgZttcn_"
            height="100%"
            minHeight="0"
            width="100%"
        >
            <AppHeader />
            <Outlet />
        </Stack>
    );
};

export default WorkspacesFlat;
