import {
    sharedAccessReviewController,
    sharedAccessReviewPeriodDetailsController,
    sharedActiveAccessReviewPeriodsController,
} from '@controllers/access-reviews';
import { sharedUsersController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { AccessReviewCreatePeriodWizardView } from '@views/access-review-create-period';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: t`Edit Review Period` }];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { periodId } = params;

        // Validate period ID parameter
        if (!periodId) {
            throw new Error('Period ID is required');
        }

        // Get workspace ID from controller instead of params
        const { currentWorkspace } = sharedWorkspacesController;

        sharedAccessReviewPeriodDetailsController.loadPeriodDetails(
            Number(periodId),
        );

        sharedAccessReviewController.accessReview.load();

        sharedActiveAccessReviewPeriodsController.activeAccessReviewPeriods.load();

        sharedUsersController.loadReviewerUsers();

        return {
            pageHeader: {
                title: t`Edit Review Period`,
                pageId: 'access-review-edit-period',
                backLink: (
                    <AppLink
                        href={`/workspaces/${currentWorkspace?.id}/governance/access-review/active`}
                        label={t`Back to Access Review`}
                    />
                ),
            },
            layout: {
                centered: true,
            },
            tabs: [],
        };
    },
);

const AccessReviewWorkflowEditPeriod = (): React.JSX.Element => {
    return (
        <AccessReviewCreatePeriodWizardView
            data-testid="AccessReviewWorkflowEditPeriod"
            data-id="WF1ii-bq"
        />
    );
};

export default AccessReviewWorkflowEditPeriod;
