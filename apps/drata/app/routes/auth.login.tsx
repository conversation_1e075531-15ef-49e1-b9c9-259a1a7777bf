import type { MetaFunction } from '@remix-run/node';
import { LoginLayoutView } from '@views/login';
import { LoginCustomerView } from '@views/login-customer';

export const meta: MetaFunction = () => [{ title: 'Drata' }];

const LoginCustomerPage = (): React.JSX.Element => {
    return (
        <LoginLayoutView data-testid="LoginCustomerPage" data-id="dIbpeFV0">
            <LoginCustomerView />
        </LoginLayoutView>
    );
};

export default LoginCustomerPage;
