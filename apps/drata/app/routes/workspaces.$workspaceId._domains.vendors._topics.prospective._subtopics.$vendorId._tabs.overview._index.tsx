import {
    SECURITY_OWNER_ROLES,
    VENDOR_CONTACT_ROLES,
} from '@components/vendors-current-add-vendor';
import {
    sharedUsersInfiniteController,
    sharedVendorContactsInfiniteController,
} from '@controllers/users';
import { sharedVendorsIntegrationsInfiniteController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: t`Vendors Prospective Overview` },
];

export const clientLoader = action((): ClientLoader => {
    sharedVendorsIntegrationsInfiniteController.load();
    sharedUsersInfiniteController.loadUsers({
        roles: SECURITY_OWNER_ROLES,
        excludeReadOnlyUsers: true,
    });
    sharedVendorContactsInfiniteController.loadUsers({
        roles: VENDOR_CONTACT_ROLES,
    });

    return null;
});

const VendorsProspectiveOverview = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsProspectiveOverview"
            data-id="RPsOggAK"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsProspectiveOverview;
