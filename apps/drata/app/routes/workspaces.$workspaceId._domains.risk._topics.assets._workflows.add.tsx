import { sharedUsersController } from '@controllers/users';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AddAssetView } from '@views/add-asset';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Add an asset' }];

export const clientLoader = action((): ClientLoader => {
    sharedUsersController.usersList.load();

    return {
        pageHeader: {
            title: 'Add an asset',
            pageId: 'add-asset-page-header',
        },
    };
});

const AddRiskAsset = (): React.JSX.Element => {
    return <AddAssetView data-testid="AddRiskAsset" data-id="vJtg_bBY" />;
};

export default AddRiskAsset;
