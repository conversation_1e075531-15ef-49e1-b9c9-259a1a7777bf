import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileReportsAndDocumentsView } from '@views/vendors-profile-reports-and-documents';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Reports and documents' },
];

const VendorsCurrentReportsAndDocuments = (): React.JSX.Element => {
    return (
        <VendorsProfileReportsAndDocumentsView
            data-testid="VendorsCurrentReportsAndDocuments"
            data-id="MATlLF6M"
        />
    );
};

export default VendorsCurrentReportsAndDocuments;
