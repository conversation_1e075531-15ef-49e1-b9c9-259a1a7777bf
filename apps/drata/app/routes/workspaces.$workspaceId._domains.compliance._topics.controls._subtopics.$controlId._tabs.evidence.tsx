import { sharedControlEvidenceController } from '@controllers/controls';
import { action } from '@globals/mobx';
import type { ClientLoaderFunction, MetaFunction } from '@remix-run/react';
import { ControlsEvidenceView } from '@views/controls-evidence';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Control Detail Evidence' }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { controlId } = params;

        sharedControlEvidenceController.load(Number(controlId));

        return null;
    },
);

const Evidence = (): React.JSX.Element => {
    return <ControlsEvidenceView data-id="brjAywEy" data-testid="Evidence" />;
};

export default Evidence;
