const PolicyBuilderOverview = (): React.JSX.Element => {
    return (
        <div
            data-testid="PolicyBuilderOverview"
            data-id="policy-builder-overview"
        >
            <h2>Policy Builder - Overview Tab</h2>
            <p>
                This will contain the overview tab content with policy details,
                approvers, and owner cards.
            </p>
            <p>
                <strong>Status:</strong> Ready for component integration
            </p>
        </div>
    );
};

export default PolicyBuilderOverview;
