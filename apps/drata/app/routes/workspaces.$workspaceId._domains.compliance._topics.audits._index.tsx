import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const AuditsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/all`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="AuditsIndex" data-id="zb_L36pq"></div>;
};

export default AuditsIndex;
