import { action } from '@globals/mobx';
import { MonitoringPipelineRunDetailsFailuresPanelView } from '@views/monitoring-pipeline-run-details-failures-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringPipelineRunDetailsPipelineFailuresPanel =
    (): React.JSX.Element => {
        return (
            <MonitoringPipelineRunDetailsFailuresPanelView
                data-testid="MonitoringPipelineRunDetailsPipelineFailuresPanel"
                data-id="IJVuh86S"
            />
        );
    };

export default MonitoringPipelineRunDetailsPipelineFailuresPanel;
