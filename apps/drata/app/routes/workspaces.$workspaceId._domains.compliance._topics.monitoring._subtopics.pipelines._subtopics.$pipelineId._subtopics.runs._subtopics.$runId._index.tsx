import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const PipelineRunDetailsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/causes`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="PipelineRunDetailsIndex" data-id="zb_L36pq"></div>;
};

export default PipelineRunDetailsIndex;
