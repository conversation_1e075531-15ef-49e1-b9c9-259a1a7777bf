import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Risk',
        },

        topicsNav: {
            id: 'nav.risk',
            title: t`Risk`,
            domainsOrder: ['risk'],
            domains: {
                risk: {
                    label: t`Risk`,
                    hideLabel: true,
                    topicsOrder: [
                        'risk.insights',
                        'risk.assets',
                        'risk.register',
                        'risk.vulnerabilities',
                    ],
                    topics: {
                        'risk.assets': {
                            id: 'risk.assets',
                            topicPath: 'risk/assets',
                            label: t`Assets`,
                        },
                        'risk.insights': {
                            id: 'risk.insights',
                            topicPath: 'risk/insights',
                            label: t`Insights`,
                        },
                        'risk.register': {
                            id: 'risk.register',
                            topicPath: 'risk/register',
                            label: t`Register`,
                        },
                        'risk.vulnerabilities': {
                            id: 'risk.vulnerabilities',
                            topicPath: 'risk/vulnerabilities',
                            label: t`Vulnerabilities`,
                        },
                    },
                },
            },
        },
    };
});

const Risk = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Risk" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Risk;
