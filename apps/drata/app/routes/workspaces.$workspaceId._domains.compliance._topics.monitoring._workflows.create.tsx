import { Text } from '@cosmos/components/text';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MonitoringBuilderView } from '@views/monitoring-builder';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Monitoring builder' }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Test builder',
            pageId: 'monitoring-builder',
            slot: (
                <Text allowBold align="center" colorScheme="warning">
                    <strong>Draft</strong>
                </Text>
            ),
        },
        tabs: [],
    };
});

const MonitoringBuilder = (): React.JSX.Element => {
    return (
        <MonitoringBuilderView
            data-testid="MonitoringBuilder"
            data-id="4oYwg9Du"
        />
    );
};

export default MonitoringBuilder;
