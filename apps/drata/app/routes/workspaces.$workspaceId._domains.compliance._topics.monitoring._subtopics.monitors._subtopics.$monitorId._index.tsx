import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const MonitorDetailsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="MonitorDetailsIndex" data-id="zb_L36pq"></div>;
};

export default MonitorDetailsIndex;
