import { action } from '@globals/mobx';
import { MonitoringDetailsFindingsPanelView } from '@views/monitoring-details-findings-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringDetailsOverviewFindingsPanel = (): React.JSX.Element => {
    return (
        <MonitoringDetailsFindingsPanelView
            data-testid="MonitoringDetailsOverviewFindingsPanel"
            data-id="1tDf0mmH"
        />
    );
};

export default MonitoringDetailsOverviewFindingsPanel;
