import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const VendorsCurrentIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="VendorsCurrentIndex" data-id="4AUJDYud"></div>;
};

export default VendorsCurrentIndex;
