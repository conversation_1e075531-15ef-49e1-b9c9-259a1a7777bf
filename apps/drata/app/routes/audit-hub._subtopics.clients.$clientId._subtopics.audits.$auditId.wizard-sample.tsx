import {
    sharedAuditHubAuditController,
    sharedAuditHubController,
} from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCurrentUserController } from '@globals/current-user';
import { action, when } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { AuditHubWizardSample } from '@views/audit-hub-wizard-sample';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Audit Hub - Wizard Sample' },
];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { clientId } = params;

        sharedAuditHubAuditController.loadRequestTemplate();

        when(
            () => Boolean(sharedAuditHubController.auditByIdData?.framework),
            () => {
                const { auditByIdData } = sharedAuditHubController;
                const { entryId } = sharedCurrentUserController;

                if (auditByIdData?.framework) {
                    sharedAuditorController.loadAuditorPersonnel({
                        entryId,
                        auditorFrameworkId: auditByIdData.framework.id,
                        frameworkType: auditByIdData.framework.type,
                    });
                }
            },
        );

        return {
            pageHeader: {
                get title() {
                    const { auditByIdData } = sharedAuditHubController;

                    if (!auditByIdData?.framework) {
                        return 'Loading...';
                    }

                    const frameworkType =
                        auditByIdData.framework.frameworkType.label;

                    return `Start ${frameworkType} audit`;
                },
                backLink: (
                    <AppLink
                        href={`/audit-hub/clients/${clientId}/audits`}
                        label="Back to Audits"
                    />
                ),
            },
            layout: {
                centered: true,
            },
        };
    },
);

const AuditorWizardSamples = (): React.JSX.Element => {
    return (
        <AuditHubWizardSample
            data-testid="AuditorWizardSamples"
            data-id="lHwpZI3A"
        />
    );
};

export default AuditorWizardSamples;
