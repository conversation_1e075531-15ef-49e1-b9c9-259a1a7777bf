import { action } from '@globals/mobx';
import { VendorsCurrentTrustCenterDocumentDetailModel } from '@models/vendors-profile';
import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileTrustCenterDocumentDetailView } from '@views/vendors-profile-trust-center-document-detail';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Trust Center Document Detail' },
];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: new VendorsCurrentTrustCenterDocumentDetailModel(),
        contentNav: {
            tabs: [],
        },
    };
});

const VendorsCurrentTrustCenterDocumentDetail = (): React.JSX.Element => {
    return (
        <VendorsProfileTrustCenterDocumentDetailView
            data-testid="VendorsCurrentTrustCenterDocumentDetail"
            data-id="7pHXIOTU"
        />
    );
};

export default VendorsCurrentTrustCenterDocumentDetail;
