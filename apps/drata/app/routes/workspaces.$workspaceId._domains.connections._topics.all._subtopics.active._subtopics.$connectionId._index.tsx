import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const ConnectionDetailsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="ConnectionDetailsIndex" data-id="RmC8kmaW"></div>;
};

export default ConnectionDetailsIndex;
