import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const CodebaseDetailsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="CodebaseDetailsIndex" data-id="zb_L36pq"></div>;
};

export default CodebaseDetailsIndex;
