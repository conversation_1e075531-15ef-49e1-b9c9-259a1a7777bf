import type { MetaFunction } from '@remix-run/node';
import { VendorsQuestionnaireSendViaDrataModalView } from '@views/vendors-questionnaire-send-via-drata-modal';

export const meta: MetaFunction = () => [
    { title: 'Vendors Modals Questionnaire Send Via Drata' },
];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Vendor Modals Questionnaire Send Via Drata',
            pageId: 'vendors-temp-modals-questionnaire-send-via-drata-page',
        },
    },
};

const VendorsModalsQuestionnaireSendViaDrata = (): React.JSX.Element => {
    return (
        <VendorsQuestionnaireSendViaDrataModalView
            data-testid="VendorsModalsQuestionnaireSendViaDrata"
            data-id="mQrehWzs"
        />
    );
};

export default VendorsModalsQuestionnaireSendViaDrata;
