import {
    sharedRiskLibraryController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Register' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedRiskLibraryController.riskLibraryListQuery.load();
    sharedRiskSettingsController.load();

    return {
        pageHeader: {
            title: 'Register',
        },
        contentNav: {
            tabs: [
                {
                    id: 'risk.register.library',
                    topicPath: 'risk/register/library',
                    label: 'Library',
                },
                {
                    id: 'risk.register.management',
                    topicPath: 'risk/register/management',
                    label: 'Management',
                },
            ],
        },
    };
});

const RiskRegister = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegister"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default RiskRegister;
