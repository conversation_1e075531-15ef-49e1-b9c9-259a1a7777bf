import type { MetaFunction } from '@remix-run/node';
import { LoginLayoutView } from '@views/login';
import { LoginEmailSentView } from '@views/login-email-sent';

export const meta: MetaFunction = () => {
    return [{ title: 'Drata' }];
};

export const EmailSentPage = (): React.JSX.Element => {
    return (
        <LoginLayoutView data-testid="EmailSentPage" data-id="xrihOpoc">
            <LoginEmailSentView />
        </LoginLayoutView>
    );
};

export default EmailSentPage;
