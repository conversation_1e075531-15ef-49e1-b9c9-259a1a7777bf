import { sharedConnectionsController } from '@controllers/connections';
import { activeLibraryTestController } from '@controllers/library-test';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { LibraryTestOverviewView } from '@views/library-test-overview';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Test Overview' }];

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        const { testId } = params;

        if (testId) {
            activeLibraryTestController.loadTest(Number(testId));
        }

        sharedConnectionsController.allConfiguredConnectionsQuery.load();

        return null;
    },
);

const LibraryTestOverview = (): React.JSX.Element => {
    return (
        <LibraryTestOverviewView
            data-testid="LibraryTestOverview"
            data-id="xt6TPAIa"
        />
    );
};

export default LibraryTestOverview;
