import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileRisksView } from '@views/vendors-profile-risks';

export const meta: MetaFunction = () => [{ title: 'Vendors Current Risks' }];

const VendorsCurrentRisks = (): React.JSX.Element => {
    return (
        <VendorsProfileRisksView
            data-testid="VendorsCurrentRisks"
            data-id="-OG8tC0B"
        />
    );
};

export default VendorsCurrentRisks;
