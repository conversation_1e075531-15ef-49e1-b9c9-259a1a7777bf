import { libraryTestTemplatesController } from '@controllers/library-tests';
import { action } from '@globals/mobx';
import { LibraryTestsPageHeaderModel } from '@models/library-tests';
import type { MetaFunction } from '@remix-run/node';
import { LibraryTestTemplateView } from '@views/library-tests';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Library Tests' }];

export const clientLoader = action((): ClientLoader => {
    libraryTestTemplatesController.loadTestTemplates();

    return {
        pageHeader: new LibraryTestsPageHeaderModel(),
    };
});

const LibraryTests = (): React.JSX.Element => {
    return (
        <LibraryTestTemplateView
            data-testid="LibraryTests"
            data-id="q5FYgTtC"
        />
    );
};

export default LibraryTests;
