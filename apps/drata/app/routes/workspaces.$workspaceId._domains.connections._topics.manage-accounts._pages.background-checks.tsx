import { sharedConnectionsController } from '@controllers/connections';
import { ActionStack, type Stack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { dimension2x } from '@cosmos/constants/tokens';
import { action } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { RouteLandmark } from '@ui/layout-landmarks';
import { ConnectionManageAccountsBackgroundChecksView } from '@views/connections-manage-accounts-background-checks';
import type { ClientLoader } from '../types';

const ACTIONS: Stack[] = [
    {
        id: 'manage-account-bg-checks-action-stack',
        actions: [
            {
                id: 'learnMore',
                actionType: 'text',
                typeProps: {
                    align: 'left',
                    type: 'body',
                    children: (
                        <AppLink
                            isExternal
                            label="Learn about managing background checks"
                        />
                    ),
                },
            },
        ],
    },
];

export const clientLoader = action((): ClientLoader => {
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            title: 'Manage background checks',
            banner: (
                <Banner
                    body="In-scope employees and contractors need to have a valid background check for related tests to pass. Sort the personnel column for empty fields to find unlinked background checks and select the personnel."
                    data-id="manage-accounts-devices-banner"
                    displayMode="section"
                    severity="warning"
                    title="Some personnel are missing linked background checks"
                />
            ),
            actionStack: <ActionStack gap={dimension2x} stacks={ACTIONS} />,
        },
    };
});

const ConnectionsBackgroundChecks = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionsBackgroundChecks"
            data-id="U-T_ktyd"
        >
            <ConnectionManageAccountsBackgroundChecksView />
        </RouteLandmark>
    );
};

export default ConnectionsBackgroundChecks;
