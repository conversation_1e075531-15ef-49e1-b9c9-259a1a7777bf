import { sharedConnectionsController } from '@controllers/connections';
import {
    sharedEvidenceDetailsArtifactsController,
    sharedEvidenceDetailsController,
} from '@controllers/evidence-library';
import { action } from '@globals/mobx';
import {
    EvidenceDetailsContentNavModel,
    EvidenceDetailsPageHeaderModel,
} from '@models/evidence';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Evidence Details Overview' }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { evidenceId } = params;

        if (!Number(evidenceId)) {
            throw new Error('Evidence ID is required.');
        }

        sharedEvidenceDetailsController.initEvidenceDetailsLoad(
            Number(evidenceId),
        );

        sharedEvidenceDetailsArtifactsController.loadArtifacts(
            Number(evidenceId),
            { isCurrent: false },
        );

        sharedConnectionsController.allConfiguredConnectionsQuery.load();

        return {
            pageHeader: new EvidenceDetailsPageHeaderModel(),
            contentNav: new EvidenceDetailsContentNavModel(),
        };
    },
);

const EvidenceDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="EvidenceDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default EvidenceDetails;
