import { sharedCustomTasksController } from '@controllers/tasks';
import { action } from '@globals/mobx';
import { TasksHeaderModel } from '@models/tasks';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Tasks' }];

export const clientLoader: ClientLoaderFunction = action(
    ({ request }): ClientLoader => {
        const url = new URL(request.url);

        let taskYear = url.searchParams.get('taskYear');
        let ownerId = url.searchParams.get('ownerId');

        if (taskYear && Number.isNaN(Number(taskYear))) {
            taskYear = null;
        }
        if (ownerId && Number.isNaN(Number(ownerId))) {
            ownerId = null;
        }

        sharedCustomTasksController.setFilters({
            year: taskYear ? Number(taskYear) : new Date().getFullYear(),
            ownerId: ownerId ? Number(ownerId) : undefined,
        });

        sharedCustomTasksController.loadTasks();

        return { pageHeader: new TasksHeaderModel() };
    },
);

const Tasks = (): React.JSX.Element => {
    return <Outlet data-testid="Tasks" data-id="krUD_IMN" />;
};

export default Tasks;
