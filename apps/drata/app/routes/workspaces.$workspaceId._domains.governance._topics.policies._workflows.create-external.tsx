import { action } from '@globals/mobx';
import { PoliciesAddExternalView } from '@views/policies-add-external';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Policies',
            pageId: 'policies-create-external',
        },
        tabs: [],
    };
});

const CreateExternalPolicyWorkflow = (): React.JSX.Element => {
    return (
        <PoliciesAddExternalView
            data-testid="CreateExternalPolicyWorkflow"
            data-id="QRs3RQeQ"
        />
    );
};

export default CreateExternalPolicyWorkflow;
