import {
    sharedMonitoringController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedMonitoringController.monitoringProductionList.load({
        path: { workspaceId: 1, xWorkspaceId: 1 },
    });
    sharedMonitoringStatsController.monitoringStats.load({
        path: { xProductId: 1 },
        query: { type: 'DEPLOYED' },
    });

    return {
        tabs: [
            {
                topicPath: 'compliance/monitoring/monitors',
                label: t`Production`,
            },
            {
                topicPath: 'compliance/monitoring/pipelines',
                label: t`Pipelines`,
            },
            {
                topicPath: 'compliance/monitoring/codebases',
                label: t`Codebases`,
            },
        ],
    };
});

export const meta: MetaFunction = () => [{ title: 'Monitoring' }];

const Monitoring = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Monitoring" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Monitoring;
