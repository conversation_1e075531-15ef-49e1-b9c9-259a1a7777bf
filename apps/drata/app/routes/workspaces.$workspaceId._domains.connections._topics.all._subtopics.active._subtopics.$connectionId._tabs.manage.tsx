import { sharedConnectionsController } from '@controllers/connections';
import { action } from '@globals/mobx';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import { SettingsConnectionsManageView } from '@views/settings-connections-manage';

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): null => {
        const { connectionId } = params;

        if (!connectionId || isNaN(Number(connectionId))) {
            throw new Error('Connection ID is required');
        }

        sharedConnectionsController.loadSingleConnection(Number(connectionId));

        return null;
    },
);

const ConnectionDetailsManageTab = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionDetailsManageTab"
            data-id="VLDXbn4p"
        >
            <SettingsConnectionsManageView />
        </RouteLandmark>
    );
};

export default ConnectionDetailsManageTab;
