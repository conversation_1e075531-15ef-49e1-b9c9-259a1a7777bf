import { action } from '@globals/mobx';
import { MonitoringDetailsControlsPanelView } from '@views/monitoring-details-controls-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringCodeDetailsOverviewControlPanel = (): React.JSX.Element => {
    return (
        <MonitoringDetailsControlsPanelView
            data-testid="MonitoringCodeDetailsOverviewControlPanel"
            data-id="afP_ral0"
        />
    );
};

export default MonitoringCodeDetailsOverviewControlPanel;
