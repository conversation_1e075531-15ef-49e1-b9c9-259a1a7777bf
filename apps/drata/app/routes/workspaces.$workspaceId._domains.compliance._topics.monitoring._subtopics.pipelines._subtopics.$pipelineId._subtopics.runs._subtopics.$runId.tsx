import { Banner } from '@cosmos/components/banner';
import { <PERSON><PERSON> } from '@cosmos/components/button';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

const MONITORING_PIPELINE_HEADER_KEY_VALUES = [
    {
        id: 'monitoring-header-date',
        'data-id': 'monitoring-header-date',
        label: 'Date',
        value: 'Oct 05, 2024 at 5:03pm',
        type: 'TEXT',
    },
    {
        id: 'monitoring-header-repository',
        'data-id': 'monitoring-header-repository',
        label: 'Repository',
        value: 'Acorn-QA/terracorn.insecure',
        type: 'TEXT',
    },
    {
        id: 'monitoring-header-repository-owner',
        'data-id': 'monitoring-repository-owner',
        label: 'Repository owner',
        iconName: 'Edit',
        iconSize: '100',
        isInteractive: true,
        type: 'USER',
        value: {
            username: 'Acorn-QA',
            avatarProps: {
                fallbackText: 'AQ',
                imgSrc: '',
                imgAlt: '',
            },
        },
    },
    {
        id: 'monitoring-header-source',
        'data-id': 'monitoring-header-source',
        label: 'Source',
        value: 'drata/orchestrator_test/8011',
        type: 'TEXT',
    },
    {
        id: 'monitoring-header-trigger',
        'data-id': 'monitoring-header-trigger',
        label: 'Trigger',
        value: 'push',
        type: 'TEXT',
    },
] as const satisfies KeyValuePairProps[];

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { pipelineId, runId } = params;

        return {
            pageHeader: {
                title: 'Pipeline run details',
                pageId: 'monitoring-pipeline-run',

                keyValuePairs: MONITORING_PIPELINE_HEADER_KEY_VALUES,

                slot: (
                    <Metadata
                        colorScheme="neutral"
                        label="GH"
                        type="tag"
                        data-id="p8IiNATm"
                    />
                ),

                banner: (
                    <Banner
                        data-id="banner-past-pipeline-testid"
                        displayMode="section"
                        severity="primary"
                        title="This is a past Pipeline run"
                        body={
                            <Button
                                label="View latest run"
                                level="secondary"
                                data-id="latest-run-button"
                            />
                        }
                    />
                ),
            },
            tabs: [
                {
                    topicPath: `compliance/monitoring/pipelines/${pipelineId}/runs/${runId}/causes`,
                    label: 'Causing pipeline to fail',
                },
                {
                    topicPath: `compliance/monitoring/pipelines/${pipelineId}/runs/${runId}/failures`,
                    label: 'Other failures',
                },
                {
                    topicPath: `compliance/monitoring/pipelines/${pipelineId}/runs/${runId}/exclusions`,
                    label: 'Exclusions',
                },
            ],
        };
    },
);

const PipelineRunDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PipelineRunDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default PipelineRunDetails;
