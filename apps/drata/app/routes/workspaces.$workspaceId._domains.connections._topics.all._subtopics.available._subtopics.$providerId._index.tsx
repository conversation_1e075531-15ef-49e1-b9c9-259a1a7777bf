import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const ProviderDetailsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/browse`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="ProviderDetailsIndex" data-id="qBAnrTCf"></div>;
};

export default ProviderDetailsIndex;
