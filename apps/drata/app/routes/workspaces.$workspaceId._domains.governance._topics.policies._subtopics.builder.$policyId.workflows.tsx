const PolicyBuilderWorkflows = (): React.JSX.Element => {
    return (
        <div
            data-testid="PolicyBuilderWorkflows"
            data-id="policy-builder-workflows"
        >
            <h2>Policy Builder - Workflows Tab</h2>
            <p>
                This will contain the workflow and multi-approver configuration
                functionality.
            </p>
        </div>
    );
};

export default PolicyBuilderWorkflows;
