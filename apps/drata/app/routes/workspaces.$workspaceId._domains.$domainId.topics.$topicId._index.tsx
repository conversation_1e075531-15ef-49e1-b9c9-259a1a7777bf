import type { MetaFunction } from '@remix-run/node';
import { useParams } from '@remix-run/react';

export const meta: MetaFunction = () => [{ title: 'TopicIdIndex' }];

const TopicIdIndex = (): React.JSX.Element => {
    const { workspaceId, domainId, topicId } = useParams();

    return (
        <div
            data-testid="TopicIdIndex"
            data-id="jPwZmxJb"
            style={{
                background: '#ff0',
                padding: '16px',
            }}
        >
            <div style={{ background: '#0f0' }}>
                TopicsIndex: {workspaceId} : {domainId} : {topicId}
            </div>
        </div>
    );
};

export default TopicIdIndex;
