import { sharedConnectionsController } from '@controllers/connections';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { EvidenceAddEvidenceView } from '@views/evidence-add-evidence';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Create evidence' }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            title: t`Create evidence`,
            isCentered: true,
        },
        topicsNav: {},
    };
});

const ComplianceEvidenceAddEvidence = (): React.JSX.Element => {
    return (
        <EvidenceAddEvidenceView
            data-testid="ComplianceEvidenceAddEvidence"
            data-id="tDlB-1WC"
        />
    );
};

export default ComplianceEvidenceAddEvidence;
