import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { DashboardAllWorkspacesView } from '@views/dashboard-all-workspaces';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Dashboard All Workspaces` }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: t`All Workspaces`,
        },
    };
});

const DashboardAllWorkSpacesTab = (): React.JSX.Element => {
    return (
        <DashboardAllWorkspacesView
            data-testid="DashboardAllWorkSpacesTab"
            data-id="xUOQZNE2"
        />
    );
};

export default DashboardAllWorkSpacesTab;
