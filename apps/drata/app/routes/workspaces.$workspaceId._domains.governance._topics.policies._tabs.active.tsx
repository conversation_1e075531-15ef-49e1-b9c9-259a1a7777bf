import { action } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import { PoliciesActiveView } from '@views/policies';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: sharedPoliciesPageHeaderModel,
    };
});

const PoliciesActive = (): React.JSX.Element => {
    return (
        <PoliciesActiveView data-testid="PoliciesActive" data-id="d_yY5uG5" />
    );
};

export default PoliciesActive;
