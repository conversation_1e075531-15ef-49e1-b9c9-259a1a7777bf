import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const AllConnectionsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/active`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="AllConnectionsIndex" data-id="qBAnrTCf"></div>;
};

export default AllConnectionsIndex;
