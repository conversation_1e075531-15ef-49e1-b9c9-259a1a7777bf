import type { MetaFunction } from '@remix-run/node';
import { VendorsUploadResponseFilesModalView } from '@views/vendors-upload-response-files-modal';

export const meta: MetaFunction = () => [
    { title: 'Vendors Modals Upload Response Files Questionnaire' },
];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Vendor Modals Upload Response Files Questionnaire',
            pageId: 'vendors-temp-modals-upload-response-files-questionnaire-page',
        },
    },
};

const VendorsUploadResponseFilesModal = (): React.JSX.Element => {
    return (
        <VendorsUploadResponseFilesModalView
            data-testid="VendorsUploadResponseFilesModal"
            data-id="qjlCCa1N"
        />
    );
};

export default VendorsUploadResponseFilesModal;
