import { LoginAuthenticationIssue } from '@components/login';
import type { MetaFunction } from '@remix-run/node';
import { LoginLayoutView } from '@views/login';

export const meta: MetaFunction = () => {
    return [{ title: 'Drata' }];
};

export const AuthenticationIssue = (): React.JSX.Element => {
    return (
        <LoginLayoutView data-testid="AuthenticationIssue" data-id="OdzEx8sV">
            <LoginAuthenticationIssue />
        </LoginLayoutView>
    );
};

export default AuthenticationIssue;
