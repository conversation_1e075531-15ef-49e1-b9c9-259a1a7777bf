import {
    sharedCustomFrameworkRequirementsController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import { sharedRequirementsController } from '@controllers/requirements';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { action, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { FrameworkDetailsPageHeaderModel } from '@models/framework-details';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import type { ClientLoader } from '../types';

const DEFAULT_PARAMS: FetchDataResponseParams = {
    pagination: {
        page: 1,
        pageSize: DEFAULT_PAGE_SIZE,
        pageIndex: 0,
        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
    },
    globalFilter: {
        search: '',
        filters: {
            isInScope: {
                filterType: 'radio',
                value: 'true',
            },
        },
    },
    sorting: [],
};

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { frameworkId } = params;

        if (!frameworkId) {
            throw new Error('Framework ID is required');
        }

        sharedRequirementsController.frameworkDetailsId = Number(frameworkId);

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    return;
                }

                sharedFrameworkDetailsController.frameworkDetailsQuery.load({
                    path: {
                        frameworkId: Number(frameworkId),
                        xProductId: Number(currentWorkspace.id),
                    },
                });
            },
        );

        when(
            () =>
                Boolean(sharedWorkspacesController.currentWorkspace) &&
                Boolean(sharedFrameworkDetailsController.frameworkDetails),
            () => {
                const { frameworkDetails: framework } =
                    sharedFrameworkDetailsController;

                if (!framework) {
                    return;
                }

                const isCustomFramework = framework.tag === 'CUSTOM';

                if (isCustomFramework) {
                    sharedCustomFrameworkRequirementsController.loadCustomFrameworkRequirements(
                        DEFAULT_PARAMS,
                    );
                } else {
                    sharedRequirementsController.loadRequirements(
                        DEFAULT_PARAMS,
                    );
                }
            },
        );

        return {
            pageHeader: new FrameworkDetailsPageHeaderModel(),
            contentNav: {
                tabs: [],
            },
        };
    },
);

const FrameworksRequirements = (): React.JSX.Element => {
    return <Outlet data-testid="FrameworksRequirements" data-id="yK0g7TAF" />;
};

export default FrameworksRequirements;
