import { activeLibraryTestController } from '@controllers/library-test';
import { action } from '@globals/mobx';
import {
    LibraryTestPageHeaderModel,
    sharedLibraryTestMappingsFilterModel,
} from '@models/library-test';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { testId } = params;

        if (!Number(testId)) {
            throw new Error('Invalid requestId');
        }

        activeLibraryTestController.loadTest(Number(testId));
        sharedLibraryTestMappingsFilterModel.loadFrameworks(Number(testId));

        return {
            pageHeader: new LibraryTestPageHeaderModel(),
            tabs: [
                {
                    id: 'library.test.overview',
                    topicPath: `library/tests/${testId}/overview`,
                    label: 'Overview',
                },
                {
                    id: 'library.test.mappings',
                    topicPath: `library/tests/${testId}/mappings`,
                    label: 'Mappings',
                },
            ],
        };
    },
);

const LibraryTest = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="LibraryTest"
            data-id="yKbcJXMz"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default LibraryTest;
