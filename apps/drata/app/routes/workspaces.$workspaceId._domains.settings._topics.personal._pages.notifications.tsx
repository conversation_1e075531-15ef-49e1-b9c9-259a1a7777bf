import { sharedConnectionsController } from '@controllers/connections';
import { sharedUserSettingsController } from '@controllers/settings-user';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsNotificationsView } from '@views/settings-notifications';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Notifications' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedUserSettingsController.loadFeatureSettings('notifications');
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            pageId: 'settings-notifications',
            title: 'Notifications',
            isCentered: true,
        },
    };
});

const SettingsNotificationsPage = (): React.JSX.Element => {
    return (
        <SettingsNotificationsView
            data-testid="SettingsNotificationsPage"
            data-id="RPk1fL-3"
        />
    );
};

export default SettingsNotificationsPage;
