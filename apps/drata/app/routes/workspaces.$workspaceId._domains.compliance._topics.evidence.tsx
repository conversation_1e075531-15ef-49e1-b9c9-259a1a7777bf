import { sharedConnectionsController } from '@controllers/connections';
import { sharedEvidenceLibraryListController } from '@controllers/evidence-library';
import { sharedUsersInfiniteController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { EvidenceLibraryPageHeaderModel } from '@models/evidence';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: t`Evidence` }];

export const clientLoader = action((): ClientLoader => {
    sharedEvidenceLibraryListController.load();
    sharedUsersInfiniteController.loadUsers({
        roles: ['ADMIN', 'TECHGOV', 'EMPLOYEE'],
        withAllUsers: true,
    });

    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: new EvidenceLibraryPageHeaderModel(),
    };
});

const EvidenceLibrary = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="EvidenceLibrary"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default EvidenceLibrary;
