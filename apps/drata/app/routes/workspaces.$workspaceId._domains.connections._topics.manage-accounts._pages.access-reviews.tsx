import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import { ConnectionManageAccountsAccessReviewView } from '@views/connections-manage-accounts-access-review';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Connections - Manage Account - Access Review' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Manage Access Review Accounts',
        },
    };
});

const ConnectionsAccessReviews = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionsAccessReviews"
            data-id="bZ1yQ8L9"
        >
            <ConnectionManageAccountsAccessReviewView />
        </RouteLandmark>
    );
};

export default ConnectionsAccessReviews;
