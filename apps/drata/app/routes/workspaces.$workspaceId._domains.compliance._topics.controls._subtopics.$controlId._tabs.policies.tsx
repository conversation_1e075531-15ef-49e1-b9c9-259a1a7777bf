import { sharedControlPoliciesController } from '@controllers/controls';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { ControlsPoliciesView } from '@views/controls-policies';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Control Policies' }];
};
export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { controlId } = params;

        sharedControlPoliciesController.load(Number(controlId));

        return null;
    },
);

const Policies = (): React.JSX.Element => {
    return <ControlsPoliciesView data-testid="Policies" data-id="cAHouebq" />;
};

export default Policies;
