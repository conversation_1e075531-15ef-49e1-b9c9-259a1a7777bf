import { action } from '@globals/mobx';
import { MonitoringDetailsFindingsPanelView } from '@views/monitoring-details-findings-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringCodeDetailsOverviewFindingsPanel = (): React.JSX.Element => {
    return (
        <MonitoringDetailsFindingsPanelView
            data-testid="MonitoringCodeDetailsOverviewFindingsPanel"
            data-id="cCj0-VtO"
        />
    );
};

export default MonitoringCodeDetailsOverviewFindingsPanel;
