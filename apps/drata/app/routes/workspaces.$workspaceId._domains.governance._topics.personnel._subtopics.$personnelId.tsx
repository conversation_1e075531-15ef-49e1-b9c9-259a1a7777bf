import { activeGovernancePersonnelController } from '@controllers/governance-personnel';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = ({
    params,
}): ClientLoader => {
    const { personnelId } = params;

    if (!personnelId) {
        throw new Error(
            'personnelId does not exist, and that is a catastrophe. Call the police',
        );
    }

    activeGovernancePersonnelController.personnelDetails.load({
        path: { personnelId: parseInt(personnelId) },
    });

    return {
        tabs: [
            {
                topicPath: `governance/personnel/${personnelId}/overview`,
                label: 'Overview',
            },
            {
                topicPath: `governance/personnel/${personnelId}/devices`,
                label: 'Devices',
            },
        ],
    };
};

const PersonnelDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PersonnelDetails"
            data-id="MviHNu9x"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default PersonnelDetails;
