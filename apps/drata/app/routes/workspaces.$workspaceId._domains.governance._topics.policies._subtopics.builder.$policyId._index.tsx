import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const PolicyBuilderIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return (
        <div
            data-testid="PolicyBuilderIndex"
            data-id="policy-builder-index"
        ></div>
    );
};

export default PolicyBuilderIndex;
