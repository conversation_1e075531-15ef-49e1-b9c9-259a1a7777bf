import { sharedWorkspaceMonitorsCoordinatorController } from '@controllers/workspace-monitors-coordinator';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { ControlsMonitoringView } from '@views/controls-monitoring';

export const meta: MetaFunction = () => {
    return [{ title: 'Control Monitoring' }];
};

export const clientLoader: ClientLoaderFunction = action(({ params }) => {
    const { controlId } = params;

    sharedWorkspaceMonitorsCoordinatorController.setControlId(
        Number(controlId),
    );

    sharedWorkspaceMonitorsCoordinatorController.loadMonitors();

    return null;
});

const Monitoring = (): React.JSX.Element => {
    return (
        <ControlsMonitoringView data-testid="Monitoring" data-id="gUm3wIYI" />
    );
};

export default Monitoring;
