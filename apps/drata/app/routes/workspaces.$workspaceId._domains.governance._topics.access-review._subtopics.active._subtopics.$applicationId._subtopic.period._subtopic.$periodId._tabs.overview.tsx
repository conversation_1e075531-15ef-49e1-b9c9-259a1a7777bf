import { isEmpty } from 'lodash-es';
import {
    sharedAccessReviewActiveApplicationNotesController,
    sharedAccessReviewApplicationEvidenceController,
    sharedAccessReviewApplicationReviewersController,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationSummaryController,
} from '@controllers/access-reviews';
import { action } from '@globals/mobx';
import { AccessReviewApplicationDetailsModel } from '@models/access-review';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { AccessReviewApplicationDetailsView } from '@views/access-review-application-details';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        if (isEmpty(params.applicationId) || isEmpty(params.periodId)) {
            throw new Error('Invalid params');
        }

        sharedAccessReviewPeriodApplicationController.loadAccessReviewPeriodApplication(
            params,
        );

        sharedAccessReviewPeriodApplicationSummaryController.loadAccessReviewPeriodApplicationSummary(
            params,
        );

        sharedAccessReviewPeriodApplicationSummaryController.setPeriodId(
            Number(params.periodId),
        );

        sharedAccessReviewPeriodApplicationSummaryController.setApplicationId(
            Number(params.applicationId),
        );

        sharedAccessReviewApplicationReviewersController.setPeriodId(
            Number(params.periodId),
        );

        sharedAccessReviewApplicationReviewersController.setReviewAppId(
            Number(params.applicationId),
        );

        sharedAccessReviewApplicationEvidenceController.setPeriodId(
            Number(params.periodId),
        );

        sharedAccessReviewApplicationEvidenceController.setApplicationId(
            Number(params.applicationId),
        );

        sharedAccessReviewActiveApplicationNotesController.loadNotes({
            periodId: params.periodId,
            reviewAppId: params.applicationId,
        });

        return {
            pageHeader: new AccessReviewApplicationDetailsModel(),
            utilities: {
                utilitiesList: ['notes_for_access_review_active_application'],
            },
        };
    },
);

const UserAccessReviewActiveOverviewDetails = (): React.JSX.Element => {
    return (
        <AccessReviewApplicationDetailsView
            data-testid="UserAccessReviewActiveOverviewDetails"
            data-id="EdTtRHlZ"
        />
    );
};

export default UserAccessReviewActiveOverviewDetails;
