import { action } from '@globals/mobx';
import { MonitoringPipelineRunDetailsCausesPanelView } from '@views/monitoring-pipeline-run-details-causes-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringPipelineRunDetailsPipelineCausesPanel =
    (): React.JSX.Element => {
        return (
            <MonitoringPipelineRunDetailsCausesPanelView
                data-testid="MonitoringPipelineRunDetailsPipelineCausesPanel"
                data-id="UxRcRTLB"
            />
        );
    };

export default MonitoringPipelineRunDetailsPipelineCausesPanel;
