// import { SettingsWorkspacesView } from '@views/settings-workspaces';
import { useLocation } from '@remix-run/react';
import { AppLink } from '@ui/app-link';

export const SettingsWorkspaces = (): React.JSX.Element => {
    const { pathname } = useLocation();

    return (
        <div data-testid="SettingsWorkspaces" data-id="s85rwMkx">
            <h1>SettingsWorkspaces Index</h1>

            <p>IDK why this page is broken at SettingsWorkspacesView</p>

            <nav>
                <AppLink
                    href={`${pathname}/some-foo`}
                    label="Some workspace id"
                />
            </nav>
        </div>
        // <SettingsWorkspacesView
        //     data-testid="SettingsWorkspaces"
        //     data-id="TGTCn6_L"
        // />
    );
};

export default SettingsWorkspaces;
