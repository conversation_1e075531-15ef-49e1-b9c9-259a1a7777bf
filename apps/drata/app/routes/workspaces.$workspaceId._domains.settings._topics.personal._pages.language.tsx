import { Metadata } from '@cosmos/components/metadata';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsMyLanguageView } from '@views/settings-my-language';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Language' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            title: 'Personal Language',
            slot: (
                <Metadata colorScheme="education" label="Beta" type="status" />
            ),
        },
    };
});

export const SettingsMyLanguage = (): React.JSX.Element => {
    return (
        <SettingsMyLanguageView
            data-testid="SettingsMyLanguage"
            data-id="gy192fnm"
        />
    );
};

export default SettingsMyLanguage;
