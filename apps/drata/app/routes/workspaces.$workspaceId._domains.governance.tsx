import { isString } from 'lodash-es';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader, Topic } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Governance` }];
};

// we are keeping the model in here as the same pattern as the rest of first elements on the other topics
class GovernancePageTopicsNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get topicsNav() {
        const topicsOrder = [
            'governance.access-review',
            'governance.personnel',
            'governance.policies',
            'governance.vulnerabilities',
        ].filter(isString);

        const topics: Record<string, Topic> = {
            'governance.access-review': {
                id: 'governance.access-review',
                topicPath: 'governance/access-review',
                label: t`Access review`,
            },
            'governance.personnel': {
                id: 'governance.personnel',
                topicPath: 'governance/personnel',
                label: t`Personnel`,
            },
            'governance.policies': {
                id: 'governance.policies',
                topicPath: 'governance/policies',
                label: t`Policies`,
            },
            'governance.vulnerabilities': {
                id: 'governance.vulnerabilities',
                topicPath: 'governance/vulnerabilities',
                label: t`Vulnerabilities`,
            },
        };

        return {
            id: 'nav.governance',
            title: t`Governance`,
            domainsOrder: ['governance'],
            domains: {
                governance: {
                    label: t`Governance`,
                    hideLabel: true,
                    topicsOrder,
                    topics,
                },
            },
        };
    }
}

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: t`Governance`,
        },

        topicsNav: new GovernancePageTopicsNavModel(),
    };
});

const Governance = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Governance" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Governance;
