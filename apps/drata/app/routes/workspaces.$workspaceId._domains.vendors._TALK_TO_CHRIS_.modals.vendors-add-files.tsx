import type { MetaFunction } from '@remix-run/node';
import { VendorsUploadResponseFilesModalView } from '@views/vendors-upload-response-files-modal';

export const meta: MetaFunction = () => [
    { title: 'Vendors Modals Manual Security Questionnaire Upload' },
];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Vendor Modals Manual Security Questionnaire Upload',
            pageId: 'vendors-temp-modals-manual-security-questionnaire-upload-page',
        },
    },
};

const VendorsModalsAddFiles = (): React.JSX.Element => {
    return (
        <VendorsUploadResponseFilesModalView
            data-testid="VendorsModalsAddFiles"
            data-id="2ZJxGNs0"
        />
    );
};

export default VendorsModalsAddFiles;
