import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const ComplianceIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/controls`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="ComplianceIndex" data-id="zb_L36pq"></div>;
};

export default ComplianceIndex;
