import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const FrameworksIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/all`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="FrameworksIndex" data-id="zb_L36pq"></div>;
};

export default FrameworksIndex;
