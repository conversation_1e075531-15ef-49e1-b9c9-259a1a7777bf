import { isNil } from 'lodash-es';
import {
    sharedAccessReviewActiveApplicationNotesController,
    sharedAccessReviewApplicationGroupsController,
    sharedAccessReviewApplicationReviewersController,
    sharedAccessReviewBulkActionStatusController,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationSummaryController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import { sharedConnectionsController } from '@controllers/connections';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import { AccessReviewApplicationDetailsModel } from '@models/access-review';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AccessReviewApplicationPeriodPersonnelView } from '@views/access-review-application-period-personnel';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { periodId, applicationId } = params;

        if (isNil(periodId) || isNil(applicationId)) {
            throw new Error('periodId and applicationId are required');
        }

        sharedConnectionsController.allConfiguredConnectionsQuery.load();

        sharedAccessReviewPeriodApplicationSummaryController.setPeriodId(
            Number(periodId),
        );

        sharedAccessReviewPeriodApplicationSummaryController.setApplicationId(
            Number(applicationId),
        );

        sharedAccessReviewPeriodApplicationSummaryController.loadAccessReviewPeriodApplicationSummary(
            params,
        );

        sharedAccessReviewPeriodApplicationController.loadAccessReviewPeriodApplicationConnections();

        sharedAccessReviewApplicationGroupsController.loadAccessReviewApplicationGroups();

        sharedAccessReviewPeriodApplicationUsersController.setPeriodId(
            Number(periodId),
        );

        sharedAccessReviewPeriodApplicationUsersController.setApplicationId(
            Number(applicationId),
        );

        sharedAccessReviewPeriodApplicationController.loadAccessReviewPeriodApplication(
            {
                periodId,
                applicationId,
            },
        );

        sharedAccessReviewApplicationReviewersController.setReviewAppId(
            Number(applicationId),
        );

        sharedAccessReviewApplicationReviewersController.setPeriodId(
            Number(periodId),
        );

        sharedAccessReviewBulkActionStatusController.setPeriodId(
            Number(periodId),
        );

        sharedAccessReviewBulkActionStatusController.setApplicationId(
            Number(applicationId),
        );

        sharedAccessReviewPeriodApplicationUsersController.resetFilterValues();
        sharedAccessReviewPeriodApplicationUsersController.invalidateAccessReviewApplicationUsers();

        sharedAccessReviewPeriodApplicationUsersController.loadAccessReviewPeriodApplicationUsers(
            {
                pagination: {
                    pageIndex: 0,
                    pageSize: 10,
                    pageSizeOptions: [DEFAULT_PAGE_SIZE],
                },
                globalFilter: { search: '', filters: {} },
                sorting: [],
            },
        );

        sharedAccessReviewActiveApplicationNotesController.loadNotes({
            periodId,
            reviewAppId: applicationId,
        });

        sharedConnectionsController.allConfiguredConnectionsQuery.load();

        return {
            pageHeader: new AccessReviewApplicationDetailsModel(),
            utilities: {
                utilitiesList: ['notes_for_access_review_active_application'],
            },
        };
    },
);

export const meta: MetaFunction = () => [{ title: 'Access Review Details' }];

const UserAccessReviewActivePersonnel = (): React.JSX.Element => {
    return (
        <AccessReviewApplicationPeriodPersonnelView
            data-testid="UserAccessReviewActivePersonnel"
            data-id="2gKCfv9i"
        />
    );
};

export default UserAccessReviewActivePersonnel;
