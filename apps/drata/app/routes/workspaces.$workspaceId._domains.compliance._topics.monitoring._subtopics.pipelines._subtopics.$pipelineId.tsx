import { action } from '@globals/mobx';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringPipelineDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="MonitoringPipelineDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default MonitoringPipelineDetails;
