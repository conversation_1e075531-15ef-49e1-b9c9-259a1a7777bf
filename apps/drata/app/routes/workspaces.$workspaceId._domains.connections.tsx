import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Connections` }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: t`Connections`,
        },

        topicsNav: {
            id: 'nav.connections',
            title: t`Connections`,
            domainsOrder: ['connections', 'connections.manage-accounts'],
            domains: {
                connections: {
                    label: t`Connections`,
                    hideLabel: true,
                    topicsOrder: ['connections.all'],
                    topics: {
                        'connections.all': {
                            id: 'connections.all',
                            topicPath: 'connections/all',
                            label: t`All Connections`,
                        },
                    },
                },
                'connections.manage-accounts': {
                    label: t`Manage accounts`,
                    topicsOrder: [
                        'connections.manage-accounts.access-reviews',
                        'connections.manage-accounts.background-checks',
                        'connections.manage-accounts.devices',
                        'connections.manage-accounts.infrastructure',
                        'connections.manage-accounts.observability',
                        'connections.manage-accounts.version-control',
                    ],
                    topics: {
                        'connections.manage-accounts.access-reviews': {
                            id: 'connections.manage-accounts.access-reviews',
                            topicPath:
                                'connections/manage-accounts/access-reviews',
                            label: t`Access Reviews`,
                        },
                        'connections.manage-accounts.background-checks': {
                            id: 'connections.manage-accounts.background-checks',
                            topicPath:
                                'connections/manage-accounts/background-checks',
                            label: t`Background Checks`,
                        },
                        'connections.manage-accounts.devices': {
                            id: 'connections.manage-accounts.devices',
                            topicPath: 'connections/manage-accounts/devices',
                            label: t`Devices`,
                        },
                        'connections.manage-accounts.infrastructure': {
                            id: 'connections.manage-accounts.infrastructure',
                            topicPath:
                                'connections/manage-accounts/infrastructure',
                            label: t`Infrastructure`,
                        },
                        'connections.manage-accounts.observability': {
                            id: 'connections.manage-accounts.observability',
                            topicPath:
                                'connections/manage-accounts/observability',
                            label: t`Observability`,
                        },
                        'connections.manage-accounts.version-control': {
                            id: 'connections.manage-accounts.version-control',
                            topicPath:
                                'connections/manage-accounts/version-control',
                            label: t`Version Control`,
                        },
                    },
                },
            },
        },
    };
});

const Connections = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="Connections"
            data-id="ISzzDb1x"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default Connections;
