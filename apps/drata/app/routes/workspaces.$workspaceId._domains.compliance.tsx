import { isString } from 'lodash-es';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Compliance` }];
};

class CompliancePageTopicsNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get topicsNav() {
        const { hasReadEvidenceLibraryPermission, hasReadControlPermission } =
            sharedFeatureAccessModel;

        const topicsOrder = [
            hasReadControlPermission ? 'compliance.controls' : null,
            'compliance.frameworks',
            'compliance.monitoring',
            hasReadEvidenceLibraryPermission ? 'compliance.evidence' : null,
            'compliance.audits',
        ].filter(isString);

        return {
            id: 'nav.compliance',
            title: t`Compliance`,
            domainsOrder: ['compliance'],
            domains: {
                compliance: {
                    label: t`Compliance`,
                    hideLabel: true,
                    topicsOrder,
                    topics: {
                        'compliance.controls': {
                            id: 'compliance.controls',
                            topicPath: 'compliance/controls',
                            label: t`Controls`,
                        },
                        'compliance.frameworks': {
                            id: 'compliance.frameworks',
                            topicPath: 'compliance/frameworks',
                            label: t`Frameworks`,
                        },
                        'compliance.monitoring': {
                            id: 'compliance.monitoring',
                            topicPath: 'compliance/monitoring',
                            label: t`Monitoring`,
                        },
                        'compliance.evidence': {
                            id: 'compliance.evidence',
                            topicPath: 'compliance/evidence',
                            label: t`Evidence`,
                        },
                        'compliance.audits': {
                            id: 'compliance.audits',
                            topicPath: 'compliance/audits',
                            label: t`Audits`,
                        },
                    },
                },
            },
        };
    }
}

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: t`Compliance`,
        },
        topicsNav: new CompliancePageTopicsNavModel(),
    };
});

const Compliance = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Compliance" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Compliance;
