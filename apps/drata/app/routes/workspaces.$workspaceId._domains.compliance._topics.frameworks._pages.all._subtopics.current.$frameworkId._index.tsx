import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const FrameworksRequirements = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/requirements`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="FrameworksRequirements" data-id="zb_L36pq"></div>;
};

export default FrameworksRequirements;
