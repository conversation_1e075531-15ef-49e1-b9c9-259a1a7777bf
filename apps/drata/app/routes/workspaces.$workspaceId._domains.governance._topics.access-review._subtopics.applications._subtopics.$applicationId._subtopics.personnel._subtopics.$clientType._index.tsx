import { sharedAccessReviewApplicationGroupsController } from '@controllers/access-reviews';
import {
    activeAccessReviewsApplicationsController,
    activeAccessReviewsApplicationsUserController,
} from '@controllers/access-reviews-applications';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action, runInAction } from '@globals/mobx';
import { AccessReviewPersonnelPageHeaderModel } from '@models/access-review-personnel';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { AccessReviewPersonnelView } from '@views/access-review-personnel';

export const meta: MetaFunction = () => [{ title: t`Access Review Personnel` }];

// Batch data loading in a single action
const loadAllData = action((applicationId: unknown, clientType: string) => {
    runInAction(() => {
        activeAccessReviewsApplicationsUserController.setApplicationId(
            Number(applicationId),
        );
        activeAccessReviewsApplicationsUserController.setClientType(
            clientType as ClientTypeEnum,
        );

        // Load data sequentially
        activeAccessReviewsApplicationsUserController.loadAccessReviewApplicationUsersConnections();
        sharedAccessReviewApplicationGroupsController.loadAccessReviewApplicationGroupsForPersonnel(
            {
                applicationId: Number(applicationId),
            },
        );
        activeAccessReviewsApplicationsController.loadAccessReviewApplicationDetails(
            Number(applicationId),
        );
        activeAccessReviewsApplicationsUserController.loadAccessReviewApplicationUsers(
            {
                pagination: {
                    pageIndex: 0,
                    pageSize: 10,
                    pageSizeOptions: [DEFAULT_PAGE_SIZE],
                },
                globalFilter: { search: '', filters: {} },
                sorting: [],
            },
        );
    });
});

// Use the action decorator from MobX instead of clientLoader
export const clientLoader = action(({ params }: LoaderFunctionArgs) => {
    const { applicationId, clientType } = params;

    if (!applicationId || !clientType) {
        throw new Error(t`Application ID and client type are required`);
    }

    loadAllData(applicationId, clientType);

    return {
        pageHeader: new AccessReviewPersonnelPageHeaderModel(),
        tabs: [],
    };
});

const UserAccessReviewApplicationsPersonnel = (): React.JSX.Element => {
    return (
        <AccessReviewPersonnelView
            data-testid="UserAccessReviewApplicationsPersonnel"
            data-id="7jgew8ty"
        />
    );
};

export default UserAccessReviewApplicationsPersonnel;
