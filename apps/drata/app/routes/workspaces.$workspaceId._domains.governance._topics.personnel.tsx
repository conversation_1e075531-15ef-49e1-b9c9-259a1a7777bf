import { action } from '@globals/mobx';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Personnel',
        },
        tabs: [
            {
                topicPath: 'governance/personnel/personnel',
                label: 'Personnel',
            },
            {
                topicPath: 'governance/personnel/exclusions',
                label: 'Exclusions',
            },
        ],
    };
});

const Personnel = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Personnel" data-id="MviHNu9x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Personnel;
