import { isNil } from 'lodash-es';
import {
    sharedPolicyDetailsController,
    sharedPolicyFilesController,
} from '@controllers/policies';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunctionArgs, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Policy Details' }];
};

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        const { policyId } = params;

        if (isNil(policyId)) {
            throw new Error('Policy ID is required');
        }

        sharedPolicyDetailsController.loadPolicyDetails(Number(policyId));
        sharedPolicyDetailsController.loadPolicyControlsAssociated(
            Number(policyId),
        );
        sharedPolicyDetailsController.loadPolicyFrameworksAssociated(
            Number(policyId),
        );

        sharedPolicyFilesController.loadPdfDownload(Number(policyId));
        sharedPolicyFilesController.loadTemplateHtml(Number(policyId));

        return {
            tabs: [
                {
                    id: 'governance.policy-details.policy',
                    topicPath: `governance/policies/${policyId}/policy`,
                    label: 'Policy',
                },
                {
                    id: 'governance.policy-details.overview',
                    topicPath: `governance/policies/${policyId}/overview`,
                    label: 'Overview',
                },

                {
                    id: 'governance.policy-details.history',
                    topicPath: `governance/policies/${policyId}/history`,
                    label: 'Version history',
                },
            ],
        };
    },
);

const PolicyDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PolicyDetails"
            data-id="Xw6tBZCP"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default PolicyDetails;
