import { isNil } from 'lodash-es';
import { sharedPolicyBuilderController } from '@controllers/policy-builder';
import { action } from '@globals/mobx';
import { PolicyBuilderHeaderModel } from '@models/policy-builder';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunctionArgs, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Policy Builder' }];
};

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        const { policyId } = params;

        if (isNil(policyId)) {
            throw new Error('Policy ID is required');
        }

        sharedPolicyBuilderController.loadPolicyBuilder(Number(policyId));

        return {
            pageHeader: new PolicyBuilderHeaderModel(),
            tabs: [
                {
                    id: 'governance.policies.builder.overview',
                    topicPath: `governance/policies/builder/${policyId}/overview`,
                    label: 'Overview',
                },
                {
                    id: 'governance.policies.builder.policy',
                    topicPath: `governance/policies/builder/${policyId}/policy`,
                    label: 'Policy',
                },
                {
                    id: 'governance.policies.builder.version-history',
                    topicPath: `governance/policies/builder/${policyId}/version-history`,
                    label: 'Version History',
                },
                {
                    id: 'governance.policies.builder.workflows',
                    topicPath: `governance/policies/builder/${policyId}/workflows`,
                    label: 'Workflows',
                },
            ],
        };
    },
);

const PolicyBuilder = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PolicyBuilder"
            data-id="policy-builder"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default PolicyBuilder;
