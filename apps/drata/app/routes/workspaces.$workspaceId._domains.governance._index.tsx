import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const GovernanceIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/access-review`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="GovernanceIndex" data-id="zb_L36pq"></div>;
};

export default GovernanceIndex;
