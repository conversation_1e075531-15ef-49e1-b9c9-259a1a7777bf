import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const ConnectionsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/all`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="ConnectionsIndex" data-id="yJ18I5If"></div>;
};

export default ConnectionsIndex;
