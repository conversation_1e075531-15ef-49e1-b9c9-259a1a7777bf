import { isEmpty } from 'lodash-es';
import { MainAppTopicsNavComponent } from '@components/main-app-topics-nav';
import { routeController } from '@controllers/route';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet, useLocation } from '@remix-run/react';
import { ContentNavigationMenu } from '@ui/page-content';
import { PageHeaderUi } from '@ui/page-header';

export const meta: MetaFunction = () => [{ title: 'DomainsFlat' }];

export type Domain = 'risk' | 'compliance' | 'compliance_controls' | 'fallback';

const DomainsFlat = observer((): React.JSX.Element => {
    const location = useLocation();
    const { contentNavItems } = routeController;

    return (
        <Stack
            data-id="domains-flat-parent-stack"
            display="flex"
            direction="row"
            height="100%"
            width="100%"
            minHeight="0"
            minWidth="0"
        >
            <MainAppTopicsNavComponent />

            <Stack
                data-id="domains-flat-child-stack"
                display="flex"
                direction="column"
                height="100%"
                width="100%"
                minHeight="0"
                minWidth="0"
                overflowY="scroll"
                position="relative"
            >
                <PageHeaderUi />

                {!isEmpty(contentNavItems) && (
                    <ContentNavigationMenu
                        data-id="page-header-ui-content-navigation-menu"
                        value={location.pathname}
                        items={contentNavItems}
                    />
                )}

                <Stack
                    data-id="domains-flat-content-stack"
                    width="100%"
                    minWidth="0"
                    p="3xl"
                    direction="column"
                >
                    <Outlet />
                </Stack>
            </Stack>
        </Stack>
    );
});

export default DomainsFlat;
