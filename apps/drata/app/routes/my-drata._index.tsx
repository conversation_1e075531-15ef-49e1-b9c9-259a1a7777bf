import { sharedMyDrataAgentController } from '@controllers/my-drata-agent';
import { sharedMyDrataBackgroundCheckController } from '@controllers/my-drata-background-check';
import { sharedMyDrataDevicesController } from '@controllers/my-drata-devices';
import { sharedMyDrataHipaaTrainingController } from '@controllers/my-drata-hipaa-training';
import { sharedMyDrataNistAiTrainingController } from '@controllers/my-drata-nist-ai-training';
import { sharedMyDrataPoliciesController } from '@controllers/my-drata-policies';
import { sharedMyDrataSecurityTrainingController } from '@controllers/my-drata-security-training';
import { action } from '@globals/mobx';
import { MyDrataPageHeaderModel } from '@models/my-drata';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { MyDrataView } from '@views/my-drata';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'My Drata' }];
};

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    sharedMyDrataSecurityTrainingController.load();
    sharedMyDrataPoliciesController.load();
    sharedMyDrataNistAiTrainingController.load();
    sharedMyDrataDevicesController.load();
    sharedMyDrataHipaaTrainingController.load();
    sharedMyDrataBackgroundCheckController.load();
    sharedMyDrataAgentController.load();

    return {
        pageHeader: new MyDrataPageHeaderModel(),
    };
});

export const MyDrata = (): React.JSX.Element => {
    return <MyDrataView data-testid="MyDrata" data-id="MyDrata" />;
};

export default MyDrata;
