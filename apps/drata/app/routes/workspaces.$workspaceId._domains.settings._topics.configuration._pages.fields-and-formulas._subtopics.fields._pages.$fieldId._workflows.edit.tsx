import { action } from '@globals/mobx';
import { FieldsAndFormulasFieldPageHeaderModel } from '@models/fields-and-formulas-field';
import type { MetaFunction } from '@remix-run/node';
import { SettingsFieldsAndFormulasFieldsEditView } from '@views/settings-fields-and-formulas-fields-edit';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Edit Field' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: new FieldsAndFormulasFieldPageHeaderModel(),
    };
});

export const SettingsFieldsAndFormulasFormulas = (): React.JSX.Element => {
    return (
        <SettingsFieldsAndFormulasFieldsEditView
            data-testid="SettingsFieldsAndFormulasFormulas"
            data-id="qtJ8bGIk"
        />
    );
};

export default SettingsFieldsAndFormulasFormulas;
