import { activeAssetController } from '@controllers/asset';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import {
    AssetDetailsActionStack,
    AssetDetailsView,
    buildDeviceComplianceKVP,
    buildOwnerKVP,
} from '@views/asset-details';

class PageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'asset-details-page-header';

    get title(): string {
        return activeAssetController.assetDetails?.name || '';
    }

    get keyValuePairs(): KeyValuePairProps[] {
        return [
            ...buildDeviceComplianceKVP(activeAssetController.deviceCompliance),
            ...buildOwnerKVP(activeAssetController.assetDetails?.owner),
        ];
    }

    get breadcrumbs() {
        return [{ label: t`Asset`, pathname: 'risk/assets' }];
    }

    get actionStack() {
        return <AssetDetailsActionStack />;
    }
}

export const clientLoader: ClientLoaderFunction = action(({ params }) => {
    activeAssetController.loadAsset(params.assetId);
    activeAssetController.loadConnections();

    return {
        pageHeader: new PageHeaderModel(),
    };
});

export const meta: MetaFunction = () => [
    {
        title: 'Assets Details',
    },
];

const RiskAssetDetails = (): React.JSX.Element => (
    <AssetDetailsView data-id="20JOjm5v" data-testid="RiskAssetDetails" />
);

export default RiskAssetDetails;
