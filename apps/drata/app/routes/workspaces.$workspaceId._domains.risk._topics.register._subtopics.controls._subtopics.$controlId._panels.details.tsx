// import { RiskRegisterDetailsPanelView } from '@views/risk-register-details-panel';
import { RouteLandmark } from '@ui/layout-landmarks';

const RiskRegisterControlsDetailsPanel = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="RiskRegisterControlsDetailsPanel"
            data-id="eGFV7hnx"
        >
            <h1>RiskRegisterControlsDetailsPanel</h1>
            <h2>TODO: This needs some help. Talk to chris</h2>
        </RouteLandmark>
        // <RiskRegisterDetailsPanelView
        //     data-testid="RiskRegisterControlsDetailsPanel"
        //     data-id="ezN3f6tB"
        // />
    );
};

export default RiskRegisterControlsDetailsPanel;
