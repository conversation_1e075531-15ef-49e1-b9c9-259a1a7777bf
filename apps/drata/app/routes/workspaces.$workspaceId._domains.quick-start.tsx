import { sharedFeatureAnnouncementDismissalsController } from '@controllers/feature-announcement-dismissals';
import { sharedOnboardingController } from '@controllers/onboarding';
import { action, observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Quick Start' }];
};

export const clientLoader = action((): ClientLoader => {
    sharedOnboardingController.adminOnboardingDataQuery.load();
    sharedFeatureAnnouncementDismissalsController.featureDismissalQuery.load({
        query: { type: 'WELCOME_EXPERIENCE_ACTIVITY' },
    });

    return {
        pageHeader: {
            title: 'Quick Start',
        },
    };
});

const QuickStart = observer((): React.JSX.Element => {
    return (
        <RouteLandmark as="article" data-testid="QuickStart" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
});

export default QuickStart;
