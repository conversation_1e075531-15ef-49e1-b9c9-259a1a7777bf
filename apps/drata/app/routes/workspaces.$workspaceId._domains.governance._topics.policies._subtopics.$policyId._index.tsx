import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const PolicyIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/policy`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="PolicyIndex" data-id="zb_L36pq"></div>;
};

export default PolicyIndex;
