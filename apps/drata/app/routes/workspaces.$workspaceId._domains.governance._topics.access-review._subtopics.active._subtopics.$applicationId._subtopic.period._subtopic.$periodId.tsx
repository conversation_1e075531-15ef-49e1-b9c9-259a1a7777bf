import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { applicationId, periodId } = params;

        return {
            tabs: [
                {
                    id: 'governance.access-review.application.periods',
                    topicPath: `governance/access-review/active/${applicationId}/period/${periodId}/overview`,
                    label: t`Overview`,
                },
                {
                    id: 'governance.access-review.application.personnel',
                    topicPath: `governance/access-review/active/${applicationId}/period/${periodId}/personnel`,
                    label: t`Personnel`,
                },
            ],
        };
    },
);

const ReviewApplication = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="ReviewApplication"
            data-id="PQKKCe-u"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default ReviewApplication;
