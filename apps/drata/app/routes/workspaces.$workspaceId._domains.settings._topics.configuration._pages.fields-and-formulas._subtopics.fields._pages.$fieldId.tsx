import { sharedFieldsAndFormulasFieldDetailsController } from '@controllers/fields-and-formulas-field-details';
import { action } from '@globals/mobx';
import { FieldsAndFormulasFieldPageHeaderModel } from '@models/fields-and-formulas-field';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = ({ params }) => {
    return [{ title: `Settings - Field: ${params.fieldId}` }];
};
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        if (!params.fieldId) {
            throw new Error('field ID is required');
        }

        sharedFieldsAndFormulasFieldDetailsController.fieldDetails.load({
            path: { customFieldId: parseInt(params.fieldId) },
        });

        return {
            pageHeader: new FieldsAndFormulasFieldPageHeaderModel(),
            tabs: [],
        };
    },
);

export const SettingsFieldsAndFormulasFormulas = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsFieldsAndFormulasFormulas"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsFieldsAndFormulasFormulas;
