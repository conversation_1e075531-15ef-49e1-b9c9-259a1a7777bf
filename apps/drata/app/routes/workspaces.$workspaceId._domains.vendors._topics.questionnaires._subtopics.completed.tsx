import { ActionStack } from '@cosmos/components/action-stack';
import { Organization } from '@cosmos-lab/components/organization';
import type { MetaFunction } from '@remix-run/node';
import { VENDOR_HUB_QUESTIONNAIRE_COMPLETED_HEADER_ACTIONS } from '@views/vendor-hub-questionnaire';
import { VendorHubQuestionnaireCompletedView } from '@views/vendors-questionnaire-completed';

export const meta: MetaFunction = () => [{ title: 'Vendors Settings' }];

const isQuestionnaireCompleted = true;

export const handle = {
    overrides: {
        pageHeader: {
            pageId: 'vendor-hub-questionnaire-page',
            actionStack: (
                <ActionStack
                    data-id="vendor-hub-questionnaire-action-stack"
                    stacks={[
                        {
                            actions:
                                VENDOR_HUB_QUESTIONNAIRE_COMPLETED_HEADER_ACTIONS,
                            id: 'vendor-hub-questionnaire-action-stack',
                        },
                    ]}
                />
            ),
            slot: (
                <Organization
                    imgSrc="https://drata.com/images/favicon-32x32.png"
                    imgAlt="vendor logo"
                />
            ),
        },
    },
};
const TempVendorHubQuestionnaireComplete = (): React.JSX.Element => {
    return (
        <VendorHubQuestionnaireCompletedView
            isQuestionnaireCompleted={isQuestionnaireCompleted}
            data-testid="TempVendorHubQuestionnaireComplete"
            data-id="xIbiZtdg"
        />
    );
};

export default TempVendorHubQuestionnaireComplete;
