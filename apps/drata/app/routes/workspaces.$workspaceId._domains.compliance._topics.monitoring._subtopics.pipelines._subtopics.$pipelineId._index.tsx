import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const MonitoringPipelineDetailsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/runs/123`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return (
        <div
            data-testid="MonitoringPipelineDetailsIndex"
            data-id="zb_L36pq"
        ></div>
    );
};

export default MonitoringPipelineDetailsIndex;
