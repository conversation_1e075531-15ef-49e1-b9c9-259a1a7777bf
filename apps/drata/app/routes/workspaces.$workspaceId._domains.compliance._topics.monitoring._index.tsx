import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const MonitoringIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/monitors`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="MonitoringIndex" data-id="zb_L36pq"></div>;
};

export default MonitoringIndex;
