import type { MetaFunction } from '@remix-run/node';
import { PoliciesBuilderOverviewView } from '@views/policies-builder-overview';

export const meta: MetaFunction = () => [{ title: 'Policy details overview' }];

const PolicyOverview = (): React.JSX.Element => {
    return (
        <PoliciesBuilderOverviewView
            data-testid="PolicyOverview"
            data-id="Mf7gOwhB"
        />
    );
};

export default PolicyOverview;
