import { t } from '@globals/i18n/macro';
import { FrameworkRequirementsUploadView } from '@views/framework-requirements-upload';
import type { ClientLoader } from '../types';

export const clientLoader = (): ClientLoader => {
    return {
        pageHeader: {
            title: t`Upload framework requirements`,
        },
    };
};

const FrameworkRequirementUpload = (): React.JSX.Element => {
    return (
        <FrameworkRequirementsUploadView
            data-testid="FrameworkRequirementUpload"
            data-id="rkJ1-kbQ"
        />
    );
};

export default FrameworkRequirementUpload;
