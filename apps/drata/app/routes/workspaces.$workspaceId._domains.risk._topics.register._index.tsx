import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const RiskRegisterIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/library`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="RiskRegisterIndex" data-id="zb_L36pq"></div>;
};

export default RiskRegisterIndex;
