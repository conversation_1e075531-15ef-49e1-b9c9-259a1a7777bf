import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { action } from '@globals/mobx';
import { RequestDetailsPageHeaderModel } from '@models/request-details-page-header';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { requestId, auditId, clientId } = params;

        sharedCustomerRequestDetailsController.requestId = Number(requestId);

        sharedAuditHubController.loadRequestOwnersForAudit(auditId as string);

        sharedCustomerRequestDetailsController.customerQuery.load({
            path: { customerRequestId: Number(requestId) },
        });

        return {
            pageHeader: new RequestDetailsPageHeaderModel(),
            tabs: [
                {
                    id: 'overview',
                    label: 'Overview',
                    topicPath: `clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/overview`,
                },
                {
                    id: 'evidence',
                    label: 'Evidence',
                    topicPath: `clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/evidence`,
                },
                {
                    id: 'controls',
                    label: 'Controls',
                    topicPath: `clients/${clientId}/audits/${auditId}/evidence-requests/${requestId}/controls`,
                },
            ],
            utilities: {
                utilitiesList: ['notes_for_controls'], // TODO: Change this to the right utility https://drata.atlassian.net/browse/ENG-69330
            },
            layout: {
                centered: false,
            },
        };
    },
);

const AuditHubRequest = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditHubRequest"
            data-id="CY5rwNFa"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AuditHubRequest;
