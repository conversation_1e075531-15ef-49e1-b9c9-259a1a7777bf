import {
    dataDiverge1Strong,
    dataDiverge10Strong,
} from '@cosmos/constants/tokens';
import { DataPosture } from '@cosmos-lab/components/data-posture';
import { action } from '@globals/mobx';
import { RouteLandmark } from '@ui/layout-landmarks';
import { ConnectionManageAccountsObservabilityView } from '@views/connections-manage-accounts-observability';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Manage Observability Accounts',
            slot: (
                <DataPosture
                    size="lg"
                    boxes={[
                        {
                            color: dataDiverge1Strong,
                            id: 'linked',
                            value: 2,
                        },
                        {
                            color: dataDiverge10Strong,
                            id: 'unlinked',
                            value: 1,
                        },
                    ]}
                />
            ),
        },
    };
});

const ConnectionsObservability = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionsObservability"
            data-id="1L7VBxGy"
        >
            <ConnectionManageAccountsObservabilityView />
        </RouteLandmark>
    );
};

export default ConnectionsObservability;
