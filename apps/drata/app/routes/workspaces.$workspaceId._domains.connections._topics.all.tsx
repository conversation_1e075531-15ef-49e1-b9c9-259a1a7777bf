import { sharedConnectionsController } from '@controllers/connections';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Connections' }];

export const clientLoader = action((): ClientLoader => {
    sharedConnectionsController.allConfiguredConnectionsQuery.load();

    return {
        pageHeader: {
            title: 'All Connections',
        },
        tabs: [
            {
                topicPath: 'connections/all/active',
                label: 'Active',
            },
            {
                topicPath: 'connections/all/available',
                label: 'Available',
            },
        ],
    };
});

const AllConnections = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AllConnections"
            data-id="ug7Tv5x2"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AllConnections;
