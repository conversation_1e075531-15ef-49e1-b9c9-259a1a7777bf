import type { MetaFunction } from '@remix-run/node';
import { MonitoringDetailsOverviewView } from '@views/monitoring-details-overview';

export const meta: MetaFunction = () => [
    { title: 'Monitoring code details overview' },
];

const MonitoringCodeDetailsOverview = (): React.JSX.Element => {
    return (
        <MonitoringDetailsOverviewView
            data-testid="MonitoringCodeDetailsOverview"
            data-id="BIlnuYYz"
        />
    );
};

export default MonitoringCodeDetailsOverview;
