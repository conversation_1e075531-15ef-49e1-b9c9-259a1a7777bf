import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const PoliciesIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/active`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="PoliciesIndex" data-id="zb_L36pq"></div>;
};

export default PoliciesIndex;
