import { useEffect } from 'react';
import { observer } from '@globals/mobx';
import { useLocation, useNavigate } from '@remix-run/react';

const AccessReviewIndex = observer((): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/applications`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="AccessReviewIndex" data-id="zb_L36pq"></div>;
});

export default AccessReviewIndex;
