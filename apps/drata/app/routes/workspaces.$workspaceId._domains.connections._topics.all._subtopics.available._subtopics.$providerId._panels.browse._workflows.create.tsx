import { sharedOrganizationDetailsInfoController } from '@controllers/companies';
import { action } from '@globals/mobx';
import { isProviderType } from '@globals/providers';
import { CreateConnectionPageHeaderModel } from '@models/connections';
import type {
    ClientLoaderFunction,
    ClientLoaderFunctionArgs,
} from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import { SettingsConnectionsCreateView } from '@views/settings-connections-create';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        const providerId = params.providerId?.toUpperCase() ?? '';

        if (!isProviderType(providerId)) {
            throw new Error('Invalid provider ID');
        }

        sharedOrganizationDetailsInfoController.getOrganizationDetailsInfo();

        return {
            pageHeader: new CreateConnectionPageHeaderModel(providerId),
            tabs: [],
        };
    },
);

const CreateConnection = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="CreateConnection"
            data-id="pDRTOwbF"
        >
            <SettingsConnectionsCreateView />
        </RouteLandmark>
    );
};

export default CreateConnection;
