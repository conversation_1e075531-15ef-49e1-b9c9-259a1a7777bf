import { activeEdrController } from '@controllers/edr';
import { action } from '@globals/mobx';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import { ConnectionsManageAccountsDevicesView } from '@views/connections-manage-accounts-devices';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Connections - Manage Account - Devices' }];
};
export const clientLoader = action(
    ({ request }: LoaderFunctionArgs): ClientLoader => {
        const { url: requestUrl = '' } = request;

        const url = new URL(requestUrl);

        const page = Number(url.searchParams.get('page') ?? 1);

        activeEdrController.assets.load({
            query: { page },
        });

        return {
            pageHeader: {
                title: 'Review Linked Devices',
            },
        };
    },
);

const ConnectionsDevices = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionsDevices"
            data-id="3wf8UIY6"
        >
            <ConnectionsManageAccountsDevicesView />
        </RouteLandmark>
    );
};

export default ConnectionsDevices;
