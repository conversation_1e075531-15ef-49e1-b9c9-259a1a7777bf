import type { MetaFunction } from '@remix-run/node';
import { useParams } from '@remix-run/react';

export const meta: MetaFunction = () => [{ title: 'TopicsIndex' }];

const TopicsIndex = (): React.JSX.Element => {
    const { workspaceId, domainId } = useParams();

    return (
        <div
            style={{ background: '#f00', padding: '16px' }}
            data-testid="TopicsIndex"
            data-id="jPwZmxJb"
        >
            <div style={{ background: '#0f0' }}>
                TopicsIndex: {workspaceId} : {domainId}
            </div>
        </div>
    );
};

export default TopicsIndex;
