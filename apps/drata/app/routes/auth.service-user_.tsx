import type { MetaFunction } from '@remix-run/node';
import { LoginLayoutView } from '@views/login';
import { LoginServiceUserView } from '@views/login-service-user';

export const meta: MetaFunction = () => [{ title: 'Drata' }];

const LoginServiceUserPage = (): React.JSX.Element => {
    return (
        <LoginLayoutView data-testid="LoginServiceUserPage" data-id="uVwkrhyx">
            <LoginServiceUserView />
        </LoginLayoutView>
    );
};

export default LoginServiceUserPage;
