import type { MetaFunction } from '@remix-run/node';
import { VendorsProspectiveView } from '@views/vendors-prospective';

export const meta: MetaFunction = () => [{ title: 'Vendors Prospective' }];

const VendorsProspective = (): React.JSX.Element => {
    return (
        <VendorsProspectiveView
            data-testid="VendorsProspective"
            data-id="Bwg9WWyA"
        />
    );
};

export default VendorsProspective;
