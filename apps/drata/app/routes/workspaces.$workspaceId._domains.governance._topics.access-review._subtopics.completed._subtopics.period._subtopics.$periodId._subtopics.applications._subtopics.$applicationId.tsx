import { isNil } from 'lodash-es';
import { activeAccessReviewCompletedDetailsController } from '@controllers/access-reviews';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { AccessReviewCompletedDetailsPageHeaderModel } from '@models/access-review-completed-details-header';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AccessReviewApplicationDetailsCompleted } from '@views/access-review-application-details';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Access Review Details` }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        if (isNil(params.periodId) || isNil(params.applicationId)) {
            throw new Error(t`Period ID or App ID params are missing`);
        }

        const { setIds, accessReviewActivePeriods } =
            activeAccessReviewCompletedDetailsController;

        setIds(Number(params.periodId), Number(params.applicationId));

        activeAccessReviewCompletedDetailsController.loadAccessReviewCompletedApplicationDetails(
            {
                periodId: params.periodId,
                reviewAppId: params.applicationId,
            },
        );

        accessReviewActivePeriods.load();

        return {
            pageHeader: new AccessReviewCompletedDetailsPageHeaderModel(),
            utilities: {
                utilitiesList: [
                    'notes_for_completed_application_on_completed_review',
                ],
            },
        };
    },
);

const AccessReviewCompletedReviewDetails = (): React.JSX.Element => {
    return (
        <AccessReviewApplicationDetailsCompleted
            data-testid="AccessReviewCompletedReviewDetails"
            data-id="K0seVMdK"
        />
    );
};

export default AccessReviewCompletedReviewDetails;
