import { sharedAccessReviewController } from '@controllers/access-reviews';
import { action } from '@globals/mobx';
import { AccessReviewCompletedReviewsView } from '@views/access-review-completed-reviews';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    sharedAccessReviewController.accessReviewComplete.load();

    return null;
});

const AccessReviewCompleted = (): React.JSX.Element => {
    return (
        <AccessReviewCompletedReviewsView
            data-testid="AccessReviewCompleted"
            data-id="TpfTDn3J"
        />
    );
};

export default AccessReviewCompleted;
