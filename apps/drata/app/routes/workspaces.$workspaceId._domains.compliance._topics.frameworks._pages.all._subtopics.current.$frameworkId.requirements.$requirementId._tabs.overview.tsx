import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import { FrameworksOverviewView } from '@views/frameworks-overview';

export const meta: MetaFunction = () => [{ title: t`Framework overview` }];

const FrameworkOverview = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="FrameworkOverview"
            data-id="jPwZmxJb"
        >
            <FrameworksOverviewView />
        </RouteLandmark>
    );
};

export default FrameworkOverview;
