import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import { sharedRolesAdministrationController } from '@controllers/role-administration';
import { action } from '@globals/mobx';
import { AuditorClientPageHeaderModel } from '@models/auditor-client-page-header';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditorAuditsView } from '@views/auditor-audits-view';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Audit Hub - Client' }];

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { clientId } = params;

        sharedRolesAdministrationController.loadUserRoles();
        sharedAuditHubAuditorClientAuditController.loadAuditorDetails();

        return {
            pageHeader: new AuditorClientPageHeaderModel({
                clientId: clientId as string,
            }),
            utilities: {
                utilitiesList: [],
            },
        };
    },
);

const AuditorClientPage = (): React.JSX.Element => {
    return (
        <AuditorAuditsView data-testid="AuditorClientPage" data-id="lHwpZI3A" />
    );
};

export default AuditorClientPage;
