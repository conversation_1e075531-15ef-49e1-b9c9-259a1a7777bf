import type { MetaFunction } from '@remix-run/node';
import { LoginLayoutView } from '@views/login';
import { LoginAuditorView } from '@views/login-auditor';

export const meta: MetaFunction = () => [{ title: 'Drata' }];

export const LoginAuditorPage = (): React.JSX.Element => {
    return (
        <LoginLayoutView data-testid="LoginAuditorPage" data-id="YJNDS3A3">
            <LoginAuditorView />
        </LoginLayoutView>
    );
};

export default LoginAuditorPage;
