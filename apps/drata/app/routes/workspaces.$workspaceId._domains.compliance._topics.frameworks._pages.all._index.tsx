import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const AllFrameworksIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/current`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="AllFrameworksIndex" data-id="zb_L36pq"></div>;
};

export default AllFrameworksIndex;
