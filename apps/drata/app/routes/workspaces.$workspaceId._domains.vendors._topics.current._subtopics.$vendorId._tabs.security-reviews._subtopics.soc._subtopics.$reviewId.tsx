import {
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { SocReportPageHeaderModel } from '@models/vendor-security-reviews';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewSocView } from '@views/vendors-profile-security-review-soc';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Security Reviews Soc' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, reviewId } = params;

        if (
            !reviewId ||
            isNaN(Number(reviewId)) ||
            !vendorId ||
            isNaN(Number(vendorId))
        ) {
            throw new Error('reviewId or vendorId are invalid');
        }

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(reviewId) },
        });

        sharedVendorsSecurityReviewDocumentsController.loadSecurityReviewSOCDocument(
            {
                path: { id: Number(reviewId) },
            },
        );

        sharedVendorsSecurityReviewDocumentsController.setVendorId(
            Number(vendorId),
        );

        sharedVendorsProfileQuestionnaireAISummaryController.saveSocSummary();

        return {
            pageHeader: new SocReportPageHeaderModel(),
        };
    },
);

const VendorsCurrentSecurityReviewsSoc = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewSocView
            data-testid="VendorsCurrentSecurityReviewsSoc"
            data-id="XQSZpMMh"
        />
    );
};

export default VendorsCurrentSecurityReviewsSoc;
