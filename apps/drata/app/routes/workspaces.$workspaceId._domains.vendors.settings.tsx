import type { MetaFunction } from '@remix-run/node';
import { VendorsSettingsView } from '@views/vendors-settings';

export const meta: MetaFunction = () => [{ title: 'Vendors Settings' }];

export const handle = {
    overrides: {
        pageHeader: {
            title: 'TEMP Modal Settings',
            pageId: 'vendors-modal-temp-settings-page',
        },
    },
};

const VendorsSettings = (): React.JSX.Element => {
    return (
        <VendorsSettingsView data-testid="VendorsSettings" data-id="jP_-SXBm" />
    );
};

export default VendorsSettings;
