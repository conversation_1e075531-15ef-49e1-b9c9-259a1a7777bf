import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { action } from '@globals/mobx';
import { AuditDetailsOverviewHeaderModel } from '@models/audit-details-overview';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AuditDetailsRelatedControlsView } from '@views/audit-details-related-controls';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { auditId, requestId } = params;

        if (!Number(requestId)) {
            throw new Error('Invalid requestId');
        }

        if (!auditId) {
            throw new Error(
                'params.auditId does not exist, and that is a catastrophe. Call the police',
            );
        }

        sharedCustomerRequestDetailsController.customerQuery.load({
            path: { customerRequestId: Number(requestId) },
        });

        sharedCustomerRequestDetailsController.auditorId = auditId;
        sharedCustomerRequestDetailsController.auditorFrameworkId =
            String(requestId);

        return {
            pageHeader: new AuditDetailsOverviewHeaderModel(),
        };
    },
);

export const meta: MetaFunction = () => {
    return [{ title: 'Audit related controls' }];
};

const AuditRelatedControls = (): React.JSX.Element => {
    return (
        <AuditDetailsRelatedControlsView
            data-testid="AuditRelatedControls"
            data-id="t73zVMg9"
        />
    );
};

export default AuditRelatedControls;
