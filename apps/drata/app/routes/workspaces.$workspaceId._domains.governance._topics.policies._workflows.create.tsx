import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { PoliciesAddView } from '@views/policies-add';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Create custom policy' }];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Policies',
            pageId: 'policies-create',
        },
        tabs: [],
    };
});

const CreatePolicyWorkflow = (): React.JSX.Element => {
    return (
        <PoliciesAddView
            data-testid="CreatePolicyWorkflow"
            data-id="RPUzAeUB"
        />
    );
};

export default CreatePolicyWorkflow;
