import { sharedAuthController } from '@controllers/auth';
import { sharedCustomerRequestDetailsController } from '@controllers/customer-request-details';
import { snackbarController } from '@controllers/snackbar';
import { action, observer } from '@globals/mobx';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { clientId } = params;

        if (!sharedAuthController.email || !sharedAuthController.region) {
            snackbarController.addSnackbar({
                id: 'auditor-settings-update-error',
                props: {
                    title: 'Email and region are required',
                    description:
                        'An error occurred while accesing to client`s page. Try again later.',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });
        }

        if (
            clientId &&
            sharedAuthController.email &&
            sharedAuthController.region
        ) {
            sharedAuthController.attemptLogin(
                sharedAuthController.email,
                sharedAuthController.region,
                { clientId }, // initiate a secondary authentication flow with tenant token
            );

            sharedCustomerRequestDetailsController.clientId = clientId;
        }

        return null;
    },
);

const AuditHubClient = observer((): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditHubClient"
            data-id="CY5rwNFa"
        >
            {sharedAuthController.isAttemptingLogin && (
                // TODO: replace with loading component https://drata.atlassian.net/browse/ENG-69894
                <div data-id="loading">Loading...</div>
            )}
            {sharedAuthController.hasAttemptedLogin && <Outlet />}
        </RouteLandmark>
    );
});

export default AuditHubClient;
