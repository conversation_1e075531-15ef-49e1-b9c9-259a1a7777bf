import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'TopicIdFlat' }];

const TopicIdFlat = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="TopicIdFlat"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default TopicIdFlat;
