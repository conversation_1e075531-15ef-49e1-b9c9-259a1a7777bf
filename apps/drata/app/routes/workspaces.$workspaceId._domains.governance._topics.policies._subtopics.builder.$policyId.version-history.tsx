const PolicyBuilderVersionHistory = (): React.JSX.Element => {
    return (
        <div
            data-testid="PolicyBuilderVersionHistory"
            data-id="policy-builder-version-history"
        >
            <h2>Policy Builder - Version History Tab</h2>
            <p>
                This will contain the version history datatable and management
                functionality.
            </p>
        </div>
    );
};

export default PolicyBuilderVersionHistory;
