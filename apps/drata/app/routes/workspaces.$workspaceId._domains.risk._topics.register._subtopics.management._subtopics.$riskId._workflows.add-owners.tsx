import type { MetaFunction } from '@remix-run/node';
import { RiskRegisterAddOwnersView } from '@views/risk-register-add-owners';

export const meta: MetaFunction = () => {
    return [{ title: 'Risk Register - Add Owners' }];
};

const RiskRegisterAddOwners = (): React.JSX.Element => {
    return (
        <RiskRegisterAddOwnersView
            data-testid="RiskRegisterAddOwners"
            data-id="o98bsN7P"
        />
    );
};

export default RiskRegisterAddOwners;
