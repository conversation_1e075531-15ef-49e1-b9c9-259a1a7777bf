import {
    sharedEventsController,
    sharedEventsOwnersController,
} from '@controllers/events';
import { sharedUsersController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Events` }];
};

export const clientLoader = action((): ClientLoader => {
    sharedEventsController.loadEvents();
    sharedUsersController.usersList.load();
    sharedEventsOwnersController.loadEventsOwners();

    return {
        pageHeader: {
            title: t`Events`,
        },
    };
});

const Events = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Events" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Events;
