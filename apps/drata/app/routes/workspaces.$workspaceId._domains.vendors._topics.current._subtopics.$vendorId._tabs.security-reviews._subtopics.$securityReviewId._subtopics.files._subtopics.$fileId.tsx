import { sharedUtilitiesObservationsController } from '@controllers/utilities';
import {
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsSecurityReviewFileController,
    sharedVendorsSecurityReviewObservationsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { SecurityReviewFilePageHeaderModel } from '@models/vendor-security-reviews';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewFilesView } from '@views/vendors-profile-security-review-files';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Security Review File' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { securityReviewId, fileId, vendorId } = params;

        if (!securityReviewId || isNaN(Number(securityReviewId))) {
            throw new Error('Invalid securityReviewId');
        }

        if (!fileId || isNaN(Number(fileId))) {
            throw new Error('Invalid fileId');
        }

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsSecurityReviewFileController.setVendorId(Number(vendorId));

        sharedVendorsSecurityReviewFileController.setSecurityReviewId(
            Number(securityReviewId),
        );

        sharedVendorsSecurityReviewFileController.loadPdfDownloadUrl(
            Number(fileId),
        );

        sharedVendorsSecurityReviewFileController.loadSecurityReviewDocument(
            Number(fileId),
        );

        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(securityReviewId) },
            },
        );

        sharedUtilitiesObservationsController.openUtility();

        sharedVendorsProfileQuestionnaireAISummaryController.saveSocSummary(
            Number(fileId),
        );

        return {
            pageHeader: new SecurityReviewFilePageHeaderModel(),
            utilities: {
                utilitiesList: ['observations'],
            },
        };
    },
);

const VendorsCurrentSecurityReviewFile = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewFilesView
            data-testid="VendorsCurrentSecurityReviewFile"
            data-id="TsaCYRBa"
        />
    );
};

export default VendorsCurrentSecurityReviewFile;
