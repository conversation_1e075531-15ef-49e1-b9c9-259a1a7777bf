import { sharedFrameworkCreateController } from '@controllers/frameworks';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    sharedFrameworkCreateController.load();
    sharedCurrentCompanyController.load();

    return {
        contentNav: {
            tabs: [
                {
                    topicPath: 'compliance/frameworks/all/current',
                    label: t`Current frameworks`,
                },
                {
                    topicPath: 'compliance/frameworks/all/available',
                    label: t`Available for your company`,
                },
            ],
        },
    };
});

const AllFrameworksPage = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AllFrameworksPage"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AllFrameworksPage;
