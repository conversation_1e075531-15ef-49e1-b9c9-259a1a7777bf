import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const LibraryTestIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="LibraryTestIndex" data-id="S64bWazY"></div>;
};

export default LibraryTestIndex;
