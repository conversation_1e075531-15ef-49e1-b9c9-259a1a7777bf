import {
    sharedAccessReviewController,
    sharedActiveAccessReviewPeriodsController,
} from '@controllers/access-reviews';
import {
    sharedUsersController,
    sharedUsersInfiniteController,
} from '@controllers/users';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Access Review` }];
};

class AccessReviewPageContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get title(): string {
        return t`Access Review`;
    }
}

export const clientLoader = action((): ClientLoader => {
    const { isUserAccessReviewEnabled } = sharedFeatureAccessModel;

    sharedAccessReviewController.accessReview.load();
    sharedActiveAccessReviewPeriodsController.activeAccessReviewPeriods.load();
    sharedUsersController.loadReviewerUsers();
    sharedUsersInfiniteController.loadUsers({
        roles: ['ADMIN', 'REVIEWER'],
    });

    const tabs = [
        {
            id: 'governance.access-review.applications',
            topicPath: `governance/access-review/applications`,
            label: t`Applications`,
        },
        {
            id: 'governance.access-review.active',
            topicPath: `governance/access-review/active`,
            label: t`Active reviews`,
        },
        {
            id: 'governance.access-review.completed',
            topicPath: `governance/access-review/completed`,
            label: t`Completed reviews`,
        },
    ];

    if (!isUserAccessReviewEnabled) {
        return {
            pageHeader: new AccessReviewPageContentNavModel(),
            tabs: undefined,
        };
    }

    return {
        pageHeader: new AccessReviewPageContentNavModel(),
        tabs,
    };
});

const AccessReview = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AccessReview"
            data-id="MviHNu9x"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AccessReview;
