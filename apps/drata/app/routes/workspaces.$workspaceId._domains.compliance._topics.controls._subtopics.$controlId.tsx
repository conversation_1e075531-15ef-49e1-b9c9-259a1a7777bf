import {
    sharedControlCustomFieldsController,
    sharedControlDetailsOrchestratorController,
    sharedControlTicketsController,
} from '@controllers/controls';
import { action } from '@globals/mobx';
import { ComplianceControlsPageHeaderModel } from '@models/compliance-controls';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Control Detail Overview' }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { controlId } = params;

        if (!Number(controlId)) {
            throw new Error('Control ID is required.');
        }

        sharedControlDetailsOrchestratorController.load(Number(controlId));

        sharedControlCustomFieldsController.load(Number(controlId));
        sharedControlTicketsController.load(Number(controlId));

        return {
            pageHeader: new ComplianceControlsPageHeaderModel(),
            tabs: [
                {
                    topicPath: `compliance/controls/${controlId}/overview`,
                    label: 'Overview',
                },
                {
                    topicPath: `compliance/controls/${controlId}/evidence`,
                    label: 'Evidence',
                },
                {
                    topicPath: `compliance/controls/${controlId}/monitoring`,
                    label: 'Monitoring',
                },
                {
                    topicPath: `compliance/controls/${controlId}/policies`,
                    label: 'Policies',
                },
                {
                    topicPath: `compliance/controls/${controlId}/frameworks`,
                    label: 'Frameworks',
                },
                {
                    topicPath: `compliance/controls/${controlId}/risks`,
                    label: 'Risks',
                },
            ],
            utilities: {
                utilitiesList: ['tickets_for_controls'],
            },
        };
    },
);

const Overview = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Overview" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Overview;
