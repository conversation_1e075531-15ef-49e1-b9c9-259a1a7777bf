import { action } from '@globals/mobx';
import { MonitoringDetailsEventPanelView } from '@views/monitoring-details-event-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringCodeDetailsOverviewEventPanel = (): React.JSX.Element => {
    return (
        <MonitoringDetailsEventPanelView
            data-testid="MonitoringCodeDetailsOverviewEventPanel"
            data-id="jnS1huiW"
        />
    );
};

export default MonitoringCodeDetailsOverviewEventPanel;
