import { sharedUsersInfiniteController } from '@controllers/users';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { VendorsProspectiveAddView } from '@views/vendors-prospective-add';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Vendors Prospective Add` }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        sharedUsersInfiniteController.loadUsers({
            roles: [
                'ADMIN',
                'TECHGOV',
                'WORKSPACE_ADMINISTRATOR',
                'E<PERSON><PERSON><PERSON>Y<PERSON>',
                'RISK_MANAGER',
            ],
        });
        sharedVendorsTypeformQuestionnairesController.allVendorsQuestionnairesQuery.load(
            {
                query: {
                    page: 1,
                    limit: 10,
                    prioritizeCategory: false,
                },
            },
        );

        return {
            pageHeader: {
                title: t`Add Prospective Vendor`,
                pageId: 'vendors-prospective-add-vendor-page',
                backLink: (
                    <AppLink
                        data-id="vendors-prospective-add-vendor-page-back-link"
                        href={new URL(getParentRoute(request.url)).pathname}
                        label={t`Back to Prospective vendors`}
                    />
                ),
            },
        };
    },
);

const VendorsProspectiveAdd = (): React.JSX.Element => {
    return (
        <VendorsProspectiveAddView
            data-testid="VendorsProspectiveAdd"
            data-id="xJmcE7p3"
        />
    );
};

export default VendorsProspectiveAdd;
