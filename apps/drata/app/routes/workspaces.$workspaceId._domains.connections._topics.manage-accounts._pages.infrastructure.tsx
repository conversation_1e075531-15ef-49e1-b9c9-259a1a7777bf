import { Banner } from '@cosmos/components/banner';
import { action } from '@globals/mobx';
import { RouteLandmark } from '@ui/layout-landmarks';
import { ConnectionManageAccountsInfrastructureView } from '@views/connection-manage-accounts-infrastructure';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Manage Infrastructure Accounts',
            banner: (
                <Banner
                    body="In-scope employees and contractors need to have a valid background check for related tests to pass. Sort the personnel column for empty fields to find unlinked background checks and select the personnel."
                    data-id="manage-infra-accounts-banner"
                    displayMode="section"
                    severity="warning"
                    title="Some personnel are missing linked background checks"
                />
            ),
        },
    };
});

const ConnectionsInfrastructure = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionsInfrastructure"
            data-id="92D946Ml"
        >
            <ConnectionManageAccountsInfrastructureView />
        </RouteLandmark>
    );
};

export default ConnectionsInfrastructure;
