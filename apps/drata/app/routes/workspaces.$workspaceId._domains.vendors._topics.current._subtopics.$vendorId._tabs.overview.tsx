import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileOverviewView } from '@views/vendors-profile-overview';

export const meta: MetaFunction = () => [{ title: 'Vendors Current Overview' }];

const VendorsCurrentOverview = (): React.JSX.Element => {
    return (
        <VendorsProfileOverviewView
            data-id="ic65FpqZ"
            data-testid="VendorsCurrentOverview"
        />
    );
};

export default VendorsCurrentOverview;
