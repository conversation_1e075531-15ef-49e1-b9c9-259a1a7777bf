import {
    sharedVendorsDetailsController,
    sharedVendorsProfileRisksController,
    sharedVendorsRisksController,
    sharedVendorsSettingsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import {
    VendorsProfilePageHeaderModel,
    VendorsProfileTabsModel,
} from '@models/vendors-profile';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params, request }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsDetailsController.loadVendorDetails(parseInt(vendorId));
        sharedVendorsSettingsController.loadVendorsSettings();
        sharedVendorsRisksController.loadVendorRisks();
        sharedVendorsProfileRisksController.setCurrentVendorId(
            Number(vendorId),
        );

        const parentHref = new URL(getParentRoute(request.url, 2)).pathname;

        const pageHeaderModel = new VendorsProfilePageHeaderModel();
        const tabsModel = new VendorsProfileTabsModel();

        pageHeaderModel.setParentUrl(parentHref);
        tabsModel.setVendorId(Number(vendorId));
        tabsModel.setVendorType('current');

        return {
            pageHeader: pageHeaderModel,
            contentNav: tabsModel,
        };
    },
);

const VendorsCurrent = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrent"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrent;
