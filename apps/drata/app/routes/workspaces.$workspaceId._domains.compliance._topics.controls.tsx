import { sharedControlsController } from '@controllers/controls';
import { action } from '@globals/mobx';
import { ControlsPageHeaderModel } from '@models/controls';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Controls' }];

export const clientLoader = action((): ClientLoader => {
    sharedControlsController.load();

    return {
        pageHeader: new ControlsPageHeaderModel(),
    };
});

const Controls = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Controls" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Controls;
