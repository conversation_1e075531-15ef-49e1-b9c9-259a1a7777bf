import { Metadata } from '@cosmos/components/metadata';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { SettingsAiView } from '@views/settings-ai';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - AI Settings' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            isCentered: true,
            title: 'AI Settings',
            slot: <Metadata label="Beta" colorScheme="education" type="tag" />,
        },
    };
});

export const SettingsAi = (): React.JSX.Element => {
    return <SettingsAiView data-testid="SettingsAi" data-id="lEGHcqzc" />;
};

export default SettingsAi;
