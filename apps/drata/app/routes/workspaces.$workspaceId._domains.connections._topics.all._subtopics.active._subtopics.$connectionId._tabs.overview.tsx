import { sharedConnectionsController } from '@controllers/connections';
import { action } from '@globals/mobx';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import { SettingsConnectionsOverview } from '@views/settings-connections-overview';

export const clientLoader = action(
    ({ params }: ClientLoaderFunctionArgs): null => {
        const { connectionId } = params;

        if (!connectionId || isNaN(Number(connectionId))) {
            throw new Error('Connection ID is required');
        }

        sharedConnectionsController.loadSetupDetails(Number(connectionId));
        sharedConnectionsController.loadConnectionSettings(
            Number(connectionId),
        );

        return null;
    },
);

const ConnectionDetailsOverviewTab = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionDetailsOverviewTab"
            data-id="VLDXbn4p"
        >
            <SettingsConnectionsOverview />
        </RouteLandmark>
    );
};

export default ConnectionDetailsOverviewTab;
