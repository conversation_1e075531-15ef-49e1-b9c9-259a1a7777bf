import { sharedControlRisksController } from '@controllers/controls';
import { sharedRiskSettingsController } from '@controllers/risk';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunction } from '@remix-run/react';
import { ControlsRisksView } from '@views/controls-risks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Control Detail Risks' }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { controlId } = params;

        sharedRiskSettingsController.load();
        sharedControlRisksController.load(Number(controlId));

        return null;
    },
);

const Risks = (): React.JSX.Element => {
    return <ControlsRisksView data-testid="Risks" data-id="zSEghJ55" />;
};

export default Risks;
