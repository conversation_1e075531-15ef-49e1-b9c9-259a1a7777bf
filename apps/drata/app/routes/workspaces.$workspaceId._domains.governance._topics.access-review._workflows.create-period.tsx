import {
    sharedAccessReviewController,
    sharedActiveAccessReviewPeriodsController,
} from '@controllers/access-reviews';
import { sharedUsersController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { AccessReviewCreatePeriodWizardView } from '@views/access-review-create-period';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: t`Create Review Period` }];

export const clientLoader = action((): ClientLoader => {
    // Get workspace ID from controller instead of params
    const { currentWorkspace } = sharedWorkspacesController;

    // Load applications data for the wizard
    sharedAccessReviewController.accessReview.load();

    // Load active access review periods data (includes applications with reviewers)
    sharedActiveAccessReviewPeriodsController.activeAccessReviewPeriods.load();

    // Load reviewer users for selection
    sharedUsersController.loadReviewerUsers();

    return {
        pageHeader: {
            title: t`Create Review Period`,
            pageId: 'access-review-create-period',
            backLink: (
                <AppLink
                    href={`/workspaces/${currentWorkspace?.id}/governance/access-review/applications`}
                    label={t`Back to Access Review`}
                />
            ),
        },
        layout: {
            centered: true,
        },
        tabs: [],
    };
});

const AccessReviewCreatePeriod = (): React.JSX.Element => {
    return (
        <AccessReviewCreatePeriodWizardView
            data-testid="AccessReviewCreatePeriod"
            data-id="0QMEH0pp"
        />
    );
};

export default AccessReviewCreatePeriod;
