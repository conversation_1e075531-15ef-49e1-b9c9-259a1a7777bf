import type { MetaFunction } from '@remix-run/node';
import { useParams } from '@remix-run/react';

export const meta: MetaFunction = () => [{ title: 'DomainsIdIndex' }];

const DomainsIdIndex = (): React.JSX.Element => {
    const { workspaceId, domainId } = useParams();

    return (
        <div
            data-testid="DomainsIdIndex"
            data-id="jPwZmxJb"
            style={{
                background: '#f0f',
                padding: '16px',
                display: 'flex',
                flexDirection: 'column',
            }}
        >
            <div
                style={{
                    display: 'flex',
                    flexDirection: 'column',
                    gap: '16px',
                    alignItems: 'center',
                    justifyContent: 'center',
                    textAlign: 'center',
                    background: '#00f',

                    flexGrow: 2,
                }}
            >
                <h1>
                    Select a <i>topic</i>
                </h1>
                <h2>
                    <i>workspace</i>/<i>domain</i> : {workspaceId}/{domainId}
                </h2>
                <p>
                    <strong>This will have an auto-redirect eventually</strong>
                </p>
                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    This temporary experience is to help everyone internally
                    understand the loading strategy of the app.
                </p>
                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    At this point we are aware of which <i>user</i>,{' '}
                    <i>workspace</i>, and <i>domain</i> has been selected. This
                    tells us which <i>topics</i> this <i>user</i>/<i>domain</i>{' '}
                    combo has access to. However we do not know which{' '}
                    <i>topic</i> they want to view.
                </p>

                <p
                    style={{
                        width: '70%',
                        margin: '0 auto',
                    }}
                >
                    This file will be used to control <strong>how</strong> the
                    user is redirected:
                </p>

                <ul
                    style={{
                        width: '30%',
                        margin: '0 auto',
                        textAlign: 'left',
                    }}
                >
                    <li>
                        To the first <i>topic</i> returned by the selected{' '}
                        <i>domain</i>
                    </li>

                    <li>
                        To the <i>topic</i> they were viewing the last time they
                        visited this <i>domain</i>
                    </li>

                    <li>
                        Or even some other business logic we have not thought up
                        yet!
                    </li>
                </ul>
            </div>
        </div>
    );
};

export default DomainsIdIndex;
