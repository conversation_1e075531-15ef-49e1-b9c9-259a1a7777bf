import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const DashboardIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/insights`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="DashboardIndex" data-id="4AUJDYud"></div>;
};

export default DashboardIndex;
