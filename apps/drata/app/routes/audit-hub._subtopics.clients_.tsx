import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AuditorClientsView } from '@views/audit-hub-auditor-clients';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: t`Audit Hub - Clients` }];

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: t`Client list`,
        },
    };
});

const AuditorClientsPage = (): React.JSX.Element => {
    return (
        <AuditorClientsView
            data-testid="AuditorClientsPage"
            data-id="xP7Zt5Lq"
        />
    );
};

export default AuditorClientsPage;
