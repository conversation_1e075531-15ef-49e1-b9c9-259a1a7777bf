import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const LibraryIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/tests`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="LibraryIndex" data-id="rzPHLhIH"></div>;
};

export default LibraryIndex;
