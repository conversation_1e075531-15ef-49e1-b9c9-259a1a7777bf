import { VendorSuggestedConnectionsBanner } from '@components/vendors-current';
import {
    sharedVendorsCurrentController,
    sharedVendorSuggestionsController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet, useNavigate } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import {
    getVendorsCurrentPageHeaderActions,
    VENDORS_CURRENT_PAGE_HEADER_KEY,
} from '@views/vendors-current';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Vendors Current' }];

const VendorsCurrentActionStack = (): React.JSX.Element => {
    const navigate = useNavigate();

    return (
        <ActionStack
            data-id="vendors-current-action-stack-test-id"
            gap={dimension3x}
            data-testid="VendorsCurrentActionStack"
            stacks={[
                {
                    actions: getVendorsCurrentPageHeaderActions(navigate),
                    id: `${VENDORS_CURRENT_PAGE_HEADER_KEY}-actions-stack`,
                },
            ]}
        />
    );
};

export const clientLoader = action((): ClientLoader => {
    sharedVendorsCurrentController.loadVendors({
        query: { page: 1, limit: 10, isArchived: false },
    });

    sharedVendorSuggestionsController.loadSuggestedVendors();

    return {
        pageHeader: {
            title: t`Current Vendors`,
            pageId: 'vendors-current-page',
            banner: (
                <VendorSuggestedConnectionsBanner data-id="vendors-current-suggestions-banner" />
            ),
            actionStack: <VendorsCurrentActionStack />,
        },
    };
});

const VendorsCurrent = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrent"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrent;
