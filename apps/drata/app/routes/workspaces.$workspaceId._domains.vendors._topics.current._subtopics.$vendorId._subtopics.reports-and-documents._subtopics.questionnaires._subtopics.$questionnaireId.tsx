import { sharedVendorsQuestionnairesController } from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsProfileReportsAndDocumentsQuestionnairePageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewQuestionnaireView } from '@views/vendors-security-review-questionnaire';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Reports and documents Questionnaire' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, questionnaireId } = params;

        if (!vendorId) {
            throw new Error('Vendor ID is required');
        }

        if (!questionnaireId) {
            throw new Error('Questionnaire ID is required');
        }

        sharedVendorsQuestionnairesController.loadAll(
            Number(vendorId),
            Number(questionnaireId),
        );

        return {
            pageHeader:
                new VendorsProfileReportsAndDocumentsQuestionnairePageHeaderModel(),
        };
    },
);

const VendorsCurrentReportsAndDocumentsQuestionnaires =
    (): React.JSX.Element => {
        return (
            <VendorsSecurityReviewQuestionnaireView
                showActions
                data-testid="VendorsCurrentReportsAndDocumentsQuestionnaires"
                data-id="de4yfZST"
            />
        );
    };

export default VendorsCurrentReportsAndDocumentsQuestionnaires;
