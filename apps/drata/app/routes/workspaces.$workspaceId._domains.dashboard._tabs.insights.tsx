import { sharedDashboardConnectionsController } from '@controllers/dashboard-connections';
import { sharedDashboardInsightsController } from '@controllers/dashboard-insights';
import { sharedDashboardNotificationsController } from '@controllers/dashboard-notifications';
import { sharedDashboardPersonnelController } from '@controllers/dashboard-personnel';
import { sharedDashboardPoliciesController } from '@controllers/dashboard-policies';
import { sharedDashboardTaskForecastController } from '@controllers/dashboard-task-forecast';
import { sharedDashboardTestTrendsController } from '@controllers/dashboard-test-trends';
import { sharedDashboardVendorRisksController } from '@controllers/dashboard-vendor-risks';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { DashboardInsightsView } from '@views/dashboard-insights';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Dashboard Insights` }];
};

export const clientLoader = action((): ClientLoader => {
    sharedDashboardInsightsController.loadFrameworkReadiness();
    sharedDashboardNotificationsController.loadNotifications();
    sharedDashboardPoliciesController.loadPoliciesOverview();
    sharedDashboardVendorRisksController.loadVendorRisksOverview();
    sharedDashboardPersonnelController.loadPersonnelStats();
    sharedDashboardConnectionsController.loadConnectionsStats();
    sharedDashboardTestTrendsController.loadTestTrends();
    sharedDashboardTaskForecastController.loadCustomTasksOverview();
    sharedDashboardTaskForecastController.loadCustomTasksDetails();

    return {
        pageHeader: {
            title: t`Dashboard`,
        },
    };
});

const DashboardInsightsTab = (): React.JSX.Element => {
    return (
        <DashboardInsightsView
            data-testid="DashboardInsightsTab"
            data-id="9FoP24Wd"
        />
    );
};

export default DashboardInsightsTab;
