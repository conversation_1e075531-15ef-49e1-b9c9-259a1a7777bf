import { RouteLandmark } from '@ui/layout-landmarks';
import { ConnectionsAvailableView } from '@views/connection-available';

const ConnectionsAvailable = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="ConnectionsAvailable"
            data-id="sfBPYJlt"
        >
            <ConnectionsAvailableView />
        </RouteLandmark>
    );
};

export default ConnectionsAvailable;
