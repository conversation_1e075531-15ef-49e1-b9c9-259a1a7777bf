import type { MetaFunction } from '@remix-run/node';
import { VendorsProfileSecurityReviewView } from '@views/vendors-profile-security-review';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Security Reviews' },
];

const VendorsProfileSecurityReviews = (): React.JSX.Element => {
    return (
        <VendorsProfileSecurityReviewView
            data-testid="VendorsProfileSecurityReviews"
            data-id="tYXVaOAx"
        />
    );
};

export default VendorsProfileSecurityReviews;
