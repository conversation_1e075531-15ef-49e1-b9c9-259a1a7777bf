import { sharedControlsController } from '@controllers/controls-owners-candidates';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import type { ClientLoaderFunctionArgs } from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import { CreateControlInfoView } from '@views/create-control-info';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Create control' }];
};

export const clientLoader = action(
    ({ request }: ClientLoaderFunctionArgs): ClientLoader => {
        sharedControlsController.loadControlOwnersCandidates({
            page: 1,
        });

        const parentHref = new URL(
            request.url.split('/').slice(0, -1).join('/'),
        ).pathname;

        return {
            pageHeader: {
                title: t`Create control`,
                backLink: (
                    <AppLink
                        href={parentHref}
                        data-testid="BackLink"
                        label={t`Back to Controls`}
                    />
                ),
            },
            topicsNav: {},
            tabs: [],
        };
    },
);

const CreateControl = (): React.JSX.Element => {
    return (
        <CreateControlInfoView data-testid="CreateControl" data-id="bzQBvtVm" />
    );
};

export default CreateControl;
