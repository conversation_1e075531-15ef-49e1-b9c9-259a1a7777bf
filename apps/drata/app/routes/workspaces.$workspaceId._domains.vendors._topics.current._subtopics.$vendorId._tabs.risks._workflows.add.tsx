import {
    sharedControlsForRiskManagementController,
    sharedRiskCategoriesController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { sharedUsersInfiniteController } from '@controllers/users';
import {
    sharedVendorsDetailsController,
    sharedVendorsRisksMutationController,
} from '@controllers/vendors';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { getParentRoute } from '@helpers/path';
import type {
    ClientLoaderFunction,
    ClientLoaderFunctionArgs,
    MetaFunction,
} from '@remix-run/react';
import { AppLink } from '@ui/app-link';
import {
    RISK_OWNERSHIP_USER_ROLES,
    VendorsProfileRisksAddView,
} from '@views/vendors-profile-risks-add';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Risks Add' },
];

export const clientLoader: ClientLoaderFunction = action(
    ({ request }: ClientLoaderFunctionArgs) => {
        sharedVendorsRisksMutationController.resetProcess();
        sharedUsersInfiniteController.loadUsers({
            roles: RISK_OWNERSHIP_USER_ROLES,
        });
        sharedControlsForRiskManagementController.loadControls();
        sharedRiskCategoriesController.loadCategories();
        sharedRiskSettingsController.load();

        const vendorName = sharedVendorsDetailsController.vendorDetails?.name;
        const backLinkLabel = vendorName
            ? t`Back to ${vendorName}`
            : t`Back to Vendor`;

        const parentHref = getParentRoute(request.url);

        return {
            pageHeader: {
                title: t`Add risk`,
                pageId: 'vendors-profile-risks-add-page',
                isCentered: true,
                backLink: (
                    <AppLink
                        href={parentHref}
                        label={backLinkLabel}
                        size="sm"
                    />
                ),
            },
            contentNav: {
                tabs: [],
            },
        };
    },
);

const VendorsCurrentRisksAdd = (): React.JSX.Element => {
    return (
        <VendorsProfileRisksAddView
            data-testid="VendorsCurrentRisksAdd"
            data-id="3FLR_X-o"
        />
    );
};

export default VendorsCurrentRisksAdd;
