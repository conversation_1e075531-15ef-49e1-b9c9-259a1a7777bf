import { action } from '@globals/mobx';
import { MonitoringDetailsEventPanelView } from '@views/monitoring-details-event-panel';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        tabs: [],
    };
});

const MonitoringDetailsOverviewEventPanel = (): React.JSX.Element => {
    return (
        <MonitoringDetailsEventPanelView
            data-testid="MonitoringDetailsOverviewEventPanel"
            data-id="oNJmJoST"
        />
    );
};

export default MonitoringDetailsOverviewEventPanel;
