import { sharedRiskSettingsController } from '@controllers/risk';
import { sharedVendorsProfileRisksController } from '@controllers/vendors';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'Vendors Current Risks' }];

export const clientLoader = action((): null => {
    sharedVendorsProfileRisksController.loadInitialData();
    sharedRiskSettingsController.load();

    return null;
});

const VendorsCurrentRisks = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrentRisks"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrentRisks;
