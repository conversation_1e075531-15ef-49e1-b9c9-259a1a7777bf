import { action } from '@globals/mobx';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Manage accounts',
        },
    };
});

const ManageAccounts = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="ManageAccounts"
            data-id="afKpVWha"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default ManageAccounts;
