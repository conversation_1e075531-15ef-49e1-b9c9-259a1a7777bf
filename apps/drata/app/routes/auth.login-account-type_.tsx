import type { MetaFunction } from '@remix-run/node';
import { LoginAccountTypeView } from '@views/login-account-type';

export const meta: MetaFunction = () => {
    return [{ title: 'Drata' }];
};

export const LoginAccountType = (): React.JSX.Element => {
    const regionAccountTypeOptions: React.ComponentProps<
        typeof LoginAccountTypeView
    >['regionAccountTypeOptions'] = [
        {
            label: 'North America',
            value: 'NA',
            accountTypeOptions: [
                {
                    label: 'My own business',
                    value: 'STANDARD_ACCOUNT',
                    helpText: 'Access Drata for your primary business account.',
                },
                {
                    label: 'My account list',
                    value: 'SERVICE_ACCOUNT',
                    helpText:
                        'Access Drata for other business accounts you manage.',
                },
            ],
        },
        {
            label: 'Europe',
            value: 'EU',
            accountTypeOptions: [
                {
                    label: 'My own business',
                    value: 'STANDARD_ACCOUNT',
                    helpText: 'Access Drata for your primary business account.',
                },
            ],
        },
        {
            label: 'Asia Pacific and Japan',
            value: 'APJ',
            accountTypeOptions: [],
        },
    ];

    return (
        <LoginAccountTypeView
            regionAccountTypeOptions={regionAccountTypeOptions}
            data-testid="LoginAccountType"
            data-id="sPyhQnSt"
        />
    );
};

export default LoginAccountType;
