import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { AppLink } from '@ui/app-link';
import { VendorsCurrentBulkAddVendorsView } from '@views/vendors-current-bulk-add-vendors';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Vendors Current bulk Add' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Bulk Add Vendors',
            pageId: 'vendors-current-add-vendor-page',
            backLink: (
                <AppLink
                    data-id="vendors-current-add-vendor-page-back-link"
                    href="/workspaces/1/vendors/current"
                    label="Back to Current vendors"
                />
            ),
            isCentered: true,
        },
    };
});

const VendorsCurrentBulkAddVendors = (): React.JSX.Element => {
    return (
        <VendorsCurrentBulkAddVendorsView
            data-testid="VendorsCurrentBulkAddVendors"
            data-id="2-YA94Nf"
        />
    );
};

export default VendorsCurrentBulkAddVendors;
