import { sharedConnectionsController } from '@controllers/connections';
import type { ClientTypeEnum } from '@globals/api-sdk/types';
import { action } from '@globals/mobx';
import {
    type ClientLoaderFunction,
    type ClientLoaderFunctionArgs,
    Outlet,
} from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }: ClientLoaderFunctionArgs): ClientLoader => {
        const { providerId } = params;

        sharedConnectionsController.benefits.load({
            query: {
                type: providerId?.toUpperCase() as ClientTypeEnum,
            },
        });

        return null;
    },
);

const ConnectionProviderDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="ConnectionProviderDetails"
            data-id="ug7Tv5x2"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default ConnectionProviderDetails;
