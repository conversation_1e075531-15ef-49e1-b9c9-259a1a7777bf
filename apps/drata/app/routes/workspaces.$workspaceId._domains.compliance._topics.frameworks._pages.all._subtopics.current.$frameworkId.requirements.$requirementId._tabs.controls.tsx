import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import { FrameworksControlsView } from '@views/frameworks-controls';

export const meta: MetaFunction = () => [{ title: t`Framework controls` }];

const FrameworkControls = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="article"
            data-testid="FrameworkControls"
            data-id="jPwZmxJb"
        >
            <FrameworksControlsView />
        </RouteLandmark>
    );
};

export default FrameworkControls;
