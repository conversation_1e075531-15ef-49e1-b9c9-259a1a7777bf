import { isNil } from 'lodash-es';
import { sharedConnectionsController } from '@controllers/connections';
import { sharedEventsController } from '@controllers/events';
import { sharedMonitoringCodeExclusionsController } from '@controllers/monitoring';
import {
    activeMonitoringCodeDetailsController,
    activeMonitoringController,
} from '@controllers/monitoring-details';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { action } from '@globals/mobx';
import { MonitoringCodePageHeaderModel } from '@models/monitoring-code-details';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { codebaseId } = params;

        if (isNil(codebaseId)) {
            throw new Error(
                'codebaseId does not exist, and that is a catastrophe. Call the police',
            );
        }

        activeMonitoringCodeDetailsController.loadTest(Number(codebaseId));

        sharedMonitoringCodeExclusionsController.setTestId(Number(codebaseId));

        sharedMonitoringCodeExclusionsController.monitoringCodeExclusionLoad();

        activeMonitoringController.loadMonitor(Number(codebaseId));
        sharedConnectionsController.allConfiguredConnectionsQuery.load();
        sharedEventsController.loadEvents();
        sharedMonitoringTestDetailsController.loadTest(Number(codebaseId));

        return {
            pageHeader: new MonitoringCodePageHeaderModel(),
            tabs: [
                {
                    topicPath: `compliance/monitoring/codebases/${codebaseId}/overview`,
                    label: 'Overview',
                },
                {
                    topicPath: `compliance/monitoring/codebases/${codebaseId}/findings`,
                    label: 'Findings',
                },
                {
                    topicPath: `compliance/monitoring/codebases/${codebaseId}/exclusions`,
                    label: 'Exclusions',
                },
                {
                    topicPath: `compliance/monitoring/codebases/${codebaseId}/controls`,
                    label: 'Controls',
                },
            ],
        };
    },
);

const MonitoringCodebaseDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="MonitoringCodebaseDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default MonitoringCodebaseDetails;
