import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'DomainsIdFlat' }];

const DomainsIdFlat = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="DomainsIdFlat"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default DomainsIdFlat;
