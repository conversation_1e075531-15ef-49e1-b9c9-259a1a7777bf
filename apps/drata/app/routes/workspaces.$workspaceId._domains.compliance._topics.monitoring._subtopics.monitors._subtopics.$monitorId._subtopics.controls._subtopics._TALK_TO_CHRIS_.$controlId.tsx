import { MonitoringDetailsControlsPanelView } from '@views/monitoring-details-controls-panel';

const MonitoringDetailsOverviewControlPanel = (): React.JSX.Element => {
    // TODO: Why does this not need the $controlId?
    // If it is not needed to load new data then we do not need an additional route.

    return (
        <MonitoringDetailsControlsPanelView
            data-testid="MonitoringDetailsOverviewControlPanel"
            data-id="uC8oAXyx"
        />
    );
};

export default MonitoringDetailsOverviewControlPanel;
