import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Trust' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Trust',
        },
    };
});

const Trust = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Trust" data-id="ISzzDb1x">
            <Outlet />
        </RouteLandmark>
    );
};

export default Trust;
