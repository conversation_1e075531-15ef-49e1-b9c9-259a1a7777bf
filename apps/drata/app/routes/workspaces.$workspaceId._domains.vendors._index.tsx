import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const VendorsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        // TODO: Add permission check for vendor insights access
        // When the redirect logic is ready, users without sharedFeatureAccessModel.isVendorInsightsReadEnabled
        // permissions should be redirected to /current instead of /insights
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/insights`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="VendorsIndex" data-id="4AUJDYud"></div>;
};

export default VendorsIndex;
