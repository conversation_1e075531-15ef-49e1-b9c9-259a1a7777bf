import { sharedConnectionsController } from '@controllers/connections';
import { action } from '@globals/mobx';
import { ConnectionDetailPageHeaderModel } from '@models/connection-details-page-header';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { connectionId } = params;

        if (!connectionId) {
            throw new Error('Missing connectionId');
        }

        sharedConnectionsController.loadSingleConnection(Number(connectionId));

        return {
            pageHeader: new ConnectionDetailPageHeaderModel(),
            tabs: [
                {
                    id: 'connections.all.active.overview',
                    topicPath: `connections/all/active/${connectionId}/overview`,
                    label: 'Overview',
                },
                {
                    id: 'connections.all.active.manage',
                    topicPath: `connections/all/active/${connectionId}/manage`,
                    label: 'Manage',
                },
            ],
        };
    },
);

const ConnectionDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="ConnectionDetails"
            data-id="dxjreJdQ"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default ConnectionDetails;
