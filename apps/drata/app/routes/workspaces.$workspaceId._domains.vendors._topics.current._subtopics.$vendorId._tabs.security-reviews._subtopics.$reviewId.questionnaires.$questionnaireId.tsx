import { sharedUtilitiesObservationsController } from '@controllers/utilities';
import {
    sharedVendorsProfileQuestionnaireAISummaryController,
    sharedVendorsQuestionnairesController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewObservationsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsSecurityReviewQuestionnairePageHeaderModel } from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { VendorsSecurityReviewQuestionnaireView } from '@views/vendors-security-review-questionnaire';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Vendors Profile Security Review Questionnaire' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId, reviewId, questionnaireId } = params;

        if (!vendorId) {
            throw new Error('Vendor ID is required');
        }

        if (!questionnaireId) {
            throw new Error('Questionnaire ID is required');
        }

        if (!reviewId) {
            throw new Error('Review ID is required');
        }
        sharedVendorsProfileQuestionnaireAISummaryController.loadQuestionnaireSummary(
            Number(questionnaireId),
        );

        sharedVendorsSecurityReviewDetailsController.loadSecurityReviewDetails({
            path: { id: Number(reviewId) },
        });
        sharedVendorsQuestionnairesController.loadAll(
            Number(vendorId),
            Number(questionnaireId),
        );

        sharedVendorsSecurityReviewObservationsController.loadSecurityReviewObservations(
            {
                path: { id: Number(reviewId) },
            },
        );

        sharedUtilitiesObservationsController.openUtility();

        return {
            pageHeader: new VendorsSecurityReviewQuestionnairePageHeaderModel(),
            utilities: {
                utilitiesList: ['observations'],
            },
        };
    },
);

const VendorsCurrentSecurityReviewQuestionnaire = (): React.JSX.Element => {
    return (
        <VendorsSecurityReviewQuestionnaireView
            data-testid="VendorsCurrentSecurityReviewQuestionnaire"
            data-id="ExP8wMs1"
        />
    );
};

export default VendorsCurrentSecurityReviewQuestionnaire;
