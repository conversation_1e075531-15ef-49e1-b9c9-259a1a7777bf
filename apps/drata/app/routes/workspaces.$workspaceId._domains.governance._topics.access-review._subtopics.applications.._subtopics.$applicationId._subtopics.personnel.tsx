import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { action } from '@globals/mobx';
import { AccessReviewPersonnelPageHeaderModel } from '@models/access-review-personnel';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { AccessReviewPersonnelView } from '@views/access-review-personnel';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Access Review Details' }];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        if (!params.applicationId || !params.clientType) {
            throw new Error(
                'Access Review ApplicationId and clientType is required',
            );
        }

        activeAccessReviewsApplicationsController.loadAccessReviewApplicationDetails(
            Number(params.applicationId),
        );

        return {
            pageHeader: new AccessReviewPersonnelPageHeaderModel(),
            tabs: [],
        };
    },
);

const UserAccessReviewApplications = (): React.JSX.Element => {
    return (
        <AccessReviewPersonnelView
            data-testid="UserAccessReviewApplications"
            data-id="7jgew8ty"
        />
    );
};

export default UserAccessReviewApplications;
