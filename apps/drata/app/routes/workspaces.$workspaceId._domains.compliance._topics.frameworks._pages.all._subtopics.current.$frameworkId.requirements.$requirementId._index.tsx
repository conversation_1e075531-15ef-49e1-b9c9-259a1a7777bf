import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const FrameworksRequirementsDetailsOverviewIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/overview`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return (
        <div
            data-testid="FrameworksRequirementsDetailsOverviewIndex"
            data-id="Sev6TOeo"
        />
    );
};

export default FrameworksRequirementsDetailsOverviewIndex;
