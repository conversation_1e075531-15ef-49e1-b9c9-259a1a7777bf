import { parseInt } from 'lodash-es';
import {
    sharedVendorsDetailsController,
    sharedVendorsProfileSecurityReviewsController,
    sharedVendorsSettingsController,
} from '@controllers/vendors';
import { action } from '@globals/mobx';
import {
    VendorsProfileContentNavModel,
    VendorsProfilePageHeaderModel,
} from '@models/vendors-profile';
import type { LoaderFunctionArgs, MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [
    { title: 'Vendors Current Security Reviews' },
];
export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { vendorId } = params;

        if (!vendorId || isNaN(Number(vendorId))) {
            throw new Error('Invalid vendorId');
        }

        sharedVendorsProfileSecurityReviewsController.setVendorId(
            parseInt(vendorId),
        );
        sharedVendorsDetailsController.loadVendorDetails(parseInt(vendorId));
        sharedVendorsSettingsController.loadVendorsSettings();

        return null;
    },
);

export const handle = {
    overrides: {
        pageHeader: new VendorsProfilePageHeaderModel(),
        contentNav: new VendorsProfileContentNavModel(),
    },
};

const VendorsCurrentSecurityReviews = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsCurrentSecurityReviews"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsCurrentSecurityReviews;
