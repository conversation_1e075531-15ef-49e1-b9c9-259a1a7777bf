import {
    sharedAccessReviewActivePeriodUserNotesController,
    sharedAccessReviewActivePeriodUserTicketsController,
    sharedAccessReviewBulkActionStatusController,
    sharedAccessReviewPeriodApplicationUserController,
    sharedAccessReviewUserController,
} from '@controllers/access-reviews';
import { action } from '@globals/mobx';
import { AccessReviewPersonnelDetailsModel } from '@models/access-review-personnel-details';
import type { ClientLoaderFunction } from '@remix-run/react';
import { AccessReviewApplicationDetailsPersonnelDetailsView } from '@views/access-review-application-details-personnel-details';
import type { ClientLoader } from '../types';

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        const { applicationId, personnelId, periodId } = params;

        sharedAccessReviewPeriodApplicationUserController.loadAccessReviewPeriodApplicationUser(
            {
                applicationId,
                periodId,
                userId: personnelId,
            },
        );

        sharedAccessReviewUserController.loadAccessReviewUser({
            applicationId,
            userId: personnelId,
        });

        sharedAccessReviewActivePeriodUserNotesController.loadNotes({
            periodId,
            reviewAppId: applicationId,
            userId: personnelId,
        });

        sharedAccessReviewPeriodApplicationUserController.loadAccessReviewPeriodApplicationUser(
            {
                applicationId,
                periodId,
                userId: personnelId,
            },
        );

        sharedAccessReviewBulkActionStatusController.setPeriodId(
            Number(periodId),
        );

        sharedAccessReviewBulkActionStatusController.setApplicationId(
            Number(applicationId),
        );

        sharedAccessReviewActivePeriodUserTicketsController.load(
            Number(personnelId),
        );
        sharedAccessReviewActivePeriodUserTicketsController.loadClosedTicketsInfinite(
            Number(personnelId),
        );

        return {
            pageHeader: new AccessReviewPersonnelDetailsModel(),
            tabs: [],
            utilities: {
                utilitiesList: [
                    'notes_for_access_review_active_period_user',
                    'tickets_for_access_review_active_period_user',
                ],
            },
        };
    },
);

const AccessReviewApplicationDetailsPersonnelDetails =
    (): React.JSX.Element => {
        return (
            <AccessReviewApplicationDetailsPersonnelDetailsView
                data-testid="AccessReviewApplicationDetailsPersonnelDetails"
                data-id="PQKKCe-u"
            />
        );
    };

export default AccessReviewApplicationDetailsPersonnelDetails;
