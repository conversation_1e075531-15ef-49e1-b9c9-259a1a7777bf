import {
    activeMonitoringController,
    sharedMonitoringDetailsExclusionsController,
    sharedMonitoringPersonnelExclusionsController,
} from '@controllers/monitoring-details';
import { action } from '@globals/mobx';
import { MonitoringDetailsPageHeaderModel } from '@models/monitoring-details';
import type { LoaderFunctionArgs } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const clientLoader = action(
    ({ params }: LoaderFunctionArgs): ClientLoader => {
        const { monitorId } = params;

        // activeMonitoringDetailsController.loadMonitorDetails(Number(monitorId));
        // sharedConnectionsController.allConfiguredConnectionsQuery.load();
        // // TODO: My linter is saying this is unused, but I am not sure if it is from a bad merge or
        // // just my local API issues so I am leaving it here in case it needs to be added back in.
        // sharedEventsController.events.load();
        // sharedMonitoringTestDetailsController.loadTest(Number(monitorId));

        activeMonitoringController.loadMonitor(Number(monitorId));

        sharedMonitoringDetailsExclusionsController.setTestId(
            Number(monitorId),
        );

        sharedMonitoringDetailsExclusionsController.loadExclusions();

        sharedMonitoringPersonnelExclusionsController.setTestId(
            Number(monitorId),
        );

        sharedMonitoringPersonnelExclusionsController.loadPersonnelExclusions();

        return {
            pageHeader: new MonitoringDetailsPageHeaderModel(),
            tabs: [
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/overview`,
                    label: 'Overview',
                },
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/findings`,
                    label: 'Findings',
                },
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/exclusions`,
                    label: 'Exclusions',
                },
                {
                    topicPath: `compliance/monitoring/monitors/${monitorId}/controls`,
                    label: 'Controls',
                },
            ],
        };
    },
);

const MonitoringMonitorDetails = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="MonitoringMonitorDetails"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default MonitoringMonitorDetails;
