import { useEffect } from 'react';
import { useLocation, useNavigate } from '@remix-run/react';

const ManageAccountsIndex = (): React.JSX.Element => {
    const location = useLocation();
    const navigate = useNavigate();

    useEffect(() => {
        const locationWithOverrides = {
            ...location,
            pathname: `${location.pathname}/background-checks`,
        };

        navigate(locationWithOverrides);
    }, [navigate, location]);

    return <div data-testid="ManageAccountsIndex" data-id="wz5DWb8R"></div>;
};

export default ManageAccountsIndex;
