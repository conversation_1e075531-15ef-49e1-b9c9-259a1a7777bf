import { action } from '@globals/mobx';
import { ConnectionsManageAccountsVersionControlView } from '@views/connections-manage-accounts-version-control';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Manage Version Control',
        },
    };
});

export const SettingsConnectionsManageAccountsVersionControl =
    (): React.JSX.Element => {
        return (
            <ConnectionsManageAccountsVersionControlView
                data-testid="SettingsConnectionsManageAccountsVersionControl"
                data-id="hFnuOUVq"
            />
        );
    };

export default SettingsConnectionsManageAccountsVersionControl;
