import { action } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import { PoliciesArchiveView } from '@views/policies';
import type { ClientLoader } from '../types';

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: sharedPoliciesPageHeaderModel,
    };
});

const PoliciesArchive = (): React.JSX.Element => {
    return (
        <PoliciesArchiveView data-testid="PoliciesArchive" data-id="_0PX-cyl" />
    );
};

export default PoliciesArchive;
