import { action } from '@globals/mobx';
import { AuditorSettingsPageHeaderModel } from '@models/auditor-settings-page-header';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Audit Hub - Settings' }];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        pageHeader: new AuditorSettingsPageHeaderModel(),
        tabs: [
            {
                id: 'profile',
                label: 'Profile',
                topicPath: '/settings/profile',
            },
            {
                id: 'api-keys',
                label: 'API Keys',
                topicPath: '/settings/api-keys',
            },
        ],
        utilities: {
            utilitiesList: [],
        },
        layout: {
            centered: true,
        },
    };
});

const AuditHubSettings = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="AuditHubSettings"
            data-id="AuditHubSettingsContent"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default AuditHubSettings;
