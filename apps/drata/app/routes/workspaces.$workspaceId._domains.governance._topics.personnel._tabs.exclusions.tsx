import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

const PersonnelExclusions = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="PersonnelExclusions"
            data-id="MviHNu9x"
        >
            <h1>This is making no sense at this point. Talk to <PERSON>.</h1>
            <Outlet />
        </RouteLandmark>
    );
};

export default PersonnelExclusions;
