import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: 'TopicsFlat' }];

const TopicsFlat = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="TopicsFlat"
            data-id="topics-flat"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default TopicsFlat;
