import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Personal Profile' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Personal Profile',
        },
    };
});

export const SettingsPersonalProfile = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsPersonalProfile"
            data-id="73Tl5oPY"
        >
            This page does not exist in the repo, but is listed in Figma
        </RouteLandmark>
    );
};

export default SettingsPersonalProfile;
