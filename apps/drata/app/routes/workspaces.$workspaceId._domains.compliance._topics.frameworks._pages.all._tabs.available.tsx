import { isNil } from 'lodash-es';
import { sharedDisabledFrameworksController } from '@controllers/frameworks';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { action, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { MetaFunction } from '@remix-run/node';
import { AvailableFrameworksView } from '@views/available-frameworks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Available frameworks' }];

export const clientLoader = action((): ClientLoader => {
    when(
        () => !isNil(sharedWorkspacesController.currentWorkspace),
        () => {
            // This should be a given at this point, but TypeScript doesn't know that
            if (sharedWorkspacesController.currentWorkspace) {
                sharedDisabledFrameworksController.frameworksDisableQuery.load({
                    path: {
                        id: sharedWorkspacesController.currentWorkspace.id,
                    },
                });
            }
        },
    );

    sharedDisabledFrameworksController.loadDisableFrameworks({
        pagination: {
            page: 1,
            pageSize: DEFAULT_PAGE_SIZE,
            pageIndex: 0,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
        },
        globalFilter: { filters: {} },
        sorting: [],
    });

    return null;
});

const AvailableFrameworks = (): React.JSX.Element => {
    return (
        <AvailableFrameworksView
            data-testid="AvailableFrameworks"
            data-id="cbJKWI-O"
        />
    );
};

export default AvailableFrameworks;
