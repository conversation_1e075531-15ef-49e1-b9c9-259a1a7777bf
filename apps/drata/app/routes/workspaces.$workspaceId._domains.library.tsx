import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Library' }];
};

export const clientLoader = action((): ClientLoader => {
    return {
        pageHeader: {
            title: 'Library',
        },

        topicsNav: {
            id: 'nav.library',
            title: 'Library',
            domainsOrder: ['library'],
            domains: {
                library: {
                    label: 'Library',
                    hideLabel: true,
                    topicsOrder: ['library.tests'],
                    topics: {
                        'library.tests': {
                            id: 'library.tests',
                            topicPath: 'library/tests',
                            label: 'Tests',
                        },
                    },
                },
            },
        },
    };
});

const Library = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Library" data-id="1TnqFVBj">
            <Outlet />
        </RouteLandmark>
    );
};

export default Library;
