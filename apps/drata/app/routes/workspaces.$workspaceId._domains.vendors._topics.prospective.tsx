import { sharedVendorsProspectiveController } from '@controllers/vendors';
import { action } from '@globals/mobx';
import { VendorsProspectivePageHeaderModel } from '@models/vendors-profile';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => [{ title: 'Vendors Prospective' }];

export const clientLoader = action((): ClientLoader => {
    sharedVendorsProspectiveController.allVendorsProspectiveQuery.load({
        query: { page: 1, limit: 10 },
    });

    return {
        pageHeader: new VendorsProspectivePageHeaderModel(),
    };
});

const VendorsProspective = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="VendorsProspective"
            data-id="Bwg9WWyA"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default VendorsProspective;
