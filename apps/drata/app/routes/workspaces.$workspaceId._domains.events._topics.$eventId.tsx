import {
    sharedEventsDetailsController,
    sharedEventsNotesController,
} from '@controllers/events-details';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { EventDetailsPageHeaderModel } from '@models/event-details-page-header';
import type { ClientLoaderFunction, MetaFunction } from '@remix-run/react';
import { EventDetailsView } from '@views/event-details';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: t`Audit event` }];
};

export const clientLoader: ClientLoaderFunction = action(
    ({ params }): ClientLoader => {
        if (!params.eventId) {
            throw new Error('Event ID is required');
        }

        sharedEventsDetailsController.loadEventDetails(params.eventId);
        sharedEventsNotesController.loadNotes(params.eventId);

        return {
            pageHeader: new EventDetailsPageHeaderModel(),
            utilities: {
                utilitiesList: ['notes_for_events'],
            },
        };
    },
);

const EventDetails = (): React.JSX.Element => {
    return <EventDetailsView data-testid="EventDetails" data-id="52lYYce4" />;
};

export default EventDetails;
