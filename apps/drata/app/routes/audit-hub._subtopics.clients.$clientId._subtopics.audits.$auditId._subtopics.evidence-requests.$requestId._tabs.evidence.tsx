import { t } from '@globals/i18n/macro';
import type { MetaFunction } from '@remix-run/node';
import { EvidenceRequestDetailsEvidenceView } from '@views/audit-hub-evidence-request-details-evidence';

export const meta: MetaFunction = () => {
    return [{ title: t`Audit hub - Evidence` }];
};
const RequestDetailsEvidence = (): React.JSX.Element => {
    return (
        <EvidenceRequestDetailsEvidenceView
            data-id="vkjXpHrX"
            data-testid="RequestDetailsEvidence"
        />
    );
};

export default RequestDetailsEvidence;
