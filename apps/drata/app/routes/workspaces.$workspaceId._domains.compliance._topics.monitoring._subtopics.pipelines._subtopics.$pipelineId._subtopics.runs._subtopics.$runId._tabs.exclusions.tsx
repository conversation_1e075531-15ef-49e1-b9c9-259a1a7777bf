import { MonitoringPipelineRunDetailsExclusionsView } from '@views/monitoring-pipeline-run-details-exclusions';

const MonitoringPipelineDetailsExclusions = (): React.JSX.Element => {
    return (
        <MonitoringPipelineRunDetailsExclusionsView
            data-testid="MonitoringPipelineDetailsExclusions"
            data-id="qjsGzKIL"
        />
    );
};

export default MonitoringPipelineDetailsExclusions;
