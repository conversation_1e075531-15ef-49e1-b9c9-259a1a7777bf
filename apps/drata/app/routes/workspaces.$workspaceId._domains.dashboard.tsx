import { sharedCurrentUserModel } from '@globals/current-user';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Dashboard' }];
};

export const clientLoader = action((): ClientLoader => {
    const { isAdmin } = sharedCurrentUserModel;
    const { isMultipleWorkspacesEnabled } = sharedFeatureAccessModel;

    if (isAdmin && isMultipleWorkspacesEnabled) {
        return {
            pageHeader: {
                title: t`Dashboard`,
            },

            tabs: [
                {
                    topicPath: 'dashboard/insights',
                    label: t`Insights`,
                },
                {
                    topicPath: 'dashboard/all-workspaces',
                    label: t`All workspaces`,
                    metadata: {
                        label: t`Beta`,
                        type: 'status',
                        colorScheme: 'education',
                    },
                },
            ],
        };
    }

    return {
        pageHeader: {
            title: t`Dashboard`,
        },
    };
});

const DashboardDomain = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="DashboardDomain"
            data-id="ISzzDb1x"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default DashboardDomain;
