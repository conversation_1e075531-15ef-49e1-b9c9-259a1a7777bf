// import { sharedWorkspacesWithFrameworkController } from '@controllers/workspaces';
import { ActionStack, type Stack } from '@cosmos/components/action-stack';
import { dimension2x } from '@cosmos/constants/tokens';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import type { ClientLoader } from '../types';

export const meta: MetaFunction = () => {
    return [{ title: 'Settings - Workspaces' }];
};

export const HEADER_STACK_ACTIONS: Stack[] = [
    {
        actions: [
            {
                actionType: 'button',
                id: 'settings-workspaces-add-button',
                typeProps: {
                    label: 'Add workspace',
                    level: 'secondary',
                },
            },
        ],
        id: 'settings-workspaces-create-action-stack',
    },
];

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    // TODO: Idk why this is breaking
    // sharedWorkspacesWithFrameworkController.workspaces.load({
    //     query: { page: 1, limit: 10 },
    // });

    return {
        pageHeader: {
            pageId: 'settings-workspaces',
            title: 'Workspaces',
            actionStack: (
                <ActionStack
                    data-id="settings-workspaces-action-stack"
                    gap={dimension2x}
                    stacks={HEADER_STACK_ACTIONS}
                />
            ),
        },
    };
});

export const SettingsWorkspaces = (): React.JSX.Element => {
    return (
        <RouteLandmark
            as="section"
            data-testid="SettingsWorkspaces"
            data-id="jPwZmxJb"
        >
            <Outlet />
        </RouteLandmark>
    );
};

export default SettingsWorkspaces;
