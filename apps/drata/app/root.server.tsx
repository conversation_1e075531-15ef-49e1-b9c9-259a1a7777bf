import radixTheme from '@radix-ui/themes/layout.css?url';
import type { LinksFunction, MetaFunction } from '@remix-run/node';
import { <PERSON><PERSON>, <PERSON>a, Scripts } from '@remix-run/react';
import styles from './global.css?url';

/*
 * This is the root server component.
 * It is the entry point for the server.
 * It is not rendered on the client.
 *
 * It exists because remix does not offer a true SPA-only mode. It still makes
 * and renders a server bundle 1 time at build to generate an `index.html` file.
 * This action isn't needed so we patched the plugin and offer it this dummy
 * file to make into an `index.html`.
 *
 * https://github.com/remix-run/remix/discussions/8934
 */

export const meta: MetaFunction = () => {
    return [
        {
            title: 'Drata Inc.',
        },
    ];
};

export const links: LinksFunction = () => {
    return [
        { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
        {
            rel: 'preconnect',
            href: 'https://fonts.gstatic.com',
            crossOrigin: 'anonymous',
        },
        {
            rel: 'stylesheet',
            href: 'https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap',
        },
        {
            rel: 'stylesheet',
            href: styles,
        },
        { rel: 'stylesheet', href: radixTheme },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '256x256',
            href: 'https://drata.com/images/favicon-256x256.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '48x48',
            href: 'https://drata.com/images/favicon-48x48.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '32x32',
            href: 'https://drata.com/images/favicon-32x32.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '16x16',
            href: 'https://drata.com/images/favicon-16x16.png',
        },
        {
            rel: 'icon',
            type: 'image/x-icon',
            href: 'https://drata.com/images/favicon.ico',
        },
    ];
};

export const Layout = ({
    children,
}: {
    children: React.ReactNode;
}): React.JSX.Element => {
    return (
        <html lang="en" data-testid="Layout" data-id="Dz_Soub0">
            <head>
                <meta charSet="utf-8" />
                <meta
                    name="viewport"
                    content="width=device-width, initial-scale=1"
                />
                <Meta />
                <Links />
            </head>
            <body>
                {children}
                <Scripts nonce="remix-server" />
            </body>
        </html>
    );
};

const App = (): null => {
    return null;
};

export default App;
