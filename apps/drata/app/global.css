/* Source: apps/drata/app/global.css */

*,
:after,
:before {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: var(--primary-text-selected);
    line-height: 1.15;

    font-family: var(--font-family-default);
}

body {
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

article,
aside,
figcaption,
figure,
footer,
header,
hgroup,
main,
nav,
section {
    display: block;
}

a,
a:hover {
    text-decoration: none;
}
