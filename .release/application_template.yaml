---
auto_deploy: true
context: "release-rl4vgs"
domain: "envs.drata.net"
repo_name: "drata/multiverse"
git_fetch_depth: 1

rules:
  - service: "drata-app"
    hostnames:
      - "drata-app-${env_id}.${domain}"
    path: "/"
    visibility: "private"

  - service: "storybook"
    hostnames:
      - "storybook-${env_id}.${domain}"
    path: "/"
    visibility: "private"

environment_templates:
  - name: "ephemeral"
  - name: "permanent"

app_imports:
  - name: "api"
  - name: "admin"
  - name: "web" # TODO - Remove this after constellation, this is meant to be able to standup web against the same API for testing.

parallelize_app_imports: true

resources:
  cpu:
    limits: "4000m"
    requests: "200m"
  memory:
    # https://home.robusta.dev/blog/kubernetes-memory-limit
    requests: "8Gi"
    limits: "8Gi"
  replicas: 1

services:
  - name: "drata-app"
    completed_timeout: 3000
    has_repo: false
    static: true
    build_command: "bash ./scripts/releasehub-build.sh"
    build_output_directory: "apps/drata/dist/client"
    build_package_install_command: "pnpm install"

  - name: "storybook"
    completed_timeout: 3000
    has_repo: false
    static: true
    build_command: "pnpm run storybook:build"
    build_output_directory: "dist/storybook/storybook"
    build_package_install_command: "pnpm install --frozen-lockfile"

workflows:
  - name: "setup"
    wait_for_all_tasks_to_complete: true
    parallelize:
      - step: "services-0"
        wait_for_finish: true
        tasks:
          - "services.drata-app"
          - "services.storybook"

  - name: "patch"
    wait_for_all_tasks_to_complete: true
    parallelize:
      - step: "services-0"
        wait_for_finish: true
        tasks:
          - "services.drata-app"
          - "services.storybook"

  - name: "teardown"
    parallelize:
      - step: "remove-environment"
        tasks:
          - "release.remove_environment"

ingress:
  proxy_body_size: "25m"
  proxy_max_temp_file_size: "4096m"
