---
defaults: []
services:
    drata-app:
        - key: "NPM_TOKEN"
          value: "$secrets.rsm.RELEASEHUB_ACCESS_TOKEN"
          secret: true

        - key: "APP_ENV"
          value: "releasehub"

        - key: "NODE_OPTIONS"
          # max-old-space must be a fair bit less than the pod's memory limit because we also need space for the runtime and semi-space
          # max-semi-space is the space used for the young generation of objects, there are actually two semi-spaces so this number effectively gets doubled
          value: "--max-old-space-size=4096 --max-semi-space-size=128"

    storybook:
        - key: "NPM_TOKEN"
          value: "$secrets.rsm.RELEASEHUB_ACCESS_TOKEN"
          secret: true

        - key: "APP_ENV"
          value: "releasehub"

        - key: "NODE_OPTIONS"
          # max-old-space must be a fair bit less than the pod's memory limit because we also need space for the runtime and semi-space
          # max-semi-space is the space used for the young generation of objects, there are actually two semi-spaces so this number effectively gets doubled
          value: "--max-old-space-size=4096 --max-semi-space-size=128"
