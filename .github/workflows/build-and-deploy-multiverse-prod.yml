name: "Build and Deploy - Multiverse Prod"
on:
  workflow_dispatch:
    inputs:
      us-west-2:
        description: "Deploy to prod us-west-2"
        required: true
        default: false
        type: "boolean"
  push:
    tags:
      - "[0-9]+.[0-9]+.[0-9]+"

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: true

# TODO: Eventually add back in slack messages.
env:
  APP_ENV: "prod"
  APPLICATION: "web-multiverse"
  SLACK_CHANNEL: "C01FV955EKB" # deployments
  SLACK_CHANNEL_FAILOVER: "C08ES6KH332" # team-drockstars-alerts
  NPM_TOKEN: ${{secrets.NPM_REGISTRY_RO_PAT}}
  region: "us-west-2"

jobs:
  swagger:
    runs-on: ["runs-on", "runner=build-web", "private=true", "run-id=${{ github.run_id }}"]

    steps:
      - name: "Checkout API Repository"
        uses: "actions/checkout@v4"
        with:
          token: ${{secrets.MULTIVERSE_ACTIONS_PAT}}
          repository: "drata/api"
          ref: "release"
          persist-credentials: true
          submodules: "recursive"
          fetch-depth: 1

      - name: "Install Node"
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: "Cache API npm dependencies"
        uses: "actions/cache@v4"
        id: "api-node-modules-cache"
        with:
          path: "./node_modules"
          key: "${{ runner.os }}-yarn-${{ hashFiles('./yarn.lock') }}"

      - name: "Install API dependencies"
        if: steps.api-node-modules-cache.outputs.cache-hit != 'true'
        run: "yarn install --non-interactive --prefer-offline --frozen-lockfile"

      - name: "Build API Swagger doc"
        run: "NODE_CONFIG_ENV=test yarn run build && NODE_CONFIG_ENV=test SKIP_DB=true ./bin/drata-cli swagger:docs"

      - name: "Upload Artifact"
        uses: "actions/upload-artifact@v4"
        with:
          name: "swagger-json"
          path: "./docs/swagger/swagger.json"
          if-no-files-found: "error"
          retention-days: 1

  build_and_deploy:
    name: "Build and Deploy"

    runs-on: ["runs-on", "runner=build-web", "private=true", "run-id=${{ github.run_id }}"]

    needs: ["swagger"]

    environment: "prod"

    steps:
      - name: "Clone Repository"
        id: "clone-repo"
        uses: "actions/checkout@v4"
        with:
          persist-credentials: true
          fetch-depth: 0

      - name: "Set friendly branch name"
        id: "branch-name"
        uses: "drata/utils-actions/get-friendly-branch-name@1.0.0"

      - name: "Determine Trigger Source"
        id: "determine-trigger"
        run: |
          if [ "${{ github.ref_type }}" == "branch" ]; then
            echo "branch=${{ steps.branch-name.outputs.branch }}" >> $GITHUB_OUTPUT
            echo "dynamodb-branch=${{ steps.branch-name.outputs.branch }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref_type }}" == "tag" ]; then
            echo "branch=heads/${GITHUB_REF#refs/tags/}"  >> $GITHUB_OUTPUT
            echo "dynamodb-branch=${GITHUB_REF#refs/tags/}"  >> $GITHUB_OUTPUT
          else
            echo "Unknown trigger source"
            exit 1
          fi

      - name: "Get short SHA"
        id: "short-sha"
        shell: "bash"
        run: echo "short-sha=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT

      - name: "Get short region"
        id: "short-region"
        shell: "bash"
        run: echo "short-region=${region::2}" >> $GITHUB_OUTPUT

      - name: "Assume Infra role"
        id: "configure-credentials"
        uses: "drata/iam-actions/assume-role@1.2.0"
        with:
          account: "infra"
          unset-current-credentials: true

      - name: "Setup Node"
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: "Setup pnpm"
        uses: "pnpm/action-setup@v4"

      - name: "Install Dependencies"
        run: "pnpm install --frozen-lockfile"

      - name: "Build Tokens"
        run: "pnpm run tokens"

      - name: "Download Swagger"
        uses: "actions/download-artifact@v4"
        with:
          name: "swagger-json"
          path: "api/docs/swagger"

      - name: "Build API-SDK"
        run: "pnpm run update-api-sdk"
        env:
          API_LOCATION: "../../../api"

      - name: "Build"
        run: "pnpm run app:drata:build"

      - name: "Deploy CFP"
        id: "deploy-cfp"
        uses: "drata/build-and-deployment-actions/deploy-cloudflare-pages@****************************************" # TODO: remove this once the devops PR is merged in
        with:
          application: ${{ env.APPLICATION }} # Doesn't seem to be used in the yml file
          environment: "prod"
          directory: "apps/drata/dist/client"
          short-sha: ${{ steps.short-sha.outputs.short-sha }}
          sha: ${GITHUB_SHA}
          branch: ${{ steps.determine-trigger.outputs.branch }}
          npm-token: ${{ env.NPM_TOKEN }}
          dynamodb-application: ${{ env.APPLICATION }}
          dynamodb-branch: ${{ steps.determine-trigger.outputs.dynamodb-branch }}
          cfp-project-name: "prod-${{ env.APPLICATION }}-${{ steps.short-region.outputs.short-region }}"
          cloudflare-api-token: ${{ secrets.CLOUDFLARE_PROD_TOKEN }}
