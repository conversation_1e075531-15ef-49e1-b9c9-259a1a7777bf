name: Build and Deploy - Cosmos UI
on:
  workflow_dispatch:
  push:
    branches:
      - main
    paths:
      - 'cosmos/**'
      - 'cosmos-lab/**'

env:
  APPLICATION: cosmos-ui
  SLACK_CHANNEL: C019AAKJCDB # #eng-fe
  NPM_TOKEN: ${{ secrets.NPM_REGISTRY_RO_PAT }}
  region: us-west-2

jobs:

  build_and_deploy:
    name: Build and Deploy
    runs-on: ["runs-on", "runner=build-web", "private=true", "run-id=${{ github.run_id }}"]
    timeout-minutes: 60
    concurrency:
        group: "${{ github.workflow }}-${{ github.ref }}"

    steps:
      - name: Clone Repository
        id: clone-repo
        uses: "actions/checkout@v4"
        with:
          persist-credentials: true
          fetch-depth: 0

      - name: Set friendly branch name
        id: branch-name
        uses: "drata/utils-actions/get-friendly-branch-name@1.0.0"

      - name: Determine Trigger Source
        id: determine-trigger
        run: |
          if [ "${{ github.ref_type }}" == "branch" ]; then
            echo "branch=${{ steps.branch-name.outputs.branch }}" >> $GITHUB_OUTPUT
            echo "dynamodb-branch=${{ steps.branch-name.outputs.branch }}" >> $GITHUB_OUTPUT
          elif [ "${{ github.ref_type }}" == "tag" ]; then
            echo "branch=heads/${GITHUB_REF#refs/tags/}"  >> $GITHUB_OUTPUT
            echo "dynamodb-branch=${GITHUB_REF#refs/tags/}"  >> $GITHUB_OUTPUT
          else
            echo "Unknown trigger source"
            exit 1
          fi

      - name: print branch name
        shell: bash
        run: echo ${{ steps.determine-trigger.outputs.branch }}

      - name: Get short SHA
        id: short-sha
        shell: bash
        run: echo "short-sha=${GITHUB_SHA::7}" >> $GITHUB_OUTPUT

      - name: Get short region
        id: short-region
        shell: bash
        run: echo "short-region=${region::2}" >> $GITHUB_OUTPUT

      - name: Assume Infra role
        id: configure-credentials
        uses: "drata/iam-actions/assume-role@1.2.0"
        with:
          account: "infra"
          unset-current-credentials: true

      - name: Setup Node
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: Setup pnpm
        uses: "pnpm/action-setup@v4"

      - name: Install Dependencies
        shell: bash
        run: "pnpm install --frozen-lockfile"
        env:
          NPM_TOKEN: ${{ env.NPM_TOKEN }}

      - name: Build
        shell: bash
        run: "pnpm storybook:build"
        env:
          NPM_TOKEN: ${{ env.NPM_TOKEN }}

      - name: Remove CSP
        run: "rm -rf functions"

      - name: Deploy CFP
        id: deploy-cfp
        uses: "drata/build-and-deployment-actions/deploy-cloudflare-pages@****************************************" # TODO: remove this once the devops PR is merged in
        with:
          application: ${{ env.APPLICATION }} # Doesn't seem to be used in the yml file
          environment: prod
          directory: "dist/storybook/storybook"
          short-sha: ${{ steps.short-sha.outputs.short-sha }}
          sha: ${GITHUB_SHA}
          branch: ${{ steps.determine-trigger.outputs.branch }}
          npm-token: ${{ env.NPM_TOKEN }}
          dynamodb-application: ${{ env.APPLICATION }}
          dynamodb-branch: ${{ steps.determine-trigger.outputs.dynamodb-branch }}
          cfp-project-name: ${{ env.APPLICATION }}
          cloudflare-api-token: ${{ secrets.CLOUDFLARE_PROD_TOKEN }}

      - name: Send message on failure
        uses: drata/slack-actions/send-build-notifications@2.0.5
        if: failure()
        with:
            application: ${{ env.APPLICATION }}
            environment: prod
            region: ${{ env.region }}
            channel: ${{ env.SLACK_CHANNEL }}
            slack-token: ${{ secrets.SLACK_TOKEN }}
            github-token: ${{ secrets.MEMBERS_READ_EMAILS_PAT }}
            stage: failure
