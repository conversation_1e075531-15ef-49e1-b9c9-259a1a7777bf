name: "Nx CI"

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

on:
  push:
    branches:
      - "main"
  pull_request:
    branches:
      - "main"

env:
  NPM_TOKEN: ${{secrets.NPM_REGISTRY_RO_PAT}}

jobs:
  swagger:
    runs-on: ["runs-on", "runner=tests-web", "private=true", "run-id=${{ github.run_id }}"]

    steps:
      - name: "Checkout API Repository"
        uses: "actions/checkout@v4"
        with:
          token: ${{secrets.MULTIVERSE_ACTIONS_PAT}}
          repository: "drata/api"
          ref: "release"
          persist-credentials: true
          submodules: "recursive"
          fetch-depth: 0

      - name: Set friendly branch name
        uses: drata/utils-actions/get-friendly-branch-name@1.0.2
        id: branch-name

      - name: 'Checkout branch'
        if: ${{ steps.branch-name.outputs.branch != 'main' }}
        continue-on-error: true
        run: 'git checkout -f ${{ steps.branch-name.outputs.branch }}'

      - name: "Install Node"
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: Authenticate with private NPM package registry
        uses: drata/utils-actions/authenticate-to-github-npm@1.0.0
        with:
            npm_pat: ${{ secrets.NPM_REGISTRY_RO_PAT }}

      - name: "Cache API npm dependencies"
        uses: "actions/cache@v4"
        id: "api-node-modules-cache"
        with:
          path: "./node_modules"
          key: "${{ runner.os }}-yarn-${{ hashFiles('./yarn.lock') }}"

      - name: "Install API dependencies"
        if: steps.api-node-modules-cache.outputs.cache-hit != 'true'
        run: "yarn install --non-interactive --prefer-offline --frozen-lockfile"

      - name: "Build API Swagger doc"
        run: "NODE_ENV=test NODE_CONFIG_ENV=test yarn run build --builder swc && NODE_ENV=test NODE_CONFIG_ENV=test SKIP_DB=true ./bin/drata-cli swagger:docs"

      - name: "Upload Artifact"
        uses: "actions/upload-artifact@v4"
        with:
          name: "swagger-json"
          path: "./docs/swagger/swagger.json"
          if-no-files-found: "error"
          retention-days: 1

  nx:
    runs-on: ["runs-on", "runner=tests-web", "private=true", "run-id=${{ github.run_id }}"]

    needs: ["swagger"]

    strategy:
      fail-fast: false # true after checking?
      max-parallel: 4
      matrix:
        job: ["typecheck", "lint", "test", "build"]

    steps:
      - name: "Checkout Repository"
        uses: "actions/checkout@v4"
        with:
          persist-credentials: true
          fetch-depth: 1

      - name: "Install Node"
        uses: "actions/setup-node@v4"
        with:
          node-version-file: ".nvmrc"

      - name: "Setup pnpm"
        uses: "pnpm/action-setup@v4"

      - name: "Cache npm dependencies"
        uses: "actions/cache@v4"
        id: "node-modules-cache"
        with:
          path: "node_modules"
          key: "${{ runner.os }}-pnpm-${{ hashFiles('pnpm-lock.yaml') }}"

      - name: "Install dependencies"
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        run: "pnpm install --frozen-lockfile"

      - name: "Build Tokens"
        run: "pnpm run tokens"

      - name: "Download Swagger"
        uses: "actions/download-artifact@v4"
        with:
          name: "swagger-json"
          path: "api/docs/swagger"

      - name: "Build API-SDK"
        run: "pnpm run update-api-sdk"
        env:
          API_LOCATION: "../../../api"

      - name: "Run Typecheck"
        if: matrix.job == 'typecheck'
        run:
          "pnpm run typecheck"

      - name: "Run Lint"
        if: matrix.job == 'lint'
        run:
          "pnpm run lint --quiet"

      - name: "Run Tests"
        if: matrix.job == 'test'
        run:
          "pnpm run test"

      - name: "Run Build"
        if: matrix.job == 'build'
        run:
          "pnpm run app:drata:build"
