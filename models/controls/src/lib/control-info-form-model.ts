import { isError } from 'lodash-es';
import { sharedControlsInfiniteListController } from '@controllers/controls';
import { snackbarController } from '@controllers/snackbar';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    type ControlInfoValues,
    sharedCreateControlInfoModel,
} from '@models/controls';

class ControlInfoFormModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleSubmit = (values: ControlInfoValues): void => {
        sharedCreateControlInfoModel.setControlInfoValues(values);
    };

    get storedValues(): ControlInfoValues {
        return action(() => sharedCreateControlInfoModel.controlInfoValues)();
    }

    checkControlNameExists = async (name: string): Promise<boolean> => {
        try {
            if (!sharedWorkspacesController.currentWorkspace) {
                return false;
            }

            sharedControlsInfiniteListController.controlsListInfiniteQuery.unload();
            sharedControlsInfiniteListController.loadInfiniteControls({
                q: name,
                limit: 10,
            });

            await when(() => !sharedControlsInfiniteListController.isLoading);

            const controls = action(
                () => sharedControlsInfiniteListController.controlsInfiniteList,
            )();

            return controls.some(
                (control) =>
                    control.name &&
                    control.name.toLowerCase() === name.toLowerCase(),
            );
        } catch (error) {
            const errorMessage = isError(error) ? error.message : String(error);

            snackbarController.addSnackbar({
                id: 'control-name-validation-error',
                props: {
                    title: t`Error validating control name`,
                    description: errorMessage
                        ? t`Error: ${errorMessage}`
                        : t`An unexpected error occurred`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return false;
        }
    };

    checkControlCodeExists = async (code: string): Promise<boolean> => {
        try {
            if (!sharedWorkspacesController.currentWorkspace) {
                return false;
            }

            sharedControlsInfiniteListController.controlsListInfiniteQuery.unload();
            sharedControlsInfiniteListController.loadInfiniteControls({
                q: code,
                limit: 10,
            });

            await when(() => !sharedControlsInfiniteListController.isLoading);

            const controls = action(
                () => sharedControlsInfiniteListController.controlsInfiniteList,
            )();

            return controls.some(
                (control) =>
                    control.code &&
                    control.code.toLowerCase() === code.toLowerCase(),
            );
        } catch (error) {
            const errorMessage = isError(error) ? error.message : String(error);

            snackbarController.addSnackbar({
                id: 'control-code-validation-error',
                props: {
                    title: t`Error validating control code`,
                    description: errorMessage
                        ? t`Error: ${errorMessage}`
                        : t`An unexpected error occurred`,
                    severity: 'critical',
                    closeButtonAriaLabel: t`Close`,
                },
            });

            return false;
        }
    };
}

export const sharedControlInfoFormModel = new ControlInfoFormModel();
