import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { makeAutoObservable } from '@globals/mobx';

class MapAutomatedTestsModel {
    constructor() {
        makeAutoObservable(this);
    }

    getCurrentSelectedMonitors = (): ListBoxItemData[] => {
        const { monitorsComboboxOptions, selectedMonitors } =
            sharedMonitorsInfiniteController;

        return selectedMonitors.map((monitor) => {
            const fullMonitor = monitorsComboboxOptions.find(
                (option) => option.id === monitor.id,
            );

            if (!fullMonitor) {
                return {
                    id: monitor.id,
                    label: monitor.name,
                    value: monitor.id,
                    monitorData: {
                        id: Number(monitor.id),
                        testId: monitor.testId,
                    },
                    tag: {
                        label: monitor.checkResultStatus || 'Unknown',
                        colorScheme: 'neutral',
                    },
                };
            }

            return fullMonitor;
        });
    };
}

export const sharedMapAutomatedTestsModel = new MapAutomatedTestsModel();
