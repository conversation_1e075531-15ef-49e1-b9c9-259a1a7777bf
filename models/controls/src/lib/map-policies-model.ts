import { isNil } from 'lodash-es';
import { sharedPoliciesLibraryInfiniteListController } from '@controllers/policies';
import type { PolicyTableResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';

export const MAP_POLICIES_MODAL_ID = 'map-policies-modal';

class MapPoliciesModel {
    selectedPolicies: PolicyTableResponseDto[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    isPolicyPublished(policy: PolicyTableResponseDto): boolean {
        return !isNil(policy.version) && !isNil(policy.version.publishedAt);
    }

    getCurrentSelectedPolicies() {
        const { policiesListAsItems, mapPolicyToItem } =
            sharedPoliciesLibraryInfiniteListController;

        return this.selectedPolicies.map((policy) => {
            const fullPolicyItem = policiesListAsItems.find(
                (option) => String(policy.id) === option.id,
            );

            if (!fullPolicyItem) {
                return mapPolicyToItem(policy);
            }

            return fullPolicyItem;
        });
    }
}

export const sharedMapPoliciesModel = new MapPoliciesModel();
