import {
    type MappedRequirement,
    sharedRequirementsController,
    sharedRequirementsInfiniteController,
} from '@controllers/requirements';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

class MapRequirementsModel {
    constructor() {
        makeAutoObservable(this);
    }

    getFrameworkMappedRequirements(frameworkId: number): MappedRequirement[] {
        const { mappedRequirements } = sharedRequirementsController;

        return mappedRequirements.filter(
            (req) => req.frameworkId === frameworkId,
        );
    }

    getFrameworkSelectedOptions(frameworkId: number): ListBoxItemData[] {
        const currentMappedRequirements =
            this.getFrameworkMappedRequirements(frameworkId);
        const { requirementsInfiniteList } =
            sharedRequirementsInfiniteController;

        return currentMappedRequirements.map((req) => {
            const fullRequirement = requirementsInfiniteList.find(
                (listReq) => listReq.id === req.id,
            );

            if (!fullRequirement) {
                return {
                    id: `${req.id}`,
                    label: req.name,
                    value: `${req.id}`,
                    description: req.description || t`No description available`,
                };
            }

            return {
                id: `${req.id}`,
                label: fullRequirement.name || req.name,
                value: `${req.id}`,
                description:
                    fullRequirement.description ||
                    req.description ||
                    t`No description available`,
            };
        });
    }

    getFormattedOptions(): ListBoxItemData[] {
        const { requirementsInfiniteList } =
            sharedRequirementsInfiniteController;

        return requirementsInfiniteList.map((requirement) => ({
            id: `${requirement.id}`,
            label: requirement.name,
            value: `${requirement.id}`,
            description: requirement.description || t`No description available`,
        }));
    }
}

export const sharedMapRequirementsModel = new MapRequirementsModel();
