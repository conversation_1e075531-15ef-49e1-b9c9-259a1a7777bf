import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import { t } from '@globals/i18n/macro';

export const getPoliciesApprovalStatusOptions =
    (): RadioFieldGroupProps['options'] => [
        {
            label: t({
                message: 'All statuses',
                comment: 'Option to show all approval statuses',
            }),
            value: '',
        },
        {
            label: t({
                message: 'New',
                comment: 'Draft policy status',
            }),
            value: 'DRAFT',
        },
        {
            label: t({
                message: 'Needs approval',
                comment: 'Policy needs approval status',
            }),
            value: 'NEEDS_APPROVAL',
        },
        {
            label: t({
                message: 'Approved',
                comment: 'Policy approved status',
            }),
            value: 'APPROVED',
        },
        {
            label: t({
                message: 'Published',
                comment: 'Policy published status',
            }),
            value: 'PUBLISHED',
        },
    ];

export const getPoliciesRenewalStatusOptions =
    (): RadioFieldGroupProps['options'] => [
        {
            label: t({
                message: 'All renewal statuses',
                comment: 'Option to show all renewal statuses',
            }),
            value: '',
        },
        {
            label: t({
                message: 'Renewal required soon',
                comment: 'Policy renewal required soon status',
            }),
            value: 'EXPIRE_SOON',
        },
        {
            label: t({
                message: 'Renewal past due',
                comment: 'Policy renewal past due status',
            }),
            value: 'EXPIRED',
        },
    ];

export const POLICIES_STATUS_OPTIONS = [
    {
        id: 'ACTIVE',
        label: 'Active',
        value: 'ACTIVE',
    },
    {
        id: 'ARCHIVED',
        label: 'Archived',
        value: 'ARCHIVED',
    },
    {
        id: 'REPLACED',
        label: 'Replaced',
        value: 'REPLACED',
    },
    {
        id: 'UNACCEPTABLE',
        label: 'Unacceptable',
        value: 'UNACCEPTABLE',
    },
    {
        id: 'OUTDATED',
        label: 'Outdated',
        value: 'OUTDATED',
    },
];
