import { isEmpty } from 'lodash-es';
import { sharedEvidenceDetailsController } from '@controllers/evidence-library';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedEvidenceDetailsModel } from '@models/evidence-library-details';

export class EvidenceDetailsContentNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get tabs(): { topicPath: string; label: string }[] {
        const { evidenceDetails, isTestEvidence } = sharedEvidenceDetailsModel;

        if (sharedEvidenceDetailsController.isLoading || !evidenceDetails) {
            return [];
        }

        const { id: evidenceId } = evidenceDetails;

        const baseContentNav = [
            {
                topicPath: `compliance/evidence/${evidenceId}/overview`,
                label: t`Overview`,
            },
            {
                topicPath: `compliance/evidence/${evidenceId}/controls`,
                label: t`Controls`,
            },
        ];

        if (isTestEvidence) {
            return [
                ...baseContentNav,
                {
                    topicPath: `compliance/evidence/${evidenceId}/test-evidence`,
                    label: t`Test evidence`,
                },
            ];
        }

        return [...baseContentNav, ...this.artifactsTabs];
    }

    get artifactsTabs(): { topicPath: string; label: string }[] {
        const { evidenceDetails, pastArtifacts } = sharedEvidenceDetailsModel;

        if (!evidenceDetails) {
            return [];
        }

        const { id: evidenceId } = evidenceDetails;

        return [
            {
                topicPath: `compliance/evidence/${evidenceId}/current-artifacts`,
                label: t`Current artifacts`,
            },
            ...(isEmpty(pastArtifacts)
                ? []
                : [
                      {
                          topicPath: `compliance/evidence/${evidenceId}/past-artifacts`,
                          label: t`Past artifacts`,
                      },
                  ]),
        ];
    }
}
