import { isEmpty, isString } from 'lodash-es';
import type { DomainsNav } from '@controllers/route';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';

export class DomainsNavModel {
    constructor() {
        makeAutoObservable(this);
    }

    get generalTopicsOrder(): string[] {
        return [
            sharedFeatureAccessModel.isQuickStartReadEnabled
                ? 'nav.domains.general.quick-start'
                : null,

            sharedFeatureAccessModel.isDashboardDomainReadEnabled
                ? 'nav.domains.general.dashboard'
                : null,

            sharedFeatureAccessModel.isTasksDomainReadEnabled
                ? 'nav.domains.general.tasks'
                : null,
        ].filter(isString);
    }

    get complianceManagementTopicsOrder(): string[] {
        return [
            sharedFeatureAccessModel.isComplianceDomainReadEnabled
                ? 'nav.domains.compliance-management.compliance'
                : null,
            sharedFeatureAccessModel.isRiskDomainReadEnabled
                ? 'nav.domains.compliance-management.risk'
                : null,
            sharedFeatureAccessModel.isVendorsDomainReadEnabled
                ? 'nav.domains.compliance-management.vendors'
                : null,
            sharedFeatureAccessModel.isGovernanceDomainReadEnabled
                ? 'nav.domains.compliance-management.governance'
                : null,
            sharedFeatureAccessModel.isTrustDomainReadEnabled
                ? 'nav.domains.compliance-management.trust'
                : null,
            sharedFeatureAccessModel.isLibraryDomainReadEnabled
                ? 'nav.domains.compliance-management.library'
                : null,
        ].filter(isString);
    }

    get settingsTopicsOrder(): string[] {
        return [
            sharedFeatureAccessModel.isConnectionsDomainReadEnabled
                ? 'nav.domains.settings.connections'
                : null,

            sharedFeatureAccessModel.isEventsDomainReadEnabled
                ? 'nav.domains.settings.events'
                : null,

            sharedFeatureAccessModel.isSettingsDomainReadEnabled
                ? 'nav.domains.settings.settings'
                : null,
        ].filter(isString);
    }

    get navDomainsOrder(): string[] {
        return [
            isEmpty(this.generalTopicsOrder) ? null : 'nav.domains.general',

            isEmpty(this.complianceManagementTopicsOrder)
                ? null
                : 'nav.domains.compliance-management',

            isEmpty(this.settingsTopicsOrder) ? null : 'nav.domains.settings',
        ].filter(isString);
    }

    get navigation(): DomainsNav {
        return {
            id: 'nav.domains',
            title: t`Compliance`,
            domainsOrder: this.navDomainsOrder,
            domains: {
                'nav.domains.general': {
                    label: t`General`,
                    hideLabel: true,
                    topicsOrder: this.generalTopicsOrder,
                    topics: {
                        'nav.domains.general.quick-start': {
                            id: 'nav.domains.general.quick-start',
                            topicPath: 'quick-start',
                            label: t`Quick start`,
                            icon: 'QuickStart',
                        },
                        'nav.domains.general.dashboard': {
                            id: 'nav.domains.general.dashboard',
                            topicPath: 'dashboard',
                            label: t`Dashboard`,
                            icon: 'Home',
                        },
                        'nav.domains.general.tasks': {
                            id: 'nav.domains.general.tasks',
                            topicPath: 'tasks',
                            label: t`Tasks`,
                            icon: 'Task',
                        },
                    },
                },

                'nav.domains.compliance-management': {
                    label: 'Compliance management',
                    hideLabel: true,
                    topicsOrder: this.complianceManagementTopicsOrder,
                    topics: {
                        'nav.domains.compliance-management.compliance': {
                            id: 'nav.domains.compliance-management.compliance',
                            topicPath: 'compliance',
                            label: t`Compliance`,
                            icon: 'Controls',
                        },
                        'nav.domains.compliance-management.risk': {
                            id: 'nav.domains.compliance-management.risk',
                            topicPath: 'risk',
                            label: t`Risk`,
                            icon: 'Risk',
                        },
                        'nav.domains.compliance-management.vendors': {
                            id: 'nav.domains.compliance-management.vendors',
                            topicPath: 'vendors',
                            label: t`Vendors`,
                            icon: 'Vendors',
                        },
                        'nav.domains.compliance-management.governance': {
                            id: 'nav.domains.compliance-management.governance',
                            topicPath: 'governance',
                            label: t`Governance`,
                            icon: 'Governance',
                        },
                        'nav.domains.compliance-management.trust': {
                            id: 'nav.domains.compliance-management.trust',
                            topicPath: 'trust',
                            label: t`Trust`,
                            icon: 'TrustCenter',
                        },
                        'nav.domains.compliance-management.library': {
                            id: 'nav.domains.compliance-management.library',
                            topicPath: 'library',
                            label: t`Library`,
                            icon: 'Observations',
                        },
                    },
                },

                'nav.domains.settings': {
                    label: 'Settings',
                    hideLabel: true,
                    topicsOrder: this.settingsTopicsOrder,
                    topics: {
                        'nav.domains.settings.connections': {
                            id: 'nav.domains.settings.connections',
                            topicPath: 'connections',
                            label: 'Connections',
                            icon: 'Connections',
                        },
                        'nav.domains.settings.events': {
                            id: 'nav.domains.settings.events',
                            topicPath: 'events',
                            label: t`Events`,
                            icon: 'Events',
                        },
                        'nav.domains.settings.settings': {
                            id: 'nav.domains.settings.settings',
                            topicPath: 'settings',
                            label: t`Settings`,
                            icon: 'Settings',
                        },
                    },
                },
            },
        };
    }
}

export const sharedDomainsNavModel = new DomainsNavModel();
