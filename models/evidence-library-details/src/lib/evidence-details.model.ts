import { find, isEmpty, isNil, pick, uniqBy } from 'lodash-es';
import {
    sharedEvidenceDetailsController,
    sharedEvidenceTestResultsController,
} from '@controllers/evidence-library';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type {
    ControlMonitorResponseDto,
    EvidenceResponseDto,
    LibraryDocumentVersionResponseDto,
    LinkedWorkspaceResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { type BaseProvider, providers } from '@globals/providers';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    type EvidenceStatusType,
    getCheckResultStatus,
    getEvidenceSourceLabel,
    getEvidenceStatus,
    getRequestDescriptionForEvidence,
} from '@helpers/evidence';
import { getFullName, getInitials } from '@helpers/formatters';

export class EvidenceDetailsModel {
    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return sharedEvidenceDetailsController.isLoading;
    }

    get evidenceDetails(): EvidenceResponseDto | null {
        return sharedEvidenceDetailsController.evidenceDetailsData;
    }

    get controls(): ControlMonitorResponseDto[] {
        return this.evidenceDetails?.controls ?? [];
    }

    get currentLibraryVersion(): LibraryDocumentVersionResponseDto | null {
        return find(this.evidenceDetails?.versions, ['current', true]) ?? null;
    }

    get isTestEvidence(): boolean {
        return this.currentLibraryVersion?.type === 'TEST_RESULT';
    }

    get linkedWorkspaces(): LinkedWorkspaceResponseDto[] {
        const { currentWorkspace } = sharedWorkspacesController;

        if (this.isTestEvidence && currentWorkspace) {
            return [pick(currentWorkspace, ['name', 'id', 'logo'])];
        }

        return uniqBy(this.evidenceDetails?.linkedWorkspaces ?? [], 'id');
    }

    get evidenceLibraryVersions(): LibraryDocumentVersionResponseDto[] {
        return (this.evidenceDetails?.versions ?? []).filter(
            (version) => version.type !== 'NONE',
        );
    }

    get currentArtifacts(): LibraryDocumentVersionResponseDto[] {
        return this.evidenceLibraryVersions.filter(({ current }) => current);
    }

    get pastArtifacts(): LibraryDocumentVersionResponseDto[] {
        return this.evidenceLibraryVersions.filter(({ current }) => !current);
    }

    get owner(): {
        id: string;
        fullName: string;
        initials: string;
        email: string;
        avatarUrl: string;
    } | null {
        if (!this.evidenceDetails?.user) {
            return null;
        }

        const { user } = this.evidenceDetails;

        const fullName = getFullName(user.firstName, user.lastName);
        const initials = getInitials(fullName);

        return {
            email: user.email,
            fullName,
            initials,
            avatarUrl: user.avatarUrl ?? '',
            id: user.id.toString(),
        };
    }

    get evidenceStatus(): EvidenceStatusType | null {
        if (isNil(this.currentLibraryVersion)) {
            return null;
        }

        return getEvidenceStatus(
            this.currentLibraryVersion,
            this.evidenceDetails?.renewalSchema.renewalDate ?? null,
        );
    }

    get evidenceConnections(): BaseProvider[] {
        const { evidenceTestResults } = sharedEvidenceTestResultsController;

        return evidenceTestResults
            .map(({ clientType }) => {
                return providers[clientType as keyof typeof providers];
            })
            .filter(Boolean);
    }

    get requestDescription(): string | null {
        const requestDescriptor =
            this.currentLibraryVersion?.testResultMap?.controlTestInstance
                .monitorInstances[0]?.parsedRequestDescriptions ?? null;

        if (isNil(requestDescriptor)) {
            return null;
        }

        return getRequestDescriptionForEvidence(
            requestDescriptor,
            this.evidenceConnections,
        );
    }

    get evidenceSource(): string {
        const clientType =
            this.currentLibraryVersion?.libraryDocumentVersionSync?.connection
                .clientType ?? null;

        return getEvidenceSourceLabel(
            this.currentLibraryVersion?.type,
            clientType,
        );
    }

    get latestTestResult(): KeyValuePairProps['value'] | undefined {
        if (isNil(this.currentLibraryVersion)) {
            return;
        }

        return getCheckResultStatus(this.currentLibraryVersion);
    }

    get latestSuccessResult(): string | undefined {
        if (isNil(this.currentLibraryVersion)) {
            return undefined;
        }

        return (
            this.currentLibraryVersion.testResultMap?.controlTestInstance
                .mostRecentHistoryRecord.lastSuccess?.createdAt ?? ''
        );
    }

    get isEvidenceGuidance(): boolean {
        return !isEmpty(this.evidenceDetails?.evidenceTemplateCode);
    }
}

export const sharedEvidenceDetailsModel = new EvidenceDetailsModel();
