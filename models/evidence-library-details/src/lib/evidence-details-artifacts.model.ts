import {
    sharedEvidenceDetailsArtifactsController,
    sharedEvidenceDetailsArtifactsMutationController,
} from '@controllers/evidence-library';
import type {
    BulkAction,
    DatatableRowSelectionState,
    FilterProps,
} from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type { LibraryDocumentVersionResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import { getDateFilterOptions } from './constants/get-date-filters-options.constant';

export class EvidenceDetailsArtifactsModel {
    selectedArtifacts: LibraryDocumentVersionResponseDto[] = [];
    isAllRowsSelected = false;
    initialArtifactsCount: number | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    get selectedEvidenceIds(): number[] {
        return this.selectedArtifacts.map((artifact) => artifact.id);
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;
        const { evidenceDetailsArtifacts } =
            sharedEvidenceDetailsArtifactsController;
        const selectedIndices = Object.keys(selectedRows);
        const selectedRowData = selectedIndices.map(
            (index) => evidenceDetailsArtifacts[parseInt(index)],
        );

        this.selectedArtifacts = selectedRowData
            .map((row) => row)
            .filter(Boolean);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleDeleteArtifacts = (): void => {
        openConfirmationModal({
            title: t`Are you sure?`,
            body: t`This will permanently delete the selected artifacts.`,
            confirmText: t`Delete`,
            cancelText: t`Cancel`,
            type: 'danger',
            onConfirm: () => {
                sharedEvidenceDetailsArtifactsMutationController.deleteArtifact(
                    this.selectedEvidenceIds,
                    this.isAllRowsSelected,
                );
                closeConfirmationModal();
            },
            onCancel: closeConfirmationModal,
        });
    };

    get bulkActions(): BulkAction[] {
        const { hasWriteEvidenceLibraryPermission } = sharedFeatureAccessModel;

        if (!hasWriteEvidenceLibraryPermission) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'bulk-actions-delete-artifacts',
                typeProps: {
                    label: t`Delete artifacts`,
                    level: 'tertiary',
                    onClick: this.handleDeleteArtifacts,
                },
            },
        ];
    }

    get #sourceFilter(): Filter {
        return {
            filterType: 'checkbox',
            id: 'source',
            label: t`Source`,
            options: [
                { label: t`URL`, value: 'URL' },
                {
                    label: t`File`,
                    value: 'S3_FILE,GOOGLE_DRIVE,ONE_DRIVE,BOX,DROPBOX,SHARE_POINT',
                },
                { label: t`Ticket`, value: 'TICKET_PROVIDER' },
            ],
        };
    }

    get #creationDateFilter(): Filter {
        return {
            filterType: 'select',
            id: 'creationDate',
            label: t`Creation date`,
            options: getDateFilterOptions(),
            placeholder: t`Select a date`,
        };
    }

    get #renewalDateFilter(): Filter {
        return {
            filterType: 'select',
            id: 'renewalDate',
            label: t`Renewal date`,
            options: getDateFilterOptions(),
            placeholder: t`Select a date`,
        };
    }

    get filters(): FilterProps {
        return {
            clearAllButtonLabel: t`Clear all`,
            filters: [
                this.#creationDateFilter,
                this.#renewalDateFilter,
                this.#sourceFilter,
            ],
            triggerLabel: t`Filters`,
        };
    }
}

export const sharedEvidenceDetailsArtifactsModel =
    new EvidenceDetailsArtifactsModel();
