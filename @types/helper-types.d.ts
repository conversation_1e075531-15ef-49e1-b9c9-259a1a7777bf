/**
 * Global Helper types.
 */

type Get<
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- needed for generics
    Obj extends Record<string, any>,
    Key extends string,
    Fallback,
> = Key extends keyof Obj ? Obj[Key] : Fallback;

type PickByValue<OBJ_T, VALUE_T> = // From https://stackoverflow.com/a/55153000
    Pick<
        OBJ_T,
        {
            [K in keyof OBJ_T]: OBJ_T[K] extends VALUE_T ? K : never;
        }[keyof OBJ_T]
    >;

type ObjectEntries<OBJ_T> = // From https://stackoverflow.com/a/60142095
    {
        [K in keyof OBJ_T]: {
            key: keyof PickByValue<OBJ_T, OBJ_T[K]>;
            value: OBJ_T[K];
        };
    }[keyof OBJ_T][];

type ElementOf<T> = T extends (infer U)[] ? Required<U> : never;

type NullNotUndefined<T> = T extends undefined ? null : T;

type NotUndefined<T> = T extends undefined ? never : T;

type NotSymbol<T> = T extends symbol ? never : T;

type ExtractQueryParam<
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Can't be expressed otherwise
    T extends { query?: Partial<Record<string, any>> },
    K extends keyof Required<T>['query'],
> = Required<Required<T>['query'][K]>;

type ExtractQueryParams<
    // eslint-disable-next-line @typescript-eslint/no-explicit-any -- Can't be expressed otherwise
    T extends { query?: Partial<Record<string, any>> },
    K extends keyof Required<T>['query'],
> = Required<Pick<Required<T>['query'], K>>;
