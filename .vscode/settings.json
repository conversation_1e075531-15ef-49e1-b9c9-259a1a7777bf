{
    "[ignore]": {
        "editor.defaultFormatter": "foxundermoon.shell-format"
    },
    // shell formatter override
    "[shellscript]": {
        // https://marketplace.visualstudio.com/items?itemName=foxundermoon.shell-format
        "editor.defaultFormatter": "foxundermoon.shell-format"
    },
    "[javascript]": {
        "editor.defaultFormatter": "biomejs.biome"
    },
    "[typescript]": {
        "editor.defaultFormatter": "biomejs.biome"
    },
    "[typescriptreact]": {
        "editor.defaultFormatter": "biomejs.biome"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "biomejs.biome"
    },
    "[markdown]": {
        "editor.defaultFormatter": "biomejs.biome"
    },
    "[json]": {
        "editor.defaultFormatter": "biomejs.biome"
    },
    "[yaml]": {
        "editor.insertSpaces": true,
        "editor.tabSize": 2,
        "editor.autoIndent": "keep",
        "diffEditor.ignoreTrimWhitespace": true,
        "editor.defaultColorDecorators": "never",
        "gitlens.codeLens.scopes": ["document"],
        "editor.quickSuggestions": {
            "other": true,
            "comments": false,
            "strings": true
        },
        "editor.defaultFormatter": "redhat.vscode-yaml"
    },

    // spell checker settings
    // https://marketplace.visualstudio.com/items?itemName=streetsidesoftware.code-spell-checker
    "cSpell.checkOnlyEnabledFileTypes": false,

    // CSS settings - more to follow when more css is being written
    "css.format.maxPreserveNewLines": 1,
    "css.format.spaceAroundSelectorSeparator": true,
    "css.lint.duplicateProperties": "error",
    "css.lint.float": "warning",

    "debug.javascript.breakOnConditionalError": true,

    // Make the vscode diff and merge editor nicer
    "diffEditor.diffAlgorithm": "advanced",
    "diffEditor.experimental.showMoves": true,
    "diffEditor.ignoreTrimWhitespace": true,

    // WARNING: HERE BE DRAGONS
    "editor.codeActionsOnSave": {
        // Run `eslint --fix` on the file when explicitly saved (not on autosave)
        "source.fixAll.eslint": "explicit",
        // vscode's import organization is dumber than a rock (and enabled by default)
        // use the eslint plugin instead
        "source.organizeImports": "never"
    },

    // This "feature" should be listed as a war crime by the Geneva Convention
    "editor.dragAndDrop": false,

    // Always autoformat
    "editor.formatOnSave": true,

    // If you have the same file open twice, then edits in one should appear in the other
    "editor.linkedEditing": true,

    // Rarely useful, never harmful
    "editor.occurrencesHighlight": "off",

    // If you turn this off then you might be interested in medieval torture techniques
    "editor.showFoldingControls": "always",

    // This, and all other _sticky_ options are such a wonderful UX improvement that
    // you will literally not be able to go back
    "editor.stickyScroll.enabled": true,

    // You almost always want the closest thing to be autocompleted
    "editor.suggest.localityBonus": true,

    // INFO level messages will spam you if you let them
    "errorLens.enabledDiagnosticLevels": ["error", "warning"],

    // Honestly don't remember why this setting is here or what it does
    "eslint.onIgnoredFiles": "warn",

    // Makes it behave like all desktop file managers
    "explorer.incrementalNaming": "smart",

    // Be good citizens
    "files.trimTrailingWhitespace": true,

    // Make git integration behave reasonably
    "git.autofetch": "all",
    "git.autoStash": true,
    "git.closeDiffOnOperation": true,
    "git.confirmSync": false,
    "git.enableCommitSigning": true,
    "git.fetchOnPull": true,
    "git.followTagsWhenSync": true,
    "git.openRepositoryInParentFolders": "always",
    "git.pruneOnFetch": true,
    "git.pullBeforeCheckout": true,
    "git.rebaseWhenSync": true,

    // bare minimum html formatting fixes, more to come
    "html.format.indentHandlebars": true,
    "html.format.indentInnerHtml": true,
    "html.format.maxPreserveNewLines": 1,
    "html.format.templating": true,
    "html.format.wrapAttributes": "aligned-multiple",
    "html.format.wrapLineLength": 80,

    // we live in the worst timeline because of ASI
    "javascript.format.semicolons": "insert",
    // Who wouldn't want this to be the default???
    "javascript.updateImportsOnFileMove.enabled": "always",

    // turn typescript on for js projects
    "js/ts.implicitProjectConfig.checkJs": true,

    // go to next after you fix something
    "merge-conflict.autoNavigateNextConflict.enabled": true,

    "multiDiffEditor.experimental.enabled": true,

    // inherited value above is to always ignore whitespace in diffs
    "scm.diffDecorationsIgnoreTrimWhitespace": "inherit",

    // should be the default
    "search.showLineNumbers": true,
    "security.promptForLocalFileProtocolHandling": false,
    "security.workspace.trust.enabled": false,
    "security.workspace.trust.untrustedFiles": "open",

    // shfmt is love, shfmt is life
    // https://marketplace.visualstudio.com/items?itemName=foxundermoon.shell-format
    "shellformat.useEditorConfig": true,

    // personal preference
    // https://marketplace.visualstudio.com/items?itemName=Tyriar.sort-lines
    "sortLines.filterBlankLines": true,

    // I don't actually use the integrated terminal
    "terminal.integrated.stickyScroll.enabled": true,

    // disable this because it gets the editor out of sync with the project
    "typescript.disableAutomaticTypeAcquisition": true,
    // again, ASI is a curse
    "typescript.format.semicolons": "insert",
    // Sometimes makes TS hovers give better info
    "typescript.implementationsCodeLens.enabled": true,
    // Also want the eslint rule to force the same thing
    "typescript.preferences.preferTypeOnlyAutoImports": true,
    // surveys are bad
    "typescript.surveys.enabled": false,
    "typescript.tsserver.enableTracing": false,
    "typescript.workspaceSymbols.scope": "currentProject",
    "typescript.tsserver.useSyntaxServer": "never",
    "typescript.tsserver.log": "off",
    "typescript.validate.enable": true,
    "typescript.updateImportsOnFileMove.enabled": "always",

    "javascript.validate.enable": true,

    // should be defaults
    // https://marketplace.visualstudio.com/items?itemName=vscode-icons-team.vscode-icons
    "vsicons.dontShowNewVersionMessage": true,
    "vsicons.projectDetection.autoReload": true,

    // You always want to see what tabs have changes
    "workbench.editor.highlightModifiedTabs": true,

    // Don't open multiple tabs of the same file on accident
    "workbench.editor.revealIfOpen": true,

    // Microsoft uses experiments to spy on users and show ads
    "workbench.enableExperiments": false,

    // vscode-icons is the best icon package
    "workbench.iconTheme": "vscode-icons",

    // I've only ever needed this setting here at Drata :cry:
    "workbench.list.horizontalScrolling": true,
    // more sticky more good
    "workbench.tree.enableStickyScroll": true,

    // YAML is a curse
    // https://marketplace.visualstudio.com/items?itemName=redhat.vscode-yaml
    "yaml.format.proseWrap": "always",

    "search.exclude": {
        "**/*.code-search": true,
        "**/bower_components": true,
        "**/node_modules": true,
        "**/package-lock.json": true,
        "**/yarn.lock": true
    },

    "files.exclude": {
        "**/.swc": true,
        "**/.nx": true,
        "**/.react-router": true,
        "**/.wrangler": true,
        "**/dist": true,
        "**/build": true,
        "**/tmp": true,
        "**/vite.config.ts.timestamp*": true
    },
    "explorer.fileNesting.enabled": true,
    "explorer.fileNesting.expand": false,
    "explorer.fileNesting.patterns": {
        "package.json": "package-lock.json, yarn.lock, pnpm-lock.yaml, pnpm-workspace.yaml, bun.lockb, project.json, biome.json, .*, *.config.mjs, *.config.js, *.config.ts",
        "*.ts": "${basename}.spec.ts"
    },
    "cSpell.words": [
        "arktype",
        "autorun",
        "biomejs",
        "ckeditor",
        "datatable",
        "drata",
        "fsecond",
        "lingui",
        "nav",
        "snackbar",
        "subheadline",
        "tanstack",
        "typeform",
        "vite",
        "vitest"
    ],
    "editor.defaultFormatter": "biomejs.biome",
    "yaml.extension.recommendations": false
}
