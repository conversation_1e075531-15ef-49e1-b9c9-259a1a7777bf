import { find, isNil } from 'lodash-es';
import {
    evidenceLibraryControllerGetArtifactsByEvidenceOptions,
    evidenceLibraryControllerGetEvidenceOptions,
} from '@globals/api-sdk/queries';
import type {
    ControlMonitorResponseDto,
    EvidenceResponseDto,
    LibraryDocumentVersionResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { queryClient } from '@globals/query-client';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedEvidenceDetailsArtifactsController } from './evidence-details-artifacts.controller';
import { sharedEvidenceTestResultsController } from './evidence-test-results.controller';

class EvidenceDetailsController {
    constructor() {
        makeAutoObservable(this);
    }

    evidenceDetailsQuery = new ObservedQuery(
        evidenceLibraryControllerGetEvidenceOptions,
    );

    evidenceDetailsArtifactsQuery = new ObservedQuery(
        evidenceLibraryControllerGetArtifactsByEvidenceOptions,
    );

    loadEvidenceDetails = (evidenceId: number): void => {
        when(
            () =>
                sharedWorkspacesController.isLoaded &&
                !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                this.evidenceDetailsQuery.load({
                    path: {
                        id: evidenceId,
                        xProductId: currentWorkspace?.id as number,
                    },
                });
            },
        );

        when(
            () => {
                return (
                    !this.isLoading &&
                    this.currentLibraryVersion?.type === 'TEST_RESULT'
                );
            },
            () => {
                if (!this.currentLibraryVersion) {
                    return;
                }

                const { testResultMap } = this.currentLibraryVersion;
                const { controlTestInstance } = testResultMap ?? {};

                if (!controlTestInstance?.id) {
                    return;
                }

                sharedEvidenceTestResultsController.loadEvidenceTestsResult(
                    controlTestInstance.id,
                );
            },
        );
    };

    initEvidenceDetailsLoad = (evidenceId: number): void => {
        this.loadEvidenceDetails(evidenceId);
    };

    invalidateEvidenceDetails = () => {
        this.evidenceDetailsQuery.invalidate();
    };

    get queryKey() {
        return this.evidenceDetailsQuery.query?.queryOptions.queryKey;
    }

    get isLoading(): boolean {
        return (
            this.evidenceDetailsQuery.isLoading ||
            sharedWorkspacesController.isLoading
        );
    }

    get evidenceDetailsData(): EvidenceResponseDto | null {
        return this.evidenceDetailsQuery.data;
    }

    get currentLibraryVersion(): LibraryDocumentVersionResponseDto | null {
        return (
            find(this.evidenceDetailsData?.versions, ['current', true]) ?? null
        );
    }

    get evidenceDetailsLinkedControls(): ControlMonitorResponseDto[] {
        return this.evidenceDetailsQuery.data?.controls ?? [];
    }

    setEvidenceDetailsQueryData = (data: EvidenceResponseDto): void => {
        if (!this.queryKey) {
            return;
        }

        queryClient.setQueryData(this.queryKey, data);
        sharedEvidenceDetailsArtifactsController.invalidateArtifacts();
    };
}

export const sharedEvidenceDetailsController = new EvidenceDetailsController();
