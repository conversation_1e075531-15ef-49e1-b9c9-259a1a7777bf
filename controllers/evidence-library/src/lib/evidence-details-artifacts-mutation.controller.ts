import { snackbarController } from '@controllers/snackbar';
import { evidenceLibraryControllerDeleteBulkArtifactMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedEvidenceDetailsController } from './evidence-details.controller';
import { sharedEvidenceDetailsArtifactsController } from './evidence-details-artifacts.controller';

class EvidenceDetailsArtifactsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    evidenceDetailsDeleteArtifactMutation = new ObservedMutation(
        evidenceLibraryControllerDeleteBulkArtifactMutation,
        {
            onSuccess: () => {
                sharedEvidenceDetailsArtifactsController.invalidateArtifacts();
            },
        },
    );

    deleteArtifact = (artifactsIds: number[], isAllRowsSelected = false) => {
        const { evidenceDetailsData } = sharedEvidenceDetailsController;

        if (!evidenceDetailsData) {
            throw new Error('No evidence details found.');
        }
        const evidenceId = evidenceDetailsData.id;

        this.evidenceDetailsDeleteArtifactMutation.mutate({
            body: {
                artifactsIds,
                isAllRowsSelected,
                evidenceId,
            },
        });

        when(
            () => !this.evidenceDetailsDeleteArtifactMutation.isPending,
            () => {
                if (this.evidenceDetailsDeleteArtifactMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'artifact-deleted-error',
                        props: {
                            title: t`Artifact could not be deleted.`,
                            description:
                                this.evidenceDetailsDeleteArtifactMutation.error
                                    ?.message,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    return;
                }

                snackbarController.addSnackbar({
                    id: 'artifact-deleted-successfully',
                    props: {
                        title: t`Artifact has been deleted successfully.`,
                        severity: 'success',
                        closeButtonAriaLabel: t`Close`,
                    },
                });
            },
        );
    };
}

export const sharedEvidenceDetailsArtifactsMutationController =
    new EvidenceDetailsArtifactsMutationController();
