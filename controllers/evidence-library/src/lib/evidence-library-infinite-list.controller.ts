import { evidenceLibraryControllerListEvidenceInfiniteOptions } from '@globals/api-sdk/queries';
import type { EvidenceResponseDto } from '@globals/api-sdk/types';
import {
    action,
    makeAutoObservable,
    ObservedInfiniteQuery,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedMapEvidenceModel } from '@models/controls';

class EvidenceLibraryInfiniteListController {
    #lastSearchQuery = '';

    constructor() {
        makeAutoObservable(this);
    }

    addSelectedEvidence(evidence: EvidenceResponseDto[]) {
        action(() => {
            const newEvidence = evidence.filter(
                (item) =>
                    !sharedMapEvidenceModel.selectedEvidence.some(
                        (existing) => existing.id === item.id,
                    ),
            );

            sharedMapEvidenceModel.selectedEvidence = [
                ...sharedMapEvidenceModel.selectedEvidence,
                ...newEvidence,
            ];
        })();
    }

    removeEvidence(id: number) {
        action(() => {
            sharedMapEvidenceModel.selectedEvidence =
                sharedMapEvidenceModel.selectedEvidence.filter(
                    (item) => item.id !== id,
                );
        })();
    }

    clearAllEvidence() {
        action(() => {
            sharedMapEvidenceModel.selectedEvidence = [];
        })();
    }

    evidenceListInfiniteQuery = new ObservedInfiniteQuery(
        evidenceLibraryControllerListEvidenceInfiniteOptions,
    );

    get evidenceList(): EvidenceResponseDto[] {
        return (
            this.evidenceListInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get isLoading(): boolean {
        return this.evidenceListInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.evidenceListInfiniteQuery.isFetching;
    }

    get hasNextPage(): boolean {
        return this.evidenceListInfiniteQuery.hasNextPage;
    }

    load = (search?: string): void => {
        this.#lastSearchQuery = search ?? '';

        when(
            () => !sharedWorkspacesController.isLoading,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    return;
                }

                this.evidenceListInfiniteQuery.load({
                    path: { xProductId: currentWorkspace.id },
                    query: {
                        page: 1,
                        limit: 20,
                        q: this.#lastSearchQuery,
                    },
                });
            },
        );
    };

    loadNextPage = ({ search }: { search?: string } = {}): void => {
        if (search !== undefined && search !== this.#lastSearchQuery) {
            this.search(search);

            return;
        }

        if (this.hasNextPage && !this.isFetching) {
            this.evidenceListInfiniteQuery.nextPage();
        }
    };

    search = (searchTerm: string): void => {
        if (searchTerm === this.#lastSearchQuery) {
            return;
        }

        this.#lastSearchQuery = searchTerm;
        this.evidenceListInfiniteQuery.unload();

        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.evidenceListInfiniteQuery.load({
            path: { xProductId: currentWorkspace.id },
            query: {
                page: 1,
                limit: 20,
                q: searchTerm,
            },
        });
    };

    fetchOptions({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }) {
        if (increasePage) {
            this.loadNextPage({
                search,
            });
        } else {
            this.search(search || '');
        }
    }

    invalidateEvidenceList = (): void => {
        this.evidenceListInfiniteQuery.invalidate();
    };
}

export const sharedEvidenceLibraryInfiniteListController =
    new EvidenceLibraryInfiniteListController();
