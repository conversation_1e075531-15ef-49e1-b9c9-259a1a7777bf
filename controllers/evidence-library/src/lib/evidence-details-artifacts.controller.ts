import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { evidenceLibraryControllerGetArtifactsByEvidenceOptions } from '@globals/api-sdk/queries';
import type { LibraryDocumentVersionResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { buildEvidenceDetailsArtifactsFilters } from '../helpers/build-evidence-details-artifacts-filters.helper';
import { buildEvidenceDetailsArtifactsSorting } from '../helpers/build-evidence-details-artifacts-sorting.helper';
import type { EvidenceDetailsArtifactsQuery } from '../types/evidence-details-artifacts.types';
import { sharedEvidenceDetailsController } from './evidence-details.controller';

interface LoadArtifactsParams {
    isCurrent?: boolean;
}

class EvidenceDetailsArtifactsController {
    evidenceDetailsArtifactsQuery = new ObservedQuery(
        evidenceLibraryControllerGetArtifactsByEvidenceOptions,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isLoading(): boolean {
        return this.evidenceDetailsArtifactsQuery.isLoading;
    }

    get evidenceDetailsArtifacts(): LibraryDocumentVersionResponseDto[] {
        return this.evidenceDetailsArtifactsQuery.data?.data ?? [];
    }

    get total(): number {
        return this.evidenceDetailsArtifactsQuery.data?.total ?? 0;
    }

    loadArtifacts = (
        evidenceId: number,
        { isCurrent = false }: LoadArtifactsParams,
    ): void => {
        when(
            () => sharedWorkspacesController.isLoaded,
            () => {
                const { currentWorkspace } = sharedWorkspacesController;

                if (!currentWorkspace) {
                    throw new Error('No workspace  found.');
                }

                this.evidenceDetailsArtifactsQuery.load({
                    path: {
                        id: evidenceId,
                        xProductId: currentWorkspace.id,
                    },
                    query: {
                        isCurrent,
                        limit: DEFAULT_PAGE_SIZE,
                    },
                });
            },
        );
    };

    loadArtifactsPage = (
        params: FetchDataResponseParams,
        { isCurrent = false }: LoadArtifactsParams,
    ): void => {
        const { currentWorkspace } = sharedWorkspacesController;
        const { evidenceDetailsData } = sharedEvidenceDetailsController;

        if (!currentWorkspace || !evidenceDetailsData) {
            return;
        }

        const { pagination, globalFilter, sorting } = params;
        const { page, pageSize } = pagination;
        const { search, filters } = globalFilter;

        const query: EvidenceDetailsArtifactsQuery = {
            page,
            limit: pageSize,
        };

        if (search) {
            query.q = search;
        }

        buildEvidenceDetailsArtifactsFilters(query, filters);
        buildEvidenceDetailsArtifactsSorting(query, sorting);

        this.evidenceDetailsArtifactsQuery.load({
            path: {
                id: evidenceDetailsData.id,
                xProductId: currentWorkspace.id,
            },
            query: {
                ...query,
                isCurrent,
            },
        });
    };

    invalidateArtifacts = () => {
        this.evidenceDetailsArtifactsQuery.invalidate();
    };
}

export const sharedEvidenceDetailsArtifactsController =
    new EvidenceDetailsArtifactsController();
