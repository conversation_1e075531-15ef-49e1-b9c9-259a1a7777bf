import {
    auditorsClientsControllerAccountActAsReadOnlyMutation,
    authControllerMagicLinkActAsMutation,
    authControllerMagicLinkMutation,
} from '@globals/api-sdk/queries';
import type { AuthOptions, Regions } from '@globals/config';
import {
    autorun,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import { AUTHENTICATOR_STORAGE_KEY } from './constants';
import type { Authenticator, AuthenticatorState } from './types/auth.types';
import type { AuthModes } from './types/auth-modes.type';
import type { AuthType } from './types/auth-type.type';
import type { Company } from './types/company.type';

export class AuditorActAsAuthenticator implements Authenticator {
    email: string | null = null;
    accessToken: string | null = null;
    authMode: AuthModes | null = null;
    companies: Company[] = [];
    region: Regions | null = null;
    authType: AuthType | null = null;
    authOptions: AuthOptions | null = null;
    initialRedirectionUrl: string | null = null;
    redirectionRoutes: Record<string, string> = {};
    loginNextStepUrl: string | null = null;
    accountTypeQuery = null;
    authTypeMutation = null;
    generateMagicLinkMutation = null;
    loginMagicLinkMutation = new ObservedMutation(
        authControllerMagicLinkMutation,
    );
    loginMagicLinkActAsMutation = new ObservedMutation(
        authControllerMagicLinkActAsMutation,
    );
    auditorClientActAsMutation = new ObservedMutation(
        auditorsClientsControllerAccountActAsReadOnlyMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get hasAttemptedLoginError(): boolean {
        return (
            Boolean(this.auditorClientActAsMutation.error) ||
            Boolean(this.loginMagicLinkActAsMutation.error)
        );
    }

    get hasAttemptedLogin(): boolean {
        return Boolean(this.accessToken);
    }

    get isAttemptingLogin(): boolean {
        return (
            this.auditorClientActAsMutation.isPending ||
            this.loginMagicLinkActAsMutation.isPending
        );
    }

    get isAcceptTermsPending(): boolean {
        return false;
    }

    get hasAcceptTermsError(): boolean {
        return false;
    }

    get token(): string | null {
        return this.accessToken;
    }

    initFromPersistedState = (state: Partial<AuthenticatorState>): this => {
        const {
            email = null,
            accessToken = null,
            authMode = null,
            companies = [],
            authType = null,
            region = null,
            authOptions = null,
        } = state;

        this.email = email;
        this.accessToken = accessToken;
        this.authMode = authMode;
        this.companies = companies;
        this.authType = authType;
        this.region = region;
        this.authOptions = authOptions;

        return this;
    };

    logout(): void {
        this.email = null;
        this.accessToken = null;
        this.authMode = null;
        this.companies = [];
        this.authType = null;
        this.region = null;
        this.redirectionRoutes = {};
        window.sessionStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
        window.localStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
    }

    getPersistableState(): AuthenticatorState {
        return {
            email: this.email,
            accessToken: this.accessToken,
            authMode: this.authMode,
            companies: this.companies,
            authType: this.authType,
            region: this.region,
            initialRedirectionUrl: this.initialRedirectionUrl,
            authOptions: this.authOptions,
        };
    }

    persistState(state: AuthenticatorState | null): void {
        window.sessionStorage.setItem(
            AUTHENTICATOR_STORAGE_KEY,
            JSON.stringify(state),
        );
    }

    setEmail(email: string): void {
        this.email = email;
    }

    setRegion(region: Regions): void {
        this.region = region;
    }

    setAccessToken(accessToken: string | null): void {
        this.accessToken = accessToken;
    }

    setAuthMode(authMode: AuthModes): this {
        if (this.authMode !== 'ACT_AS_READ_ONLY') {
            console.error(
                'Trying to switch auth mode when not in NORMAL mode',
                this.authMode,
                authMode,
            );
            throw new Error(
                'Trying to switch auth mode when not in NORMAL mode',
            );
        }

        this.authMode = authMode;

        return this;
    }

    setAuthType(authType: AuthType): void {
        this.authType = authType;
    }

    attemptLogin(_region: Regions, options: AuthOptions = {}): void {
        const { accountId } = options;

        this.auditorClientActAsMutation.mutate({
            path: { id: accountId as string },
        });

        when(
            () =>
                !this.auditorClientActAsMutation.isPending &&
                Boolean(
                    this.auditorClientActAsMutation.mutation?.state?.data
                        ?.token,
                ),
            () => {
                if (
                    this.auditorClientActAsMutation.mutation?.state?.data?.token
                ) {
                    console.debug(
                        'Got the magic link token for the auditor client act as',
                    );
                    this.finalizeLogin(
                        this.auditorClientActAsMutation.mutation.state.data
                            .token,
                    );

                    return;
                }
                console.error('Failed to login with the auditor client act as');
            },
        );
    }

    finalizeLogin(code?: string): void {
        if (!code) {
            console.error('Login cannot be finalized without a code');

            return;
        }

        if (this.authType === 'MAGIC_LINK') {
            this.magicLinkLogin(code);

            return;
        }

        this.oAuthLogin(code);
    }

    resendMagicLinkEmail(): void {
        console.error('You cannot resend magic link as an act as user');
    }

    magicLinkLogin(magicLinkToken: string): void {
        console.debug('Before making magic link request');

        this.loginMagicLinkActAsMutation.mutate({
            path: { token: magicLinkToken },
            credentials: 'include',
        });

        when(
            () =>
                !this.loginMagicLinkActAsMutation.isPending &&
                Boolean(
                    this.loginMagicLinkActAsMutation.mutation?.state?.data
                        ?.accessToken,
                ),
            () => {
                if (
                    this.loginMagicLinkActAsMutation.mutation?.state?.data
                        ?.accessToken
                ) {
                    autorun(() => {
                        if (this.accessToken) {
                            this.persistState(this.getPersistableState());
                        }
                    });
                    this.setAccessToken(
                        this.loginMagicLinkActAsMutation.mutation.state.data
                            .accessToken,
                    );

                    return;
                }
                console.error('Failed to login with magic link act as');
            },
        );
    }

    acceptTermsAndConditions(): void {
        console.error('Not implemented for Auditor Act As');
    }

    oAuthLogin(code: string): void {
        console.debug(`Code for OAuth login: ${code}`);
        throw new Error('Missing implemetation of the oAuthLogin method');
    }
}
