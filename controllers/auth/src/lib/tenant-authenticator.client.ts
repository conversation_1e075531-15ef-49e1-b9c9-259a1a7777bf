import { authControllerMagicLinkMutation } from '@globals/api-sdk/queries';
import type { Regions } from '@globals/config';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { AUTHENTICATOR_STORAGE_KEY } from './constants';
import type { Authenticator, AuthenticatorState } from './types/auth.types';
import type { AuthModes } from './types/auth-modes.type';
import type { AuthType } from './types/auth-type.type';
import type { Company } from './types/company.type';

export class TenantAuthenticator implements Authenticator {
    email: string | null = null;
    accessToken: string | null = null;
    authMode: AuthModes | null = null;
    companies: Company[] = [];
    region: Regions | null = null;
    authType: AuthType | null = null;
    initialRedirectionUrl: string | null = null;
    redirectionRoutes: Record<string, string> = {};
    loginNextStepUrl: string | null = null;
    accountTypeQuery = null;
    authTypeMutation = null;
    generateMagicLinkMutation = null;
    loginMagicLinkMutation = new ObservedMutation(
        authControllerMagicLinkMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get hasAttemptedLogin(): boolean {
        return true;
    }

    get hasAttemptedLoginError(): boolean {
        return false;
    }

    get isAttemptingLogin(): boolean {
        return false;
    }

    get isAcceptTermsPending(): boolean {
        return false;
    }

    get hasAcceptTermsError(): boolean {
        return false;
    }

    get token(): string | null {
        return this.accessToken;
    }

    initFromPersistedState = (state: Partial<AuthenticatorState>): this => {
        const {
            email = null,
            accessToken = null,
            authMode = null,
            companies = [],
            authType = null,
            region = null,
        } = state;

        this.email = email;
        this.accessToken = accessToken;
        this.authMode = authMode;
        this.companies = companies;
        this.authType = authType;
        this.region = region;

        return this;
    };

    logout(): void {
        this.email = null;
        this.accessToken = null;
        this.authMode = null;
        this.companies = [];
        this.authType = null;
        this.region = null;
        this.redirectionRoutes = {};
        window.sessionStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
        window.localStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
    }

    getPersistableState(): AuthenticatorState {
        return {
            email: this.email,
            accessToken: this.accessToken,
            authMode: this.authMode,
            companies: this.companies,
            authType: this.authType,
            region: this.region,
            initialRedirectionUrl: this.initialRedirectionUrl,
        };
    }

    persistState(state: AuthenticatorState | null): void {
        window.localStorage.setItem(
            AUTHENTICATOR_STORAGE_KEY,
            JSON.stringify(state),
        );
    }

    setEmail(email: string): void {
        this.email = email;
    }

    setRegion(region: Regions): void {
        this.region = region;
    }

    setAccessToken(accessToken: string | null): void {
        this.accessToken = accessToken;
    }

    setAuthMode(authMode: AuthModes): this {
        if (this.authMode !== 'NORMAL') {
            console.error(
                'Trying to switch auth mode when not in NORMAL mode',
                this.authMode,
                authMode,
            );
            throw new Error(
                'Trying to switch auth mode when not in NORMAL mode',
            );
        }

        this.authMode = authMode;

        return this;
    }

    setAuthType(authType: AuthType): void {
        this.authType = authType;
    }

    attemptLogin(): void {
        console.error('You cannot attempt login with the tenant authenticator');
    }

    finalizeLogin(code?: string): void {
        if (!code) {
            console.error('Login cannot be finalized without a code');

            return;
        }

        if (this.authType === 'MAGIC_LINK') {
            this.magicLinkLogin(code);

            return;
        }

        this.oAuthLogin(code);
    }

    resendMagicLinkEmail(): void {
        console.error('You cannot resend magic link as a tenant user yet');
    }

    /**
     * See if we need to have a different approach for asynchronous actions.
     */
    magicLinkLogin(magicLinkToken: string): void {
        console.debug('Before making magic link request');

        this.loginMagicLinkMutation
            .mutateAsync({
                path: { token: magicLinkToken },
                credentials: 'include',
            })
            .catch((error) => {
                console.error({ error });
            });

        when(
            () =>
                !this.loginMagicLinkMutation.isPending &&
                Boolean(
                    this.loginMagicLinkMutation.mutation?.state?.data
                        ?.accessToken,
                ),
            () => {
                if (
                    !this.loginMagicLinkMutation.mutation?.state?.data
                        ?.accessToken
                ) {
                    console.error('Failed to login with magic link');

                    return;
                }

                this.setAccessToken(
                    this.loginMagicLinkMutation.mutation.state.data.accessToken,
                );
            },
        );
    }

    acceptTermsAndConditions(): void {
        console.error(
            'You cannot accept terms and conditions as a tenant user yet',
        );
    }

    oAuthLogin(code: string): void {
        console.debug(`Code for OAuth login: ${code}`);
        throw new Error('Missing implemetation of the oAuthLogin method');
    }
}
