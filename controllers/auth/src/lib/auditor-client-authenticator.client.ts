import {
    auditorsClientsControllerAuditorTenantAuthMutation,
    authControllerMagicLinkMutation,
} from '@globals/api-sdk/queries';
import type { AuthOptions, Regions } from '@globals/config';
import {
    autorun,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import { AUTHENTICATOR_STORAGE_KEY } from './constants';
import type { Authenticator, AuthenticatorState } from './types/auth.types';
import type { AuthModes } from './types/auth-modes.type';
import type { AuthType } from './types/auth-type.type';
import type { Company } from './types/company.type';

export class AuditorClientAuthenticator implements Authenticator {
    email: string | null = null;
    accessToken: string | null = null;
    authMode: AuthModes | null = null;
    companies: Company[] = [];
    region: Regions | null = null;
    authType: AuthType | null = null;
    initialRedirectionUrl: string | null = null;
    redirectionRoutes: Record<string, string> = {};
    loginNextStepUrl: string | null = null;
    accountTypeQuery = null;
    authTypeMutation = null;
    generateMagicLinkMutation = null;
    loginMagicLinkMutation = new ObservedMutation(
        authControllerMagicLinkMutation,
    );
    auditorClientTenantAuthMutation = new ObservedMutation(
        auditorsClientsControllerAuditorTenantAuthMutation,
    );

    constructor() {
        console.info('Initializing auditor client authenticator');
        makeAutoObservable(this);
    }

    get hasAttemptedLoginError(): boolean {
        return Boolean(this.auditorClientTenantAuthMutation.error);
    }

    get hasAttemptedLogin(): boolean {
        return Boolean(this.accessToken);
    }

    get isAttemptingLogin(): boolean {
        return this.auditorClientTenantAuthMutation.isPending;
    }

    get isAcceptTermsPending(): boolean {
        return false;
    }

    get hasAcceptTermsError(): boolean {
        return false;
    }

    get token(): string | null {
        return this.accessToken;
    }

    initFromPersistedState = (state: Partial<AuthenticatorState>): this => {
        const {
            email = null,
            accessToken = null,
            authMode = null,
            companies = [],
            authType = null,
            region = null,
        } = state;

        this.email = email;
        this.accessToken = accessToken;
        this.authMode = authMode;
        this.companies = companies;
        this.authType = authType;
        this.region = region;

        return this;
    };

    logout(): void {
        this.email = null;
        this.accessToken = null;
        this.authMode = null;
        this.companies = [];
        this.authType = null;
        this.region = null;
        this.redirectionRoutes = {};
        window.sessionStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
        window.localStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
    }

    getPersistableState(): AuthenticatorState {
        return {
            email: this.email,
            accessToken: this.accessToken,
            authMode: this.authMode,
            companies: this.companies,
            authType: this.authType,
            region: this.region,
            initialRedirectionUrl: this.initialRedirectionUrl,
        };
    }

    persistState(state: AuthenticatorState | null): void {
        window.sessionStorage.setItem(
            AUTHENTICATOR_STORAGE_KEY,
            JSON.stringify(state),
        );
    }

    setEmail(email: string): void {
        this.email = email;
    }

    setRegion(region: Regions): void {
        this.region = region;
    }

    setAccessToken(accessToken: string | null): void {
        this.accessToken = accessToken;
    }

    setAuthMode(authMode: AuthModes): this {
        if (this.authMode !== 'NORMAL') {
            console.error(
                'Trying to switch auth mode when not in NORMAL mode',
                this.authMode,
                authMode,
            );
            throw new Error(
                'Trying to switch auth mode when not in NORMAL mode',
            );
        }

        this.authMode = authMode;

        return this;
    }

    setAuthType(authType: AuthType): void {
        this.authType = authType;
    }

    attemptLogin(_region: Regions, { clientId }: AuthOptions = {}): void {
        this.auditorClientTenantAuthMutation.mutate({
            path: { clientId: clientId as string },
        });

        when(
            () =>
                !this.auditorClientTenantAuthMutation.isPending &&
                Boolean(
                    this.auditorClientTenantAuthMutation.mutation?.state?.data
                        ?.accessToken,
                ),
            () => {
                if (
                    this.auditorClientTenantAuthMutation.mutation?.state?.data
                        ?.accessToken
                ) {
                    console.debug('Logged in with the auditor client');

                    autorun(() => {
                        if (this.accessToken) {
                            this.persistState(this.getPersistableState());
                        }
                    });

                    this.setAccessToken(
                        this.auditorClientTenantAuthMutation.mutation.state.data
                            .accessToken,
                    );

                    return;
                }
                console.error('Failed to login with the auditor client');
            },
        );
    }

    finalizeLogin(): void {
        console.error('You cannot finalize login as an auditor client');
    }

    resendMagicLinkEmail(): void {
        console.error('You cannot resend magic link as a tenant user yet');
    }

    magicLinkLogin(): void {
        console.error('You cannot login with magic link as an auditor client');
    }

    acceptTermsAndConditions(): void {
        console.error('Not implemented for Auditor Client');
    }

    oAuthLogin(code: string): void {
        console.debug(`Code for OAuth login: ${code}`);
        throw new Error('Missing implemetation of the oAuthLogin method');
    }
}
