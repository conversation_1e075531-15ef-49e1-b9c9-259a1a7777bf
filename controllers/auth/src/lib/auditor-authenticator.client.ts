import { isEmpty } from 'lodash-es';
import { snackbar<PERSON>ontroller } from '@controllers/snackbar';
import {
    auditorsControllerAgreeToTermsMutation,
    authControllerGenerateMagicLinkMutation,
    authControllerGetAccountTypesForEmailOptions,
    authControllerMagicLinkMutation,
} from '@globals/api-sdk/queries';
import type { AuthOptions, Regions } from '@globals/config';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
    when,
} from '@globals/mobx';
import { AuditorActAsAuthenticator } from './auditor-act-as-authenticator.client';
import { AuditorClientAuthenticator } from './auditor-client-authenticator.client';
import { AUTHENTICATOR_STORAGE_KEY } from './constants';
import type { Authenticator, AuthenticatorState } from './types/auth.types';
import type { AuthModes } from './types/auth-modes.type';
import type { AuthType } from './types/auth-type.type';
import type { Company } from './types/company.type';

export class AuditorAuthenticator implements Authenticator {
    email: string | null = null;
    accessToken: string | null = null;
    authMode: AuthModes | null = null;
    companies: Company[] = [];
    region: Regions | null = null;
    authType: AuthType | null = null;
    initialRedirectionUrl: string | null = null;
    redirectionRoutes: Record<string, string> = {};
    loginNextStepUrl: string | null = null;
    accountTypeQuery = new ObservedQuery(
        authControllerGetAccountTypesForEmailOptions,
    );
    authTypeMutation = null;
    generateMagicLinkMutation = new ObservedMutation(
        authControllerGenerateMagicLinkMutation,
    );
    loginMagicLinkMutation = new ObservedMutation(
        authControllerMagicLinkMutation,
    );
    agreeToTermsMutation = new ObservedMutation(
        auditorsControllerAgreeToTermsMutation,
    );
    /**
     * The idea is to use internally authenticators that are used to keep track of the authentication
     * used for a specific tenant and/or for when the auditor uses the act as feature.
     * Both sessions can be active.
     */
    auditorClientAuthenticator: Authenticator | null = null;
    actAsAuthenticator: Authenticator | null = null;

    constructor() {
        this.initializeSecondaryAuthenticatorFromPersistedState();

        makeAutoObservable(this);
    }

    get hasAttemptedLoginError(): boolean {
        if (this.actAsAuthenticator) {
            return this.actAsAuthenticator.hasAttemptedLoginError;
        }

        if (this.auditorClientAuthenticator) {
            return Boolean(
                this.auditorClientAuthenticator.hasAttemptedLoginError,
            );
        }

        // login auditor page
        return Boolean(this.accountTypeQuery.error);
    }

    get hasAttemptedLogin(): boolean {
        if (this.actAsAuthenticator) {
            return this.actAsAuthenticator.hasAttemptedLogin;
        }

        return this.auditorClientAuthenticator
            ? Boolean(this.auditorClientAuthenticator.hasAttemptedLogin)
            : Boolean(this.accountTypeQuery.data);
    }

    get isAttemptingLogin(): boolean {
        if (this.actAsAuthenticator) {
            return this.actAsAuthenticator.isAttemptingLogin;
        }

        if (this.auditorClientAuthenticator) {
            return this.auditorClientAuthenticator.isAttemptingLogin;
        }

        // login auditor page
        return this.accountTypeQuery.isLoading;
    }

    get token(): string | null {
        return (
            this.actAsAuthenticator?.accessToken ||
            this.auditorClientAuthenticator?.accessToken ||
            this.accessToken
        );
    }

    get isAcceptTermsPending(): boolean {
        return this.agreeToTermsMutation.isPending;
    }

    get hasAcceptTermsError(): boolean {
        return Boolean(this.agreeToTermsMutation.hasError);
    }

    initFromPersistedState = (state: Partial<AuthenticatorState>): this => {
        const {
            email = null,
            accessToken = null,
            authMode = null,
            companies = [],
            authType = null,
            region = null,
        } = state;

        this.email = email;
        this.accessToken = accessToken;
        this.authMode = authMode;
        this.companies = companies;
        this.authType = authType;
        this.region = region;
        //TODO: It's missing to retrieve more User info

        return this;
    };

    logout(): void {
        this.email = null;
        this.accessToken = null;
        this.authMode = null;
        this.companies = [];
        this.authType = null;
        this.region = null;
        this.redirectionRoutes = {};
        window.sessionStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
        window.localStorage.removeItem(AUTHENTICATOR_STORAGE_KEY);
    }

    getPersistableState(): AuthenticatorState {
        if (this.actAsAuthenticator) {
            return this.actAsAuthenticator.getPersistableState();
        }

        if (this.auditorClientAuthenticator) {
            return this.auditorClientAuthenticator.getPersistableState();
        }

        return {
            email: this.email,
            accessToken: this.accessToken,
            authMode: this.authMode,
            companies: this.companies,
            authType: this.authType,
            region: this.region,
            initialRedirectionUrl: this.initialRedirectionUrl,
        };
    }

    persistState(state: AuthenticatorState | null): void {
        /**
         * If there is a tenant authenticator we are refreshing the token for such tenant so
         * we just return here. See the code below for when the tenant token needs to be updated.
         */
        if (this.actAsAuthenticator) {
            this.actAsAuthenticator.persistState(state);

            return;
        }

        if (this.auditorClientAuthenticator) {
            return;
        }

        window.localStorage.setItem(
            AUTHENTICATOR_STORAGE_KEY,
            JSON.stringify(state),
        );
    }

    initializeSecondaryAuthenticatorFromPersistedState(): void {
        const persistedStringifiedState = window.sessionStorage.getItem(
            AUTHENTICATOR_STORAGE_KEY,
        );

        if (
            persistedStringifiedState?.includes('AUDITOR_READ_ONLY') &&
            !this.actAsAuthenticator
        ) {
            const parsedState: Partial<AuthenticatorState> = JSON.parse(
                persistedStringifiedState,
            );

            this.actAsAuthenticator = new AuditorActAsAuthenticator();
            this.actAsAuthenticator.initFromPersistedState({
                ...parsedState,
                accessToken: null,
            });
            this.actAsAuthenticator.attemptLogin(this.region as Regions, {
                accountId: parsedState.authOptions?.accountId,
            });
        }
    }

    setEmail(email: string): void {
        this.email = email;
    }

    setRegion(region: Regions): void {
        this.region = region;
    }

    setAccessToken(accessToken: string | null): void {
        //set act as token for first time
        if (this.actAsAuthenticator && !this.actAsAuthenticator.accessToken) {
            this.actAsAuthenticator.setAccessToken(accessToken); //set act as token for first time

            return;
        }

        if (
            this.auditorClientAuthenticator &&
            !this.auditorClientAuthenticator.accessToken
        ) {
            this.auditorClientAuthenticator.setAccessToken(accessToken); //update tenant token only when is not set

            return;
        }

        this.accessToken = accessToken;
    }

    setAuthMode(authMode: AuthModes): this {
        if (this.authMode !== 'AUDITOR') {
            console.error(
                'Trying to switch auth mode when not in NORMAL mode',
                this.authMode,
                authMode,
            );
            throw new Error(
                'Trying to switch auth mode when not in NORMAL mode',
            );
        }

        this.authMode = authMode;

        return this;
    }

    setAuthType(authType: AuthType): void {
        this.authType = authType;
    }

    handleAuthModeLogin(): void {
        if (this.accountTypeQuery.data?.accountTypes.length !== 1) {
            return;
        }

        const singleAccountType = this.accountTypeQuery.data.accountTypes[0];

        switch (singleAccountType) {
            case 'SERVICE_ACCOUNT': {
                this.authMode = 'SERVICE_USER';

                return;
            }

            case 'AUDITOR_ACCOUNT': {
                this.authMode = 'AUDITOR';

                return;
            }

            case 'STANDARD_ACCOUNT': {
                this.authMode = 'NORMAL';

                return;
            }

            default: {
                console.error('Unsupported account type');

                return;
            }
        }

        // this.setLoginNextStepUrlForMultipleAccountTypes();
    }

    /**
     * This method is used to resend the magic link email.
     * It is used when the user clicks on the "Resend email" button.
     */
    resendMagicLinkEmail(): void {
        if (!this.email) {
            console.error('Cannot generate magic link without an email');

            return;
        }

        this.generateMagicLinkMutation.mutate({
            body: { email: this.email, isAuditorPortal: true },
        });

        when(
            () => !this.generateMagicLinkMutation.isPending,
            () => {
                if (this.generateMagicLinkMutation.error) {
                    snackbarController.addSnackbar({
                        id: 'auditor-login-email-sent-error',
                        props: {
                            title: 'Email could not be sent. Please try again or contact support.',
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                } else {
                    this.persistState(this.getPersistableState());
                    snackbarController.addSnackbar({
                        id: 'auditor-login-email-sent-success',
                        props: {
                            title: 'Email was sent successfully.',
                            severity: 'success',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                }
            },
        );
    }

    generateMagicLink(): void {
        if (!this.email) {
            console.error('Cannot generate magic link without an email');

            return;
        }

        this.generateMagicLinkMutation.mutate({
            body: { email: this.email, isAuditorPortal: true },
        });

        when(
            () =>
                !this.generateMagicLinkMutation.isPending &&
                !this.generateMagicLinkMutation.error,
            () => {
                this.persistState(this.getPersistableState());
                // this.setDefaultNextStepLoginUrl();
            },
        );
    }

    handleAuthTypeLogin(): void {
        switch (this.authType) {
            case 'MAGIC_LINK': {
                this.authType = 'MAGIC_LINK';
                const magicLinkUrl = 'localhost:5173';

                // This is simply the web app url.
                // const magicLinkUrl = sharedConfigController.loadConfig('magicLinkUrl');
                this.generateMagicLink();
                this.loginNextStepUrl = magicLinkUrl;

                return;
            }

            case 'GOOGLE': {
                this.authType = 'GOOGLE';
                const googleUrl = 'localhost:5173';

                // For google we can get it from the config.
                // const googleUrl = sharedConfigController.loadConfig('googleOAuthUrl');
                this.loginNextStepUrl = googleUrl;

                return;
            }

            case 'MICROSOFT_365': {
                this.authType = 'MICROSOFT_365';
                const microsoftUrl = 'localhost:5173';

                // For microsoft we can get it from the config.
                // const microsoftUrl = sharedConfigController.loadConfig('microsoftOAuthUrl');
                this.loginNextStepUrl = microsoftUrl;

                return;
            }

            default: {
                // Couldn't find information about enterprise sso. Might be a legacy identity provider.
                console.error('Unsupported auth type');
                // this.setDefaultNextStepLoginUrl();

                return;
            }
        }
    }

    handleAuthenticationAttempt(): void {
        if (isEmpty(this.accountTypeQuery.data?.accountTypes)) {
            console.error('No account types returned from account type query');
            // this.setDefaultNextStepLoginUrl();

            return;
        }

        this.handleAuthModeLogin();

        this.handleAuthTypeLogin();
    }

    handleSecondaryAuditorLogin(options: AuthOptions): void {
        const { clientId, accountId } = options;

        if (clientId) {
            this.auditorClientAuthenticator = new AuditorClientAuthenticator();

            this.auditorClientAuthenticator.initFromPersistedState({
                email: this.email,
                authMode: 'NORMAL',
                authType: 'MAGIC_LINK',
                region: this.region,
                initialRedirectionUrl: this.initialRedirectionUrl,
            });
            this.auditorClientAuthenticator.attemptLogin(
                this.region as Regions,
                { clientId },
            );
        }

        if (accountId) {
            this.actAsAuthenticator = new AuditorActAsAuthenticator();

            this.actAsAuthenticator.initFromPersistedState({
                email: this.email,
                authMode: 'AUDITOR_READ_ONLY',
                authType: 'MAGIC_LINK',
                region: this.region,
                initialRedirectionUrl: this.initialRedirectionUrl,
                authOptions: options,
            });
            this.actAsAuthenticator.attemptLogin(this.region as Regions, {
                accountId,
            });
        }
    }

    attemptLogin(region: Regions, options: AuthOptions = {}): void {
        /**
         * If the user is already logged in as an auditor then we probably need to log him in into the tenant,
         * either as an auditor client or as an act as read only user.
         */
        if (this.accessToken) {
            if (!options.clientId && !options.accountId) {
                console.debug('User is already logged in');

                return;
            }

            this.handleSecondaryAuditorLogin(options);

            return;
        }

        /**
         * If the user is not logged in then we need to log him in as an auditor.
         */
        if (!this.email) {
            console.error('Cannot attempt login without an email');

            return;
        }

        this.setRegion(region);

        this.accountTypeQuery.load({
            query: { email: this.email },
        });

        when(
            () =>
                !this.accountTypeQuery.isLoading &&
                Boolean(
                    this.accountTypeQuery.data?.accountTypes ??
                        this.accountTypeQuery.error,
                ),
            () => {
                this.handleAuthenticationAttempt();
            },
        );
    }

    finalizeLogin(code?: string): void {
        if (!code) {
            console.error('Login cannot be finalized without a code');

            return;
        }

        if (this.authType === 'MAGIC_LINK') {
            this.magicLinkLogin(code);

            return;
        }

        this.oAuthLogin(code);
    }

    acceptTermsAndConditions(): void {
        this.agreeToTermsMutation.mutate();

        when(
            () => !this.agreeToTermsMutation.isPending,
            () => {
                if (this.agreeToTermsMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'auditor-accept-terms-error',
                        props: {
                            title: t`There was an error accepting the terms.`,
                            severity: 'critical',
                            closeButtonAriaLabel: 'Close',
                        },
                    });
                } else {
                    sharedCurrentUserController.setAcceptedTerms(
                        new Date().toISOString(),
                    );
                }
            },
        );
    }

    magicLinkLogin(magicLinkToken: string): void {
        this.loginMagicLinkMutation.mutate({
            path: { token: magicLinkToken },
            credentials: 'include',
        });

        when(
            () =>
                !this.loginMagicLinkMutation.isPending &&
                Boolean(
                    this.loginMagicLinkMutation.mutation?.state?.data
                        ?.accessToken,
                ),
            () => {
                if (
                    !this.loginMagicLinkMutation.mutation?.state?.data
                        ?.accessToken
                ) {
                    console.error('Failed to login with magic link');

                    return;
                }

                this.setAccessToken(
                    this.loginMagicLinkMutation.mutation.state.data.accessToken,
                );
            },
        );
    }

    oAuthLogin(code: string): void {
        console.debug(`Code for OAuth login: ${code}`);
        throw new Error('Missing implemetation of the oAuthLogin method');
    }
}
