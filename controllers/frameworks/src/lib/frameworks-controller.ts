import { isNil } from 'lodash-es';
import {
    grcControllerGetUnmappedFrameworksOptions,
    productsControllerGetProductFrameworksSimpleOptions,
} from '@globals/api-sdk/queries';
import type { FrameworkResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class FrameworksController {
    constructor() {
        makeAutoObservable(this);
    }

    allProductFrameworks = new ObservedQuery(
        productsControllerGetProductFrameworksSimpleOptions,
    );

    loadPage = (page: number, limit?: number): void => {
        when(
            () => !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                if (sharedWorkspacesController.currentWorkspace) {
                    this.allProductFrameworks.load({
                        query: { page, limit },
                        path: {
                            id: sharedWorkspacesController.currentWorkspace.id,
                        },
                    });
                }
            },
        );
    };

    loadFrameworks = (): void => {
        when(
            () => !isNil(sharedWorkspacesController.currentWorkspace),
            () => {
                if (sharedWorkspacesController.currentWorkspace) {
                    this.allProductFrameworks.load({
                        path: {
                            id: sharedWorkspacesController.currentWorkspace.id,
                        },
                    });
                }
            },
        );
    };

    isFrameworkEnabled(tag: FrameworkResponseDto['tag']): boolean {
        return (
            sharedWorkspacesController.currentWorkspace?.frameworks ?? []
        ).some(
            (framework) => framework.tag === tag && !isNil(framework.enabledAt),
        );
    }

    get productFrameworks(): FrameworkResponseDto[] {
        return this.allProductFrameworks.data?.data ?? [];
    }

    get isLoading(): boolean {
        return this.allProductFrameworks.isLoading;
    }

    get frameworksTotal(): number {
        return this.allProductFrameworks.data?.total ?? 0;
    }

    getUnmappedFrameworksQuery = new ObservedQuery(
        grcControllerGetUnmappedFrameworksOptions,
    );

    get unmappedFrameworksCount(): number {
        return (
            this.getUnmappedFrameworksQuery.data?.unmappedFrameworks.length ?? 0
        );
    }
}

export const sharedFrameworksController = new FrameworksController();
