import type { PolicyOverviewResponse } from '@globals/api-sdk/types';
import type {
    policyApprovalStatus,
    policyRenewal,
    PolicyStatuses,
} from './types/policies-controller.types';

export const STATUS_OVERVIEW_QUERY_PARAMS = {
    renewalUpcoming: 'EXPIRE_SOON',
    renewalPastDue: 'EXPIRED',
    approvalNeedsApproval: 'NEEDS_APPROVAL',
    approvalReadyToPublish: 'APPROVED',
} as const satisfies Record<
    keyof Omit<PolicyOverviewResponse, 'published' | 'activePolicies'>,
    policyApprovalStatus | policyRenewal
>;

export const DEFAULT_POLICY_STATUSES = [
    'ACTIVE',
    'UNACCEPTABLE',
    'OUTDATED',
] as const satisfies PolicyStatuses;

export const ARCHIVED_POLICY_STATUSES = [
    'ARCHIVED',
    'REPLACED',
] as const satisfies PolicyStatuses;

export const POLICY_RENEWAL: policyRenewal[] = ['EXPIRE_SOON', 'EXPIRED'];

export const POLICY_APPROVAL_STATUS: policyApprovalStatus[] = [
    'NEEDS_APPROVAL',
    'APPROVED',
    'PUBLISHED',
    'DISCARDED',
    'DRAFT',
];
