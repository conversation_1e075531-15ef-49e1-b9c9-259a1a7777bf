import { isEmpty } from 'lodash-es';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { policyApprovalStatus } from '../types/policies-controller.types';

/**
 * Extracts the policy approval status from the datatable filters.
 *
 * @param filters - The filters object from the datatable.
 * @returns The extracted policy approval status or undefined if not present.
 */
export function extractApprovalStatusFromFilters(
    filters: FetchDataResponseParams['globalFilter']['filters'],
): policyApprovalStatus | undefined {
    if (isEmpty(filters.policyApprovalStatus.value)) {
        return undefined;
    }

    return filters.policyApprovalStatus.value as policyApprovalStatus;
}
