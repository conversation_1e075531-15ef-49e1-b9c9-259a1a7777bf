import { head, isEmpty, isNil, isObject } from 'lodash-es';
import {
    type ConnectionProps,
    sharedConnectionsController,
} from '@controllers/connections';
import type { FetchDataResponseParams } from '@cosmos/components/datatable';
import type { ListBoxItems } from '@cosmos/components/list-box';
import {
    groupControllerGetGroupsOptions,
    policiesControllerDownloadAllPoliciesOptions,
    policiesControllerGetPoliciesCountByApprovalStatusOptions,
    policiesControllerGetPolicyListOptions,
} from '@globals/api-sdk/queries';
import type {
    GroupResponseDto,
    PolicyOverviewResponse,
    PolicyTableResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import { extractApprovalStatusFromFilters } from './helpers/extract-approval-status-from-filters.helper';
import { extractRenewalStatusFromFilters } from './helpers/extract-renewal-status-from-filters.helper';
import { extractUserIdFromFilters } from './helpers/extract-user-id-from-filters.helper.js';
import { formatOverviewFilters } from './helpers/policies-controller.helper';
import {
    ARCHIVED_POLICY_STATUSES,
    DEFAULT_POLICY_STATUSES,
} from './policies.controller.constants';
import type {
    policyApprovalStatus,
    policyOverviewMetrics,
    policyRenewal,
} from './types/policies-controller.types';

export class PoliciesStore {
    // ===== OBSERVABLE STATE =====
    /**
     * Core data queries - these are the source of truth.
     */
    activePolicies = new ObservedQuery(policiesControllerGetPolicyListOptions);
    archivedPolicies = new ObservedQuery(
        policiesControllerGetPolicyListOptions,
    );
    overview = new ObservedQuery(
        policiesControllerGetPoliciesCountByApprovalStatusOptions,
    );
    groups = new ObservedQuery(groupControllerGetGroupsOptions);
    downloadAllPolicies = new ObservedQuery(
        policiesControllerDownloadAllPoliciesOptions,
    );

    /**
     * UI state - tracks current user interactions.
     */
    currentParams: FetchDataResponseParams | null = null;
    overviewFilter: policyApprovalStatus | policyRenewal | undefined =
        undefined;
    isDownloadingAllPolicies = false;

    constructor() {
        makeAutoObservable(this);
    }

    // ===== COMPUTED VALUES =====
    // These are derived from observable state and automatically update

    /**
     * Active Policies - main data for the active policies table.
     */
    get activePoliciesList(): PolicyTableResponseDto[] {
        return this.activePolicies.data?.data ?? [];
    }

    get activePoliciesTotal(): number {
        return this.activePolicies.data?.total ?? 0;
    }

    get isActivePoliciesLoading(): boolean {
        return this.activePolicies.isLoading;
    }

    get hasActivePoliciesError(): boolean {
        return this.activePolicies.hasError;
    }

    get activePoliciesError(): Error | null {
        return this.activePolicies.error;
    }

    /**
     * Archived Policies - data for the archived policies table.
     */
    get archivedPoliciesList(): PolicyTableResponseDto[] {
        return this.archivedPolicies.data?.data ?? [];
    }

    get archivedPoliciesTotal(): number {
        return this.archivedPolicies.data?.total ?? 0;
    }

    get isArchivedPoliciesLoading(): boolean {
        return this.archivedPolicies.isLoading;
    }

    /**
     * Overview - dashboard metrics and statistics.
     */
    get overviewData(): PolicyOverviewResponse | null {
        return this.overview.data;
    }

    get hasPublishedPolicies(): boolean {
        return (
            !isNil(this.overviewData?.published) &&
            this.overviewData.published > 0
        );
    }

    get isOverviewLoading(): boolean {
        return this.overview.isLoading;
    }

    /**
     * Connections - derived from external connections store.
     */
    get hasExternalPolicyConnection(): boolean {
        return !isEmpty(
            sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                'EXTERNAL_POLICY',
            ),
        );
    }

    get externalPolicyConnection(): ConnectionProps | null {
        return (
            head(
                sharedConnectionsController.loadConfiguredConnectionsByProviderType(
                    'EXTERNAL_POLICY',
                ),
            ) ?? null
        );
    }

    get hasBambooHrConnection(): boolean {
        return this.externalPolicyConnection?.clientType === 'BAMBOO_HR';
    }

    /**
     * Groups - user groups for policy assignment.
     */
    get groupsList(): ListBoxItems {
        return (this.groups.data?.data ?? []).map(
            (value: GroupResponseDto): ListBoxItems[0] => ({
                id: String(value.id),
                label: value.name,
                value: String(value.id),
            }),
        );
    }

    get isGroupsLoading(): boolean {
        return this.groups.isLoading;
    }

    // ===== ACTIONS =====
    // These are the only methods that can modify observable state

    loadActivePolicies = (params: FetchDataResponseParams): void => {
        this.currentParams = params;
        const { globalFilter, pagination, sorting } = params;
        const userId = extractUserIdFromFilters(globalFilter.filters);
        const policyApprovalStatus = extractApprovalStatusFromFilters(
            globalFilter.filters,
        );
        const policyRenewal = extractRenewalStatusFromFilters(
            globalFilter.filters,
        );

        // Map column IDs to API sort values
        const sortMapping: Record<
            string,
            | 'NAME'
            | 'SLA'
            | 'APPROVED_DATE'
            | 'USER'
            | 'CREATED'
            | 'HTML_LAST_UPDATED'
            | 'RENEWAL_DATE'
            | 'PUBLISHED_DATE'
        > = {
            NAME: 'NAME',
            CREATED: 'CREATED',
            APPROVED_DATE: 'APPROVED_DATE',
            PUBLISHED_DATE: 'PUBLISHED_DATE',
            RENEWAL_DATE: 'RENEWAL_DATE',
            SLA: 'SLA',
            USER: 'USER',
            HTML_LAST_UPDATED: 'HTML_LAST_UPDATED',
        };

        const sortField = sorting[0]?.id
            ? sortMapping[sorting[0].id]
            : undefined;

        let sortDirection: 'ASC' | 'DESC' | undefined;

        if (sortField) {
            sortDirection = sorting[0]?.desc ? 'DESC' : 'ASC';
        }

        const queryParams = {
            page: pagination.pageIndex + 1,
            limit: pagination.pageSize,
            q: globalFilter.search,
            sort: sortField,
            sortDir: sortDirection,
            userId,
            policyStatuses: DEFAULT_POLICY_STATUSES,
            policyApprovalStatus,
            policyRenewal,
            ...formatOverviewFilters('TABLE_FILTER', this.overviewFilter),
        };

        this.activePolicies.load({ query: queryParams });
    };

    loadArchivedPolicies = (params: FetchDataResponseParams): void => {
        this.currentParams = params;
        const { globalFilter, pagination, sorting } = params;
        const userId = extractUserIdFromFilters(globalFilter.filters);
        const policyApprovalStatus = extractApprovalStatusFromFilters(
            globalFilter.filters,
        );
        const policyRenewal = extractRenewalStatusFromFilters(
            globalFilter.filters,
        );

        // Map column IDs to API sort values
        const sortMapping: Record<
            string,
            | 'NAME'
            | 'SLA'
            | 'APPROVED_DATE'
            | 'USER'
            | 'CREATED'
            | 'HTML_LAST_UPDATED'
            | 'RENEWAL_DATE'
            | 'PUBLISHED_DATE'
        > = {
            NAME: 'NAME',
            CREATED: 'CREATED',
            APPROVED_DATE: 'APPROVED_DATE',
            PUBLISHED_DATE: 'PUBLISHED_DATE',
            RENEWAL_DATE: 'RENEWAL_DATE',
            SLA: 'SLA',
            USER: 'USER',
            HTML_LAST_UPDATED: 'HTML_LAST_UPDATED',
        };

        const sortField = sorting[0]?.id
            ? sortMapping[sorting[0].id]
            : undefined;

        let sortDirection: 'ASC' | 'DESC' | undefined;

        if (sortField) {
            sortDirection = sorting[0]?.desc ? 'DESC' : 'ASC';
        }

        const queryParams = {
            page: pagination.pageIndex + 1,
            limit: pagination.pageSize,
            q: globalFilter.search,
            sort: sortField,
            sortDir: sortDirection,
            userId,
            policyStatuses: ARCHIVED_POLICY_STATUSES,
            policyApprovalStatus,
            policyRenewal,
        };

        this.archivedPolicies.load({ query: queryParams });
    };

    loadOverview = (): void => {
        this.overview.load();
    };

    setOverviewFilter = (filter: policyOverviewMetrics): void => {
        const filterMap: Record<
            policyOverviewMetrics,
            policyApprovalStatus | policyRenewal
        > = {
            renewalUpcoming: 'EXPIRE_SOON',
            renewalPastDue: 'EXPIRED',
            approvalNeedsApproval: 'NEEDS_APPROVAL',
            approvalReadyToPublish: 'APPROVED',
        };

        this.overviewFilter = filterMap[filter];

        if (this.currentParams) {
            this.loadActivePolicies(this.currentParams);
        }
    };

    clearOverviewFilter = (): void => {
        this.overviewFilter = undefined;
    };

    downloadAllPoliciesFile = (): Promise<void> => {
        this.isDownloadingAllPolicies = true;

        this.downloadAllPolicies.load({
            query: { withAppendix: true },
        });

        // Wait for the download to complete
        return when(() => !this.downloadAllPolicies.isLoading)
            .then(() => {
                const { data } = this.downloadAllPolicies;

                if (!data) {
                    throw new Error('No data received from download API');
                }

                // Check if the response contains a signed URL
                const signedUrl =
                    isObject(data) && 'signedUrl' in data
                        ? (data as { signedUrl: string }).signedUrl
                        : undefined;

                if (signedUrl) {
                    // Use signed URL to download the file
                    downloadFileFromSignedUrl(signedUrl);
                } else {
                    throw new Error('No signed URL received from download API');
                }
            })
            .catch((error) => {
                console.error('Error downloading all policies:', error);
                throw error;
            })
            .finally(() => {
                this.isDownloadingAllPolicies = false;
            });
    };

    loadGroups(params?: Parameters<typeof this.groups.load>[0]): void {
        if (isNil(params)) {
            this.groups.load({
                query: {
                    page: 1,
                    limit: 50,
                    includeIds: [],
                },
            });
        } else {
            this.groups.load(params);
        }
    }
}

export const sharedPoliciesController = new PoliciesStore();
