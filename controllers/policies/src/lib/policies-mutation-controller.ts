import { snackbarController } from '@controllers/snackbar';
import {
    policiesControllerDeletePolicyVersionMutation,
    policiesControllerUpdatePolicyStatusMutation,
} from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedPoliciesController } from './policies.controller';

class PoliciesMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    deletePolicyVersionMutation = new ObservedMutation(
        policiesControllerDeletePolicyVersionMutation,
    );

    archivePolicyMutation = new ObservedMutation(
        policiesControllerUpdatePolicyStatusMutation,
    );

    restorePolicyMutation = new ObservedMutation(
        policiesControllerUpdatePolicyStatusMutation,
    );

    get isDeleting(): boolean {
        return this.deletePolicyVersionMutation.isPending;
    }

    get isArchiving(): boolean {
        return this.archivePolicyMutation.isPending;
    }

    get isRestoring(): boolean {
        return this.restorePolicyMutation.isPending;
    }

    deletePolicyVersion = (policyVersionId: number, onClose?: () => void) => {
        if (!policyVersionId || this.deletePolicyVersionMutation.isPending) {
            return;
        }

        this.deletePolicyVersionMutation.mutate({
            path: { id: policyVersionId },
        });

        when(
            () => !this.deletePolicyVersionMutation.isPending,
            () => {
                if (this.deletePolicyVersionMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'policy-delete-error',
                        props: {
                            title: t`Failed to delete policy`,
                            description: t`An error occurred while deleting the policy. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'policy-delete-success',
                        hasTimeout: true,
                        props: {
                            title: t`Policy deleted successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    sharedPoliciesController.activePolicies.invalidate();
                }

                onClose?.();
            },
        );
    };

    archivePolicy = (policyId: number): void => {
        this.archivePolicyMutation.mutate({
            path: { id: policyId },
            body: { policyStatus: 'ARCHIVED' },
        });

        when(
            () => !this.archivePolicyMutation.isPending,
            () => {
                if (this.archivePolicyMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'policy-archive-error',
                        props: {
                            title: t`Failed to archive policy`,
                            description: t`An error occurred while archiving the policy. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'policy-archive-success',
                        hasTimeout: true,
                        props: {
                            title: t`Policy archived successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    // Invalidate both active and archived policies to refresh the data
                    sharedPoliciesController.activePolicies.invalidate();
                    sharedPoliciesController.archivedPolicies.invalidate();
                }
            },
        );
    };

    restorePolicy = (policyId: number): void => {
        this.restorePolicyMutation.mutate({
            path: { id: policyId },
            body: { policyStatus: 'ACTIVE' },
        });

        when(
            () => !this.restorePolicyMutation.isPending,
            () => {
                if (this.restorePolicyMutation.hasError) {
                    snackbarController.addSnackbar({
                        id: 'policy-restore-error',
                        props: {
                            title: t`Failed to restore policy`,
                            description: t`An error occurred while restoring the policy. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    snackbarController.addSnackbar({
                        id: 'policy-restore-success',
                        hasTimeout: true,
                        props: {
                            title: t`Policy restored successfully`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    // Invalidate both active and archived policies to refresh the data
                    sharedPoliciesController.activePolicies.invalidate();
                    sharedPoliciesController.archivedPolicies.invalidate();
                }
            },
        );
    };
}

export const sharedPoliciesMutationController =
    new PoliciesMutationController();
