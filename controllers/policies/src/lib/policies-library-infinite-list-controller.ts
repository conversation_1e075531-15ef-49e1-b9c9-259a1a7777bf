import { isEmpty, isObject } from 'lodash-es';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { policiesControllerGetPolicyListInfiniteOptions } from '@globals/api-sdk/queries';
import type { PolicyTableResponseDto } from '@globals/api-sdk/types';
import {
    action,
    makeAutoObservable,
    ObservedInfiniteQuery,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import { getUserInitials } from '@helpers/user';
import { sharedMapPoliciesModel } from '@models/controls';
import { DEFAULT_POLICY_STATUSES } from './policies.controller.constants';

export interface PolicyItem extends ListBoxItemData {
    creationDate: string;
    policyData: PolicyTableResponseDto;
}

export const MAP_POLICIES_MODAL_ID = 'map-policies-modal';

class PoliciesLibraryInfiniteListController {
    constructor() {
        makeAutoObservable(this);
    }

    #searchTerm = '';

    policiesInfiniteQuery = new ObservedInfiniteQuery(
        policiesControllerGetPolicyListInfiniteOptions,
    );

    get hasNextPage(): boolean {
        return this.policiesInfiniteQuery.hasNextPage;
    }

    get policiesList(): PolicyTableResponseDto[] {
        return (
            this.policiesInfiniteQuery.data?.pages
                .flatMap((page) => page?.data ?? [])
                .filter(Boolean) ?? []
        );
    }

    get policiesListAsItems(): PolicyItem[] {
        return this.policiesList.map(this.mapPolicyToItem);
    }

    mapPolicyToItem = (policy: PolicyTableResponseDto): PolicyItem => {
        const publishedDateStr = this.getPublishedDateString(policy);

        let fallbackText;

        if (policy.currentOwner) {
            fallbackText = getUserInitials({
                firstName: policy.currentOwner.firstName,
                lastName: policy.currentOwner.lastName,
            });
        }

        return {
            id: String(policy.id),
            value: String(policy.id),
            label: policy.name,
            description: publishedDateStr,
            creationDate: publishedDateStr,
            policyData: policy,
            avatar: {
                fallbackText,
                imgSrc: policy.currentOwner?.avatarUrl || undefined,
                imgAlt: 'Policy',
            },
        };
    };

    getPublishedDateString = (policy: PolicyTableResponseDto): string => {
        if (!('version' in policy) || !policy.version.publishedAt) {
            return 'Pending publish';
        }

        try {
            return `Published on: ${formatDate('field', policy.version.publishedAt)}`;
        } catch (error) {
            console.error('Error formatting date:', error);

            return 'Pending publish';
        }
    };

    get hasError(): boolean {
        return this.policiesInfiniteQuery.hasError;
    }

    get isLoading(): boolean {
        return this.policiesInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.policiesInfiniteQuery.isFetching;
    }

    search(term: string): void {
        this.#searchTerm = term;
        this.load();
    }

    load(): void {
        this.policiesInfiniteQuery.load({
            query: {
                page: 1,
                limit: 20,
                q: this.#searchTerm,
                policyStatuses: DEFAULT_POLICY_STATUSES,
            },
        });
    }

    loadNextPage(): void {
        if (this.hasNextPage && !this.isFetching) {
            this.policiesInfiniteQuery.nextPage();
        }
    }

    addSelectedPolicies(policies: PolicyTableResponseDto[]) {
        if (!Array.isArray(policies)) {
            snackbarController.addSnackbar({
                id: 'map-policies-error',
                props: {
                    title: 'Invalid policies format',
                    description: 'Expected an array of policies',
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return;
        }

        const validPolicies = policies.filter((policy) => {
            if (!isObject(policy) || !('id' in policy)) {
                snackbarController.addSnackbar({
                    id: 'invalid-policy-error',
                    props: {
                        title: 'Invalid policy object',
                        description: 'Policy is missing required properties',
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                return false;
            }

            return true;
        });

        const newPolicies = validPolicies.filter(
            (item) =>
                !sharedMapPoliciesModel.selectedPolicies.some(
                    (existing) => existing.id === item.id,
                ),
        );

        sharedMapPoliciesModel.selectedPolicies = [
            ...sharedMapPoliciesModel.selectedPolicies,
            ...newPolicies,
        ];
    }

    removePolicy(id: number) {
        sharedMapPoliciesModel.selectedPolicies =
            sharedMapPoliciesModel.selectedPolicies.filter(
                (item) => item.id !== id,
            );
    }

    clearAllPolicies() {
        sharedMapPoliciesModel.selectedPolicies = [];
    }

    getPolicyUrl(policyId: number) {
        const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

        if (!workspaceId) {
            return '';
        }

        return `/workspaces/${workspaceId}/governance/policies/${policyId}/overview`;
    }

    saveSelectedPolicies(
        selectedPolicies: ListBoxItemData[] | ListBoxItemData,
    ) {
        action(() => {
            this.clearAllPolicies();

            if (
                !(Array.isArray(selectedPolicies) && isEmpty(selectedPolicies))
            ) {
                const policiesToAdd = Array.isArray(selectedPolicies)
                    ? selectedPolicies.map(
                          (item) => (item as PolicyItem).policyData,
                      )
                    : [(selectedPolicies as PolicyItem).policyData];

                this.addSelectedPolicies(policiesToAdd);
            }
        })();

        modalController.closeModal(MAP_POLICIES_MODAL_ID);
    }

    fetchOptions({
        search,
        increasePage,
    }: {
        search?: string;
        increasePage?: boolean;
    }) {
        action(() => {
            if (increasePage) {
                this.loadNextPage();
            } else {
                this.search(search || '');
            }
        })();
    }
}

export const sharedPoliciesLibraryInfiniteListController =
    new PoliciesLibraryInfiniteListController();
