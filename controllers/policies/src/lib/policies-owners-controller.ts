import { sharedUsersInfiniteController } from '@controllers/users';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { makeAutoObservable } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

class PoliciesOwnersController {
    #lastSearchQuery = '';
    #isInitialized = false;

    constructor() {
        makeAutoObservable(this);
    }

    get ownersComboboxOptions(): ListBoxItemData[] {
        return sharedUsersInfiniteController.usersList.map((user) => ({
            id: `${user.id}`,
            label: `${user.firstName} ${user.lastName}`,
            value: `${user.id}`,
            description: user.email,
            avatar: {
                imgSrc: user.avatarUrl ?? undefined,
                fallbackText: getInitials(`${user.firstName} ${user.lastName}`),
                imgAlt: getFullName(user.firstName, user.lastName),
            },
        }));
    }

    get hasError(): boolean {
        return sharedUsersInfiniteController.hasError;
    }

    get hasNextPage(): boolean {
        return sharedUsersInfiniteController.hasNextPage;
    }

    get isLoading(): boolean {
        return sharedUsersInfiniteController.isLoading;
    }

    loadNextPage = ({ search }: { search?: string }): void => {
        if (search !== this.#lastSearchQuery) {
            sharedUsersInfiniteController.usersListInfiniteQuery.unload();
            this.#lastSearchQuery = search ?? '';
            this.#isInitialized = true; // Mark as initialized when search changes
            this.loadPoliciesOwners();

            return;
        }
        sharedUsersInfiniteController.usersListInfiniteQuery.nextPage();
    };

    loadPoliciesOwners = (): void => {
        sharedUsersInfiniteController.loadUsers({
            q: this.#lastSearchQuery,
            roles: [
                'ADMIN',
                'TECHGOV',
                'WORKSPACE_ADMINISTRATOR',
                'POLICY_MANAGER',
            ],
            excludeReadOnlyUsers: true,
        });
    };

    /**
     * Initialize the users when the controller is first accessed.
     */
    initializePoliciesOwners = (): void => {
        // Only load if we haven't initialized yet
        if (this.#isInitialized) {
            return;
        }

        this.#isInitialized = true;
        this.loadPoliciesOwners();
    };
}

export const sharedPoliciesOwnersController = new PoliciesOwnersController();
