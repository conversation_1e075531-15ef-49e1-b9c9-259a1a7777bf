import {
    usersControllerListUsersOptions,
    usersControllerUpdateUserRoleMutation,
} from '@globals/api-sdk/queries';
import type {
    UserResponseDto,
    UsersControllerListUsersData,
} from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedMutation,
    ObservedQuery,
} from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';
import type { UserListBoxItemDataWithEntryId } from './types/user-list-box-item-data-with-entry-id.type';

export class UsersController {
    constructor() {
        makeAutoObservable(this);
    }

    readonly pageSize = 20;
    usersList = new ObservedQuery(usersControllerListUsersOptions);

    get listUsersData(): UserResponseDto[] {
        return this.usersList.data?.data ?? [];
    }

    get listUsersTotal(): number {
        return this.usersList.data?.total ?? 0;
    }

    get isLoading(): boolean {
        return this.usersList.isLoading;
    }

    get hasError(): boolean {
        return this.usersList.hasError;
    }

    get userList(): UserResponseDto[] {
        return this.usersList.data?.data ?? [];
    }

    loadUsers = ({
        userIds = [],
        roles = [],
    }: {
        userIds: number[];
        roles: NonNullable<UsersControllerListUsersData['query']>['roles[]'];
    }): void => {
        this.usersList.load({
            query: { 'includeUserIds[]': userIds, 'roles[]': roles },
        });
    };

    loadReviewerUsers = (params: { search?: string } = {}): void => {
        const { search } = params;

        this.usersList.load({
            query: { 'roles[]': ['ADMIN', 'REVIEWER'], q: search },
        });
    };

    loadPolicyManagementUsers = (params: { search?: string } = {}): void => {
        const { search } = params;

        this.usersList.load({
            query: {
                'roles[]': [
                    'ADMIN',
                    'TECHGOV',
                    'WORKSPACE_ADMINISTRATOR',
                    'POLICY_MANAGER',
                ],
                excludeReadOnlyUsers: true,
                q: search,
            },
        });
    };

    get users(): UserListBoxItemDataWithEntryId[] {
        return (this.usersList.data?.data ?? []).map(
            (value: UserResponseDto): UserListBoxItemDataWithEntryId => ({
                id: String(value.id),
                entryId: value.entryId,
                label: getFullName(value.firstName, value.lastName),
                value: String(value.id),
            }),
        );
    }

    get fullUsers(): UserListBoxItemDataWithEntryId[] {
        return (this.usersList.data?.data ?? []).map(
            (value: UserResponseDto): UserListBoxItemDataWithEntryId => ({
                id: String(value.id),
                entryId: value.entryId,
                label: getFullName(value.firstName, value.lastName),
                value: String(value.id),
                description: value.email,
                avatar: {
                    fallbackText: getInitials(
                        `${value.firstName} ${value.lastName}`,
                    ),
                    imgSrc: value.avatarUrl ?? undefined,
                    imgAlt: getFullName(value.firstName, value.lastName),
                },
            }),
        );
    }

    #updateUserRole = new ObservedMutation(
        usersControllerUpdateUserRoleMutation,
    );

    updateUserRole = async (params: {
        currentRole: string;
        entryId: string;
        newRole: string;
    }): Promise<void> => {
        await this.#updateUserRole.mutateAsync({
            body: {
                entryId: params.entryId,
                currentRole: params.currentRole,
                newRole: params.newRole,
            },
        });
    };

    get isUpdateUserRolePending(): boolean {
        return this.#updateUserRole.isPending;
    }
}

export const sharedUsersController = new UsersController();
