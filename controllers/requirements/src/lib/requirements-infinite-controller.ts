import { sharedFrameworksController } from '@controllers/frameworks';
import { grcControllerGetAllRequirementsInfiniteOptions } from '@globals/api-sdk/queries';
import type { RequirementListResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedInfiniteQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

class RequirementsInfiniteController {
    #lastSearchQuery = '';
    #frameworkId: number | null = null;
    #frameworkSlug: string | null = null;

    constructor() {
        makeAutoObservable(this);

        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                sharedFrameworksController.loadFrameworks();
            },
        );
    }

    requirementsInfiniteQuery = new ObservedInfiniteQuery(
        grcControllerGetAllRequirementsInfiniteOptions,
    );

    get requirementsInfiniteList(): RequirementListResponseDto[] {
        return (
            this.requirementsInfiniteQuery.data?.pages.flatMap(
                (page) => page?.data ?? [],
            ) ?? []
        );
    }

    get hasNextPage(): boolean {
        return this.requirementsInfiniteQuery.hasNextPage;
    }

    get isLoading(): boolean {
        return this.requirementsInfiniteQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.requirementsInfiniteQuery.isFetching;
    }

    loadRequirements = (frameworkId: number, frameworkSlug: string): void => {
        this.#frameworkId = frameworkId;
        this.#frameworkSlug = frameworkSlug;

        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        this.requirementsInfiniteQuery.load({
            path: { xProductId: currentWorkspace.id },
            query: {
                frameworkId,
                frameworkSlug,
                page: 1,
                limit: 20,
                q: this.#lastSearchQuery,
            },
        });
    };

    search(searchTerm: string): void {
        if (searchTerm === this.#lastSearchQuery) {
            return;
        }

        this.#lastSearchQuery = searchTerm;
        this.requirementsInfiniteQuery.unload();

        // Use stored framework ID and slug instead of trying to extract from query data
        if (this.#frameworkId && this.#frameworkSlug) {
            this.loadRequirements(this.#frameworkId, this.#frameworkSlug);
        }
    }

    loadNextPage = (): void => {
        if (this.hasNextPage && !this.isFetching) {
            this.requirementsInfiniteQuery.nextPage();
        }
    };
}

export const sharedRequirementsInfiniteController =
    new RequirementsInfiniteController();
