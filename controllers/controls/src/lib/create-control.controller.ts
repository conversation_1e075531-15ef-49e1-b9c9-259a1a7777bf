import { isArray, isEmpty, isError, isObject } from 'lodash-es';
import { sharedMonitorsInfiniteController } from '@controllers/monitors';
import { sharedRequirementsController } from '@controllers/requirements';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerCreateControlMutation } from '@globals/api-sdk/queries';
import {
    action,
    makeAutoObservable,
    ObservedMutation,
    when,
} from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    type ControlInfoValues,
    sharedCreateControlInfoModel,
    sharedMapEvidenceModel,
    sharedMapPoliciesModel,
} from '@models/controls';

class CreateControlController {
    createControlMutation = new ObservedMutation(
        grcControllerCreateControlMutation,
    );

    constructor() {
        makeAutoObservable(this);
    }

    get isSubmitting(): boolean {
        return this.createControlMutation.isPending;
    }

    get error(): Error | null {
        return this.createControlMutation.error;
    }

    createControl = async (
        controlInfoValues: ControlInfoValues,
    ): Promise<boolean> => {
        try {
            const workspaceId = sharedWorkspacesController.currentWorkspace?.id;

            if (!workspaceId) {
                snackbarController.addSnackbar({
                    id: 'control-create-workspace-error',
                    props: {
                        title: 'Failed to create control',
                        description: 'No workspace selected',
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                return false;
            }

            const requestBody = {
                name: controlInfoValues.name || '',
                code: controlInfoValues.code || '',
                description: controlInfoValues.description || '',
                ...(controlInfoValues.question?.trim()
                    ? { question: controlInfoValues.question }
                    : {}),
                ...(controlInfoValues.activity?.trim()
                    ? { activity: controlInfoValues.activity }
                    : {}),
                owners:
                    controlInfoValues.owner && !isEmpty(controlInfoValues.owner)
                        ? controlInfoValues.owner.map((owner) =>
                              Number(owner.id),
                          )
                        : [],

                requirementIds: isEmpty(
                    sharedRequirementsController.mappedRequirements,
                )
                    ? []
                    : sharedRequirementsController.mappedRequirements.map(
                          (req) => req.id,
                      ),

                testIds: isEmpty(
                    sharedMonitorsInfiniteController.selectedMonitors,
                )
                    ? []
                    : sharedMonitorsInfiniteController.selectedMonitors.map(
                          (monitor) => Number(monitor.testId),
                      ),

                reportIds: isEmpty(sharedMapEvidenceModel.selectedEvidence)
                    ? []
                    : sharedMapEvidenceModel.selectedEvidence.map((evidence) =>
                          Number(evidence.id),
                      ),

                policyIds: isEmpty(sharedMapPoliciesModel.selectedPolicies)
                    ? []
                    : sharedMapPoliciesModel.selectedPolicies.map(
                          (policy) => policy.id,
                      ),

                // TODO: These are required fields in the DTO. Need to look further into what they are used for
                externalEvidenceMetadata: '',
                base64Files: '[]',
            };

            this.createControlMutation.mutate({
                path: {
                    xProductId: workspaceId,
                },
                body: requestBody,
            });

            await when(() => !this.createControlMutation.isPending);

            if (action(() => this.createControlMutation.hasError)()) {
                const { error } = this.createControlMutation;
                let errorMessage = 'An error occurred';

                if (error) {
                    if (isError(error)) {
                        errorMessage = error.message;
                    } else if (isObject(error)) {
                        const apiError = error as {
                            message?: string | string[];
                            statusCode?: number;
                        };

                        if (apiError.message) {
                            if (isArray(apiError.message)) {
                                errorMessage = apiError.message.join('\n');
                            } else {
                                errorMessage = apiError.message;
                            }
                        }
                    }
                }

                snackbarController.addSnackbar({
                    id: 'control-create-error',
                    props: {
                        title: 'Failed to create control',
                        description: errorMessage,
                        severity: 'critical',
                        closeButtonAriaLabel: 'Close',
                    },
                });

                return false;
            }

            snackbarController.addSnackbar({
                id: 'control-create-success',
                hasTimeout: true,
                props: {
                    title: 'Control created',
                    description: 'The control was created successfully.',
                    severity: 'success',
                    closeButtonAriaLabel: 'Close',
                },
            });

            sharedCreateControlInfoModel.setControlInfoValues({});

            return true;
        } catch (error) {
            let errorMessage = 'An unexpected error occurred';

            if (isError(error)) {
                errorMessage = error.message;
            }

            snackbarController.addSnackbar({
                id: 'control-create-unexpected-error',
                props: {
                    title: 'Failed to create control',
                    description: errorMessage,
                    severity: 'critical',
                    closeButtonAriaLabel: 'Close',
                },
            });

            return false;
        }
    };
}

export const sharedCreateControlController = new CreateControlController();
