/* eslint-disable custom/enforce-data-testid -- For Storybook styles, not QA testing */
/* eslint-disable custom/enforce-data-id -- For Storybook styles, not QA testing */
/* eslint-disable @typescript-eslint/no-unused-vars -- React needed for Storybook */
// @ts-expect-error -- React is needed for Storybook
import React from 'react';
import type { HashEntry } from 'storybook/manager-api';
import { StorybookStatusTag } from './StorybookStatusTag';

export const StorybookItemLabel = ({
    name,
    type,
    tags = [],
}: Partial<HashEntry>): JSX.Element => (
    <span style={{ display: 'flex', width: '100%' }}>
        <span style={{ flex: 1 }}>{name}</span>
        {type === 'component' &&
            (() => {
                if (tags.includes('Deprecated')) {
                    return (
                        <StorybookStatusTag colorScheme="neutral">
                            Deprecated
                        </StorybookStatusTag>
                    );
                }
                if (tags.includes('Private')) {
                    return (
                        <StorybookStatusTag colorScheme="critical">
                            Private
                        </StorybookStatusTag>
                    );
                }
                if (tags.includes('Lab')) {
                    return (
                        <StorybookStatusTag colorScheme="warning">
                            Lab
                        </StorybookStatusTag>
                    );
                }

                // Stable tags do not get rendered in the sidebar
                return null;
            })()}
    </span>
);
