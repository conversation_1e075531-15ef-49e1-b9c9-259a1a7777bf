/* eslint-disable no-restricted-imports -- mock for the imports */
export * from '@lingui/core';
export * from '@lingui/react';

// Mock for sharedDirectLocaleController
export const sharedDirectLocaleController = {
    getLocale: (): string => 'en-US',
    setLocale: (locale: string | null): void => {
        // Mock implementation - in real app this would update the locale
        if (locale) {
            sharedDirectLocaleController.locale = locale;
        }
    },
    locale: 'en-US',
};

/**
 * Mock for dynamicActivateLocale.
 */
export const dynamicActivateLocale = async (): Promise<void> => {
    // Mock implementation
};

/**
 * Mock for i18nLoader.
 */
export const i18nLoader = (): void => {
    // Mock implementation
};
