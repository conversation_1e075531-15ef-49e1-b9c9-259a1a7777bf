/* eslint-disable custom/enforce-data-id -- For Storybook styles, not QA testing */
/* eslint-disable @typescript-eslint/no-unused-vars -- React needed for Storybook */
// @ts-expect-error -- React is needed for Storybook
import React from 'react';
import { addons } from 'storybook/manager-api';
import { create } from 'storybook/theming/create';
import CosmosLogo from './cosmos_logo.svg';
import { StorybookItemLabel } from './StorybookItemLabel';

addons.setConfig({
    theme: create({
        base: 'light',
        brandImage: CosmosLogo,
        brandTitle: 'Cosmos Design System',
    }),
    sidebar: {
        filters: {
            patterns: (item): boolean => !item.tags?.includes('isHidden'),
        },
        renderLabel: ({ name, type, tags }) => {
            return <StorybookItemLabel name={name} type={type} tags={tags} />;
        },
    },
});
