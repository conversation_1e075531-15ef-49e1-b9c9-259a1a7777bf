<!-- Include design tokens CSS variables for Storybook manager -->
<style>
    /* Essential design tokens for Storybook manager */
    :root {
        /* Font sizes */
        --font-size-100: 12px;

        /* Dimensions */
        --dimension-05x: 2px;
        --dimension-2x: 8px;
        --dimension-xs: var(--dimension-05x);
        --dimension-md: var(--dimension-2x);

        /* Border radius */
        --border-radius-2x: 4px;
        --border-radius-md: var(--border-radius-2x);

        /* Colors - Light theme */
        /* Warning colors */
        --color-palette-light-warning-yellow-50: #fdf7f3;
        --color-palette-light-warning-yellow-900: #906222;
        --warning-background-mild: var(--color-palette-light-warning-yellow-50);
        --warning-text-initial: var(--color-palette-light-warning-yellow-900);

        /* Neutral colors */
        --color-palette-light-concrete-gray-50: #f0f1f5;
        --color-palette-light-forest-gray-1000: #2c3944;
        --neutral-background-mild: var(--color-palette-light-concrete-gray-50);
        --neutral-text-initial: var(--color-palette-light-forest-gray-1000);

        /* Success colors */
        --color-palette-light-success-green-50: #f2f9f3;
        --color-palette-light-success-green-900: #0d6e2e;
        --success-background-mild: var(--color-palette-light-success-green-50);
        --success-text-initial: var(--color-palette-light-success-green-900);

        /* Critical colors */
        --color-palette-light-critical-red-50: #fcf2f3;
        --color-palette-light-critical-red-800: #a2232e;
        --critical-background-mild: var(--color-palette-light-critical-red-50);
        --critical-text-initial: var(--color-palette-light-critical-red-800);
    }
</style>
