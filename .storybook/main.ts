import remarkGfm from 'remark-gfm';
import type { StorybookConfig } from '@storybook/react-vite';

const config: StorybookConfig = {
    stories: [
        '../**/storybook/**/*.mdx',
        '../**/*.mdx',
        '../**/*.stories.@(js|jsx|mjs|cjs|ts|tsx|mdx)',
    ],
    addons: [
        '@storybook/addon-a11y',
        '@storybook/addon-themes',
        '@storybook/addon-links',
        {
            name: '@storybook/addon-docs',
            options: {
                mdxPluginOptions: {
                    mdxCompileOptions: {
                        remarkPlugins: [remarkGfm],
                    },
                },
            },
        },
    ],
    framework: {
        name: '@storybook/react-vite',
        options: {},
    },
    staticDirs: ['./public'],
    typescript: {
        reactDocgen: 'react-docgen-typescript',
        reactDocgenTypescriptOptions: {
            EXPERIMENTAL_useWatchProgram: true,
        },
    },
    core: {
        disableTelemetry: true,
    },

    /**
     * See https://storybook.js.org/docs/builders/vite#environment-based-configuration. Necessary to get fonts loading correctly.
     *
     * TODO: A more secure way to allow fonts to load in Storybook.
     */
    async viteFinal(viteConfig) {
        const { mergeConfig, defineConfig } = await import('vite');
        const path = await import('node:path');

        const configOverrides = defineConfig({
            resolve: {
                alias: {
                    '@globals/i18n': path.resolve(__dirname, 'i18n.mock.ts'),
                    '@globals/i18n/macro': path.resolve(
                        __dirname,
                        'i18n-macro.mock.ts',
                    ),
                },
            },
            server: {
                fs: {
                    strict: false,
                },
            },
        });

        return mergeConfig(viteConfig, configOverrides);
    },
};

export default config;

// To customize your Vite configuration you can use the viteFinal field.
// Check https://storybook.js.org/docs/react/builders/vite#configuration
// and https://nx.dev/recipes/storybook/custom-builder-configs
