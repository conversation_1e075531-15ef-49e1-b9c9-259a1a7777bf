/**
 * Helper function to detect empty objects and functions.
 */
const isEmptyObjectOrFunction = (
    chunk: string,
    index: number,
    splittedCode: string[],
) => {
    const trimmedChunk = chunk.trim();
    let isEmptyObject = false;

    // Check for closing braces '}}'
    if (trimmedChunk.includes('}}')) {
        // Check if the previous chunk is an opening brace '{{'
        const previousChunk = splittedCode[index - 1]?.trim();

        if (previousChunk.includes('{{')) {
            // If previous chunk is '{{', mark as empty object
            isEmptyObject = true;
        }
    }

    // Check for opening braces '{{'
    if (trimmedChunk.includes('{{')) {
        // Look ahead for a matching closing brace '}}' and check in-between chunks
        for (let i = index + 1; i < splittedCode.length; i = i + 1) {
            const nextChunk = splittedCode[i].trim();

            if (nextChunk.includes('}}')) {
                // Collect the in-between chunks for validation
                const inBetweenChunks = splittedCode.slice(index + 1, i);
                const hasValidContent = inBetweenChunks.some((content) => {
                    const trimmedContent = content.trim();

                    return !(
                        (
                            trimmedContent === '' ||
                            trimmedContent.includes('undefined') ||
                            trimmedContent.includes('null') ||
                            trimmedContent === "''" ||
                            trimmedContent === '""' ||
                            (trimmedContent.includes('=') &&
                                trimmedContent.split('=')[1].trim() === '')
                        ) // checks for assignments with empty values
                    ); // Check for valid properties
                });

                // If there's no valid content between '{{' and '}}', mark it as empty
                if (!hasValidContent) {
                    return true; // Return true to indicate this is an empty object
                }
            }
        }
    }

    // Check for empty arrow functions like () => {}
    const isEmptyFunction = /\(\s*\)\s*=>\s*\{\s*\}/.test(trimmedChunk);

    return isEmptyObject || isEmptyFunction; // Return true if it's an empty object or function
};

/**
 * Main function to exclude empty props, including objects with empty values.
 */
export const excludeEmptyProps = (code: string): string => {
    const splittedCode = code.split('\n');

    // Filter out chunks with 'undefined', empty strings, or 'null'
    const excludedEmptyArgs = splittedCode.filter((chunk: string) => {
        return (
            !chunk.includes('undefined') &&
            !chunk.includes('""') &&
            !chunk.includes("''") &&
            !chunk.includes('null')
        );
    });

    // Prepare to filter out empty objects or functions
    return excludedEmptyArgs
        .filter((chunk, index) => {
            // Determine if the current chunk or its surrounding chunks represent empty objects/functions
            return !isEmptyObjectOrFunction(chunk, index, excludedEmptyArgs);
        })
        .join('\n');
};
