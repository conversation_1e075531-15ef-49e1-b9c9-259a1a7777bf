/* eslint-disable @typescript-eslint/consistent-type-imports -- Needed for Storybook */
/* eslint-disable custom/enforce-data-testid -- For Storybook styles, not QA testing */
/* eslint-disable custom/enforce-data-id -- For Storybook styles, not QA testing */
import React from 'react';

// Tag component for Storybook sidebar
export interface StorybookStatusTagProps {
    colorScheme: 'warning' | 'neutral' | 'success' | 'critical';
    children: React.ReactNode;
}

export const StorybookStatusTag = ({
    colorScheme,
    children,
}: StorybookStatusTagProps): JSX.Element => (
    <span
        style={{
            backgroundColor: `var(--${colorScheme}-background-mild)`,
            borderRadius: 'var(--border-radius-md)',
            padding: 'var(--dimension-xs) var(--dimension-md)',
            fontSize: 'var(--font-size-100)',
            marginLeft: 'var(--dimension-md)',
            color: `var(--${colorScheme}-text-initial)`,
            whiteSpace: 'nowrap',
        }}
    >
        {children}
    </span>
);
