import '@drata/design-system/fonts/Inter/inter.css';
import '@drata/design-system/fonts/IBMPlexMono/IBMPlexMono.css';
import '@radix-ui/themes/styles.css';
import { i18n, I18nProvider } from '@globals/i18n';
import { withThemeByDataAttribute } from '@storybook/addon-themes';
import type { Preview, StoryFn } from '@storybook/react-vite';
import { excludeEmptyProps } from './sourceBlock.helper';

// Initialize i18n with default locale
i18n.load('en-US', {});
i18n.activate('en-US');

const withProviders = (Story: StoryFn): React.JSX.Element => {
    return (
        <I18nProvider
            i18n={i18n}
            data-testid="withProviders"
            data-id="0TK8ffUq"
        >
            <Story data-testid="withProviders" data-id="2EsKjjqR" />
        </I18nProvider>
    );
};

const withThemes = withThemeByDataAttribute({
    themes: {
        light: 'light',
        dark: 'dark',
    },
    defaultTheme: 'light',
    attributeName: 'data-theme',
});

const preview: Preview = {
    parameters: {
        docs: {
            toc: {
                headingSelector: 'h2, h3',
                ignoreSelector: '.docs-story h2, .docs-story h3',
            },
            source: {
                dark: true,
                transform: (code: string) => excludeEmptyProps(code),
            },
        },
        options: {
            /**
             * IMPORTANT: The storySort function runs in Storybook's own global context,
             * which means it cannot access external functions, variables, or use TypeScript.
             * All logic must be contained within the function itself, and we need extensive
             * ESLint/TypeScript disables to handle the constraints.
             *
             * See: https://github.com/storybookjs/storybook/issues/22779.
             */
            /* eslint-disable
               @typescript-eslint/no-unsafe-call,
               @typescript-eslint/no-unsafe-member-access,
               @typescript-eslint/no-unsafe-return,
               @typescript-eslint/no-unsafe-argument,
               unicorn/consistent-function-scoping
               -- All needed for storySort function compatibility with Storybook's execution context */
            // @ts-expect-error -- Storybook's storySort function parameters are typed as 'any'
            storySort: (a, b) => {
                /**
                 * IMPORTANT: This category list is duplicated in the storybookCategories global below.
                 *
                 * Why the duplication?
                 * - This hardcoded array is required because storySort runs in an isolated scope
                 * and cannot access external variables or Storybook globals
                 * - The storybookCategories global is used by the cosmos-lab generator for
                 * category selection and management
                 * - Both arrays are automatically synchronized by the generator when new
                 * categories are added via `pnpm run generate:cosmos-lab`.
                 *
                 * DO NOT manually edit this array - use the generator instead to ensure
                 * both locations stay synchronized.
                 */
                const sectionOrder = [
                    'Foundations',
                    'Tokens',
                    'Patterns',
                    'Layout & Structure',
                    'Typography & Content',
                    'Navigation',
                    'Actions',
                    'Forms',
                    'Information & Data',
                    'AI',
                    'Feedback',
                    'Document Viewers',
                    'Media & Imagery',
                    'Support Components',
                ];

                /**
                 * Helper function to get parts from story title.
                 */
                // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                const getTitleParts = function (title) {
                    return title.split('/');
                };

                /**
                 * Helper function to get section name from story title.
                 */
                // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                const getSection = function (title) {
                    const parts = getTitleParts(title);

                    return parts[0] || '';
                };

                /**
                 * Helper function to get story name from title (everything after the last /).
                 */
                // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                const getStoryName = function (title) {
                    const parts = getTitleParts(title);

                    return parts[parts.length - 1];
                };

                /**
                 * Helper function to check if story is an Overview.mdx.
                 */
                // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                const isOverview = function (item) {
                    return (
                        item.name === 'Overview' ||
                        item.title.endsWith('/Overview') ||
                        item.title.endsWith('Overview')
                    );
                };

                /**
                 * Helper function to check if story is a documentation page.
                 * Documentation pages typically have specific title patterns or tags.
                 */
                // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                const isDocumentation = function (item) {
                    // Check for documentation-specific title patterns
                    const titleParts = getTitleParts(item.title);
                    const lastPart = titleParts[titleParts.length - 1];

                    // Documentation pages often have these patterns
                    const docPatterns = [
                        'Columns',
                        'Search',
                        'Table Actions',
                        'Data',
                        'Filters',
                    ];

                    return (
                        item.title.includes('/docs/') ||
                        item.name === 'Docs' ||
                        item.title.endsWith('/Docs') ||
                        docPatterns.includes(lastPart)
                    );
                };

                /**
                 * Helper function to get section order index.
                 */
                // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                const getSectionIndex = function (section) {
                    const index = sectionOrder.indexOf(section);

                    return index === -1 ? sectionOrder.length : index;
                };

                /**
                 * Helper function to sort by custom order array.
                 */
                const sortByCustomOrder = function (
                    // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                    aValue,
                    // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                    bValue,
                    // @ts-expect-error -- Needed to suppress TypeScript errors for storySort function (see https://github.com/storybookjs/storybook/issues/22779)
                    customOrder,
                ) {
                    const aIndex = customOrder.indexOf(aValue);
                    const bIndex = customOrder.indexOf(bValue);

                    // If both items are in the custom order, sort by that order
                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    }
                    // If only one is in the custom order, prioritize it
                    if (aIndex !== -1) {
                        return -1;
                    }
                    if (bIndex !== -1) {
                        return 1;
                    }

                    // Neither is in custom order, return 0 to continue with default sorting
                    return 0;
                };

                const aSection = getSection(a.title);
                const bSection = getSection(b.title);
                const aSectionIndex = getSectionIndex(aSection);
                const bSectionIndex = getSectionIndex(bSection);

                // First, sort by section order
                if (aSectionIndex !== bSectionIndex) {
                    return aSectionIndex - bSectionIndex;
                }

                // Within the same section, Overview.mdx always comes first
                const aIsOverview = isOverview(a);
                const bIsOverview = isOverview(b);

                if (aIsOverview && !bIsOverview) {
                    return -1;
                }
                if (!aIsOverview && bIsOverview) {
                    return 1;
                }

                // After Overview, documentation pages come next
                const aIsDoc = isDocumentation(a);
                const bIsDoc = isDocumentation(b);

                if (aIsDoc && !bIsDoc) {
                    return -1;
                }
                if (!aIsDoc && bIsDoc) {
                    return 1;
                }

                // Custom sorting for Foundations section
                if (aSection === 'Foundations') {
                    const foundationsOrder = [
                        'Overview',
                        'Design Principles',
                        'Content',
                        'Using Figma',
                        'Code Standards',
                        'Accessibility',
                        'Responsiveness',
                        'Contributing',
                    ];

                    const aStoryName = getStoryName(a.title);
                    const bStoryName = getStoryName(b.title);
                    const customSortResult = sortByCustomOrder(
                        aStoryName,
                        bStoryName,
                        foundationsOrder,
                    );

                    if (customSortResult !== 0) {
                        return customSortResult;
                    }
                }

                // For everything else, sort alphabetically by title (which includes the full path)
                return a.title === b.title
                    ? 0
                    : a.title.localeCompare(b.title, undefined, {
                          numeric: true,
                      });
            },
        },
        viewport: {
            viewports: {
                lgMedia: {
                    name: 'Large Media (lg)',
                    styles: {
                        width: '1200px',
                        height: '675px',
                    },
                },
                xlMedia: {
                    name: 'Extra Large Media (xl)',
                    styles: {
                        width: '1440px',
                        height: '900px',
                    },
                },
            },
        },
    },
    decorators: [withProviders, withThemes],
    globalTypes: {
        theme: {
            description: 'Select light or dark theme',
            toolbar: {
                title: 'Theme',
                items: [
                    { value: 'light', icon: 'sun', title: 'light mode' },
                    { value: 'dark', icon: 'moon', title: 'dark mode' },
                ],
                dynamicTitle: true,
            },
            defaultValue: 'light',
        },
        /**
         * IMPORTANT: This category list is duplicated in the storySort function above.
         *
         * Why the duplication?
         * - This global is used by the cosmos-lab generator for category selection
         * and management when creating new components.
         * - The hardcoded array in storySort is required because that function runs
         * in an isolated scope and cannot access Storybook globals.
         * - Both arrays are automatically synchronized by the generator when new
         * categories are added via `pnpm run generate:cosmos-lab`.
         *
         * DO NOT manually edit this array - use the generator instead to ensure
         * both locations stay synchronized.
         */
        storybookCategories: {
            description: 'Internal: Storybook categories for sorting',
            defaultValue: [
                'Foundations',
                'Tokens',
                'Patterns',
                'Layout & Structure',
                'Typography & Content',
                'Navigation',
                'Actions',
                'Forms',
                'Information & Data',
                'AI',
                'Feedback',
                'Document Viewers',
                'Media & Imagery',
                'Support Components',
            ],
        },

        storybookDocumentationOnlyCategories: {
            description: 'Internal: Categories that should not have components',
            defaultValue: ['Foundations', 'Tokens', 'Patterns'],
        },
    },
};

export default preview;
