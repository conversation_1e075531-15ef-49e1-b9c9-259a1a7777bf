import { isEmpty } from 'lodash-es';
import { routeController } from '@controllers/route';
import { PageHeader as CosmosPageHeader } from '@cosmos/components/page-header';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { observer, toJS } from '@globals/mobx';

export const PageHeaderUi = observer((): React.JSX.Element => {
    const { pageHeader, contentNavItems } = routeController;

    const {
        actionStack,
        backLink,
        banner,
        keyValuePairs,
        pageId,
        slot,
        slotTopAlign,
        title,
        breadcrumbs,
        isLoading,
    } = pageHeader ?? {};

    return (
        <Stack
            direction="column"
            data-id="page-header-ui-stack"
            data-testid="PageHeaderUi"
        >
            <CosmosPageHeader
                actionStack={toJS(actionStack)}
                backLink={toJS(backLink)}
                banner={toJS(banner)}
                keyValuePairs={toJS(keyValuePairs)}
                pageId={toJS(pageId) ?? ''}
                slot={toJS(slot)}
                slotTopAlign={toJS(slotTopAlign)}
                title={toJS(title) ?? ''}
                breadcrumbs={toJS(breadcrumbs)}
                data-id="ps1PXMwe"
                isLoading={toJS(isLoading ?? false)}
            />
            {isEmpty(contentNavItems) && (
                <Divider orientation="horizontal" size="sm" />
            )}
        </Stack>
    );
});
