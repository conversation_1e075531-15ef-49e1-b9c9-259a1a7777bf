import { Fragment } from 'react';
import { Link } from '@cosmos/components/link';
import type { Meta, StoryObj } from '@storybook/react-vite';
import { List } from '../../list';

const meta: Meta<typeof List> = {};

export default meta;
type Story = StoryObj<typeof List>;

export const ReactNodes: Story = {
    name: 'React Nodes as List Items',
    render: () => {
        const items = [
            <Fragment key="1">
                Check out our homepage{' '}
                <Link
                    isExternal
                    href="https://drata.com"
                    label="Drata.com"
                    colorScheme="primary"
                    size="md"
                />
            </Fragment>,
            <Fragment key="2">
                View our products{' '}
                <Link
                    isExternal
                    href="https://drata.com/products"
                    label="Products Overview"
                    colorScheme="primary"
                    size="md"
                />
            </Fragment>,
            <Fragment key="3">
                Need help? Visit{' '}
                <Link
                    isExternal
                    href="https://help.drata.com"
                    label="Help Center"
                    colorScheme="primary"
                    size="md"
                />
            </Fragment>,
        ];

        return (
            <List
                items={items}
                type="unordered"
                size="200"
                colorScheme="neutral"
                data-id="cMVMC4HU"
            />
        );
    },
};
