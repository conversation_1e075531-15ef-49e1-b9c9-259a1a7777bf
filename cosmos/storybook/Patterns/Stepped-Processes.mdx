import { Meta } from '@storybook/addon-docs/blocks';
import {
  wizardPatternImage,
  stepsContentImage,
  formProgressiveDisclosureImage,
  formDataSavedIncrementallyImage,
  formSectionCardImage,
  saveAndContinueImage,
  nextSectionRevealImage,
} from './assets/stepped-processes';

<Meta title="Patterns/Stepped Processes" />

# Stepped Processes

Stepped processes help users complete complex tasks by breaking them into manageable, sequential steps. This pattern reduces cognitive load and provides clear progress indicators, making it easier for users to understand where they are in a workflow and what comes next.

This section covers two primary stepped process patterns:

- **Wizard**: A guided, multi-step flow for creating new objects with clear progress indicators and navigation controls
- **Form Progressive Disclosure**: A form pattern that reveals sections incrementally based on user input, where each section builds on previous responses

Choose the appropriate pattern based on your specific use case, the complexity of the task, and whether the steps are interdependent.

# Wizard

Use the Wizard pattern when customers need to create new objects in Drata through a step-by-step flow. Wizards help break complex tasks into manageable chunks with clear progress indicators.

<img src={wizardPatternImage} alt="Wizard pattern showing step-by-step flow with progress indicators" />

## **✅ When to Use**

- Creating new platform objects (e.g. controls, frameworks, evidence).
- Occasionally, adding items to existing objects—**must be reviewed by the Design team** before adding.
- When the process requires multiple steps or decisions, and users benefit from a guided flow.

## **🚫 When Not to Use**

- For quick, one-step tasks—use a modal, drawer, or form instead.
- For editing existing objects—prefer inline editing patterns.

### **Steps**

- Displays progress across the top of the wizard.
- Shows:
    - Step number or label
    - Visual indicator of completed, current, or upcoming steps
- Should be persistent across all screens of the wizard.

### **Steps Content**

- Main container for form fields or interactions per step.
- Can support **substeps**:
    - Optional substep title (use only if clarity is needed)
    - Use clear visual hierarchy to differentiate from main step titles

<img src={stepsContentImage} alt="Steps content showing main container with form fields and substeps" />

### **CTA Bar**

- Fixed to the bottom of the screen for consistent action access.
- Left side:
    - **Back** (always visible after step 1)
    - **Cancel** (visible on step 1 only, exits the wizard)
- Right side:
    - **Skip** (optional, appears only when a step is skippable)
    - **Next** (or **Finish** on the last step)

# Form progressive disclosure

Use this pattern when a form needs to be broken into smaller, digestible sections where each part builds on the user’s previous input. Better suited for situations where completing one section unlocks or changes the next. Use this pattern when the information in previous sections is needed to complete the next.

<img src={formProgressiveDisclosureImage} alt="Form progressive disclosure pattern showing sections that build on previous input" />

## **✅ When to Use**

- When user input in one form section determines what’s needed in the next section.
- When there’s a clear linear flow, but steps don’t warrant a full wizard.
- When form data must be saved incrementally.

<img src={formDataSavedIncrementallyImage} alt="Form data saved incrementally showing how data is preserved between sections" />

## **🚫 When Not to Use**

- If the form can be completed all at once—use a standard stacked form layout instead.
- If the form has unrelated sections—use grouped wizards, forms or tabs instead.
- If the process is object creation and spans multiple decisions—use the Wizard pattern.

### **Form Sections (Cards)**

- Each section is visually distinct and housed in a card.
- Title clearly explains what information is being collected.
- Description text optional, helpful for clarifying purpose.

<img src={formSectionCardImage} alt="Form section card showing visually distinct sections with titles and descriptions" />

### **Save & Continue Button**

- Appears at the bottom of each section.
- Card section is in edit mode
- Saves data from that section and reveals the next card below.

<img src={saveAndContinueImage} alt="Save and continue button at the bottom of form sections" />

### **Next Section Reveal**

- Card content hidden by default.
- Becomes visible after the previous section is successfully saved.
- May change dynamically based on earlier responses.

<img src={nextSectionRevealImage} alt="Next section reveal showing how content becomes visible after previous section is saved" />
