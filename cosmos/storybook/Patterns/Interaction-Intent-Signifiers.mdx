import { Meta } from '@storybook/addon-docs/blocks';
import {
  chevronsImage,
  linkOutImage,
  openImage,
} from './assets/interaction-intent-signifiers';

<Meta title="Patterns/Interaction Intent Signifiers" />

# Interaction Intent Signifiers

**Icon usage rules for distinguishing low vs. high disruption interactions**

This pattern supports our [Interaction Levels](https://www.notion.so/Interaction-Levels-1e0095aed4f18047afbdf69948468bdd?pvs=21) by providing visual affordance signifiers for the expected depth of interaction.

We use similar icons—**Chevrons**, ~~Expand~~ **Open**, and **LinkOut**—to communicate different levels of interaction and navigation intent. This distinction helps users quickly understand what will happen before they click.

Use these icons consistently to guide expectations:

## Chevrons

<img src={chevronsImage} alt="Screenshot of Chevron icons" />

Low-disruption reveal (e.g. expand a section, see more inline). Reveals or expands content in-place.

- Use in neutral color
- Use when the user stays in the *same view*.
- Often rotates to show expanded/collapsed state.
- Passive, subtle cue — not the main CTA.
- Label with verbs like **“Show more”**, **“Expand”**, **“See details”**
- Use cases: Accordion, dropdown, inline expansion, pagination, tab overflow, etc.

## Open

<img src={openImage} alt="Screenshots of buttons using the Open icon" />

High-disruption navigation (e.g. open a modal, new page). Opens new context or navigates.

- Use in primary color
- Use when the user is taken to a *new context*.
- Often paired with detail text or data.
- Draws attention due to color—reinforces “this will take you somewhere.”
- Label with **“View full report”**, **“Open in modal”**, **“Go to details”**.

## LinkOut

<img src={linkOutImage} alt="Screenshots of link and button using the LinkOut icon" />

Only used for linking to another site outside of the Drata app.

Common use cases include:

- link to our help documentation
- link to our marketing website
- link to a partner or integration site

Links should always be in the primary colorScheme.
