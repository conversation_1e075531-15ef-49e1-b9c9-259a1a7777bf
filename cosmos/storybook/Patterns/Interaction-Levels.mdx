import { Meta } from '@storybook/addon-docs/blocks';
import { level0Image, level1Image, level2Image } from './assets/interaction-levels';

<Meta title="Patterns/Interaction Levels" />

# Interaction Levels

Drata has a handful of key layouts to support our depth of interaction. For example, most of the top-level navigation sections begin with a list or table view. These often branch off, based on the user’s intent, into creation/editing experience or detail views of the objects in the table or list. In order to maintain consistency and establish consistent expectations for users, we have established some guidelines and parameters around how these experiences should flow from one to the other. We have identified three levels and set rules for transitioning between them.

**Components:**

- [Utilities](https://www.notion.so/Utilities-a040888596d04e468fee6a3e6d1f77cd?pvs=21)—not yet in storybook
- [Panel](https://www.notion.so/Panel-a3a451adca2c4abfb92350af632e1e31?pvs=21)—not yet in storybook
- Modal
- Detail page,

## Level 0—Full page experiences

<img src={level0Image} alt="diagram of inteaction level 0" />

The first level of interaction contains our full page experiences. Most often this would be a table, list, or full page detail experience.

**Key use cases:**

- table or list from a top level navigation page
- focused detail page experience of a Drata object

**Key details:**

- interaction can flow to any other level from level 0
- some full page experiences can include secondary supplementary UI like the [aside component](https://www.notion.so/Utilities-a040888596d04e468fee6a3e6d1f77cd?pvs=21).

## Level 1—non-modal (non-blocking) panel or utilities

<img src={level1Image} alt="diagram of inteaction level 1" />

The second level of interaction contains non-modal panels. This is an optional level of the interaction flow and has some rigid guidelines on when it can be used.

**Key use cases:**

- detail experience of a Drata object accessed from a table or list in a full page experience
- scoped creation task in a builder context

**Key details:**

Interaction can go to any level from level 1 but use is limited by:

- should stay non-modal within the same object type
E.g. user is interacting with a **Control non-modal panel**, and **navigates to another control**, it should **stay non-modal panel**.
- must go modal or or to a full page experience for new object types or activities
E.g. user is interacting with a **Control non-modal panel**, and **navigates to a related object**, such as a risk, it **must transition to a modal or full page experience**.

## Level 2—Scoped action or related object detail

The last level of the interaction flow contains two key layouts

**Key details:**

- both are modal and block interaction with the rest of the page content until completed or dismissed
- interaction can flow to other levels but flow to level 1 is limited by:
    - can only flow to level 1 from level 2 IF the action is a return action.
    E.g. user is interacting with a **Control non-modal panel**, and **navigates to a related object**, such as a test, which **transitions to a modal panel experience**, interaction flow **can only return to the level 1 non-modal panel IF going back** to the Control non-modal panel they were previously viewing.

### Modal dialog

<img src={level2Image} alt="diagram of inteaction level 2" />

**Key use cases:**

- Edit, creation, or review task
- Wizard or stepped process
- Alerts
