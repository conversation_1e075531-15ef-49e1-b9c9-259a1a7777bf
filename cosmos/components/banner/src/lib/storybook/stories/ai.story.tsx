import { But<PERSON> } from '@cosmos/components/button';
import { <PERSON> } from '@cosmos/components/link';
import type { StoryObj } from '@storybook/react-vite';
import type { Banner } from '../../banner';

export default {};
type Story = StoryObj<typeof Banner>;

export const AI: Story = {
    args: {
        severity: 'ai',
        title: 'Speed up control mapping with AI',
        body: (
            <>
                Drata’s AI suggests relevant controls, helping you highlight
                strengths and spot gaps in policy coverage. Our generative AI
                approach keeps your data secure.{' '}
                <Link
                    href="https://help.drata.com/"
                    label="Learn how AI works in Drata"
                />
            </>
        ),
        displayMode: 'section',
        action: (
            <Button
                label="Enable AI suggestions"
                size="sm"
                level="secondary"
                colorScheme="primary"
                data-id="ai-banner-action"
                onClick={() => {
                    alert('AI insights clicked!');
                }}
            />
        ),
        onClose: () => {
            alert('AI banner dismissed');
        },
        closeButtonAriaLabel: 'Do not enable AI suggestions',
    },
};
