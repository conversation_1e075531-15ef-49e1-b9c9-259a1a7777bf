import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { Icon } from '@cosmos/components/icon';
import { Link } from '@cosmos/components/link';
import { dimensionMd } from '@cosmos/constants/tokens';
import type { Meta } from '@storybook/react-vite';
import { PageHeader } from '../page-header';
import { SAMPLE_ACTIONS, SAMPLE_KEY_VALUE_PAIRS } from './constants';

const meta: Meta<typeof PageHeader> = {
    tags: ['Stable'],
    title: 'Layout & Structure/PageHeader',
    component: PageHeader,
    args: {
        backLink: <Link href="#" label="Back to previous page" size="sm" />,
        title: 'Page Title',
        slot: <Icon name="Connections" size="600" />,
        actionStack: <ActionStack gap={dimensionMd} stacks={SAMPLE_ACTIONS} />,
        keyValuePairs: SAMPLE_KEY_VALUE_PAIRS,
        banner: <Banner title="Title" displayMode="full" />,
        pageId: 'page-header-example',
        isLoading: false,
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page

export { Loading, Playground } from './stories';
