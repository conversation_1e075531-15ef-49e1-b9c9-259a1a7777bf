import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import * as ButtonStories from './Button.stories';
import buttonScreenReader from './assets/button-screen-reader.png';
import buttonNoLabel from './assets/button-no-label.png';
import buttonWithLabel from './assets/button-with-label.png';
import buttonLoading from './assets/button-loading.gif';
import buttonDownload from './assets/button-download.png';
import buttonDownloadA11yLabelOverride from './assets/button-download-a11yLabelOverride.png';
import hierarchy from './assets/hierarchy.png';
import iconUsage from './assets/icon-usage.png';
import leftAlignment from './assets/left-alignment.png';
import rightAlignment from './assets/right-alignment.png';
import mobile from './assets/mobile.png';

<Meta of={ButtonStories} />

<Title />

*The Tooltip component displays additional information on hover or focus.*
<Description />

<Primary />

<Controls of={ButtonStories.Playground} />

## Import

```jsx
import { Button } from '@drata/cosmos-button';
```

### With default props

```jsx
<Button label="REPLACE_ME" onClick={() => {}} />
```

## Props

### Level

The `level` prop is responsible for defining the hierarchy or level of emphasis for the button.

- **Primary**
  This button is for main actions. It should only be used once per page or step.
- **Secondary**
  This button is used for secondary actions that users don’t have to take but are important enough to emphasize some actions.
- **Tertiary**
  This button is used for optional actions. It can be used multiple times on a page.

<Canvas of={ButtonStories.Level} />

### ColorScheme

For each of the three main button types, there is a primary and a danger color scheme variation.

- **Primary**
  Should be used for actions that do not require the alert of a danger color scheme button.
- **Danger**
  Should be reserved for destructive actions that cannot be recovered—delete.
- **Neutral**
  Use this for actions that are less important, especially if a primary variant is already present on the same page. See more below.

<Canvas of={ButtonStories.ColorScheme} />

#### Neutral Button Usage Guidance

Neutral (often gray or monochrome) buttons serve as **low-emphasis**, **non-disruptive actions** that are contained in a toolbar that is separate from the surrounding UI. They are not visually prioritized and are used to support — not drive — the user journey.

**Use neutral buttons when:**

- The action is **secondary, supporting, or optional** and the button is placed in a **dense or complex UI toolbar**, where emphasis should stay elsewhere.

**When not to use neutral buttons**

Avoid neutral buttons when:

- The action is **critical to task completion** (use primary).
- You want to **draw attention or guide flow** (use primary or emphasis styling).
- The button is in a **high-stakes decision point** (e.g., destructive or irreversible).

### Size

The `size` prop determines the visual size of the button and accepts two values: `sm` for small and `md` for medium.

<Canvas of={ButtonStories.Size} />

### Icons

The `startIconName` and `endIconName` props allow you to specify preset icon names as strings to be rendered at the beginning and end of the button, respectively. These icons serve as visual elements to enhance the meaning or context of the button's action.

<Canvas of={ButtonStories.Icons} />

### isLoading

The `isLoading` prop is a boolean property designed to trigger the rendering of a loading spinner in place of the regular start icon within the button when set to true. This functionality is particularly useful to visually indicate that an asynchronous operation is in progress, providing feedback to users that the button action is being processed.

<Canvas of={ButtonStories.IsLoading} />
<Canvas of={ButtonStories.AI} />

### isIconOnly

The `isIconOnly` prop is a boolean property designed to render only the start icon within the button when set to true. In this mode, the label prop value is used as the aria-label for the button, ensuring accessibility for users relying on assistive technologies.

<Canvas of={ButtonStories.IsIconOnly} />

### type

The `type` prop defines button behavior when used within a form, with values like submit (default, submits the form), or button (no default behavior), ensuring appropriate form interactions.

<Banner
    severity="primary"
    title="Note"
    body="Buttons without a type property inside of a form will default to submitting the form. Make sure to apply type='button' to any non-submit buttons in a form."
/>

### as

The `as` prop allows you to render the button as a link (`<a>`) or a button (`<button>`). This is useful when you want to use the button component as a link, for example, when navigating to another page.

### href

The `href` prop is used when the `as` prop is set to `a` to specify the URL that the button should link to.

### target

The `target` prop is used when the `as` prop is set to `a` to specify where the link should open. The default value is `_self`, which means the link will open in the same tab. If you want the link to open in a new tab, you can set the `target` prop to `_blank`.

## 🟢 When to use the component

Use this component when you need an interactive element that allows the user to trigger an action. For example:

- Submit a form
- Begin a new task
- Trigger a new UI element to appear on the page
- Specify a new or next step in a process

## ⚠️ Button vs. Link

The HTML elements for buttons and links describe specific actions that will be taken when they are used. It is important you know when to use which, as the distinction matters:

- Use a **link** when you’re **navigating to another place**, such as: a "view all" page, the profile of "Ellis Ames", a page "skip link", etc.
- Use **buttons** when you are **performing an action**, such as: "submit," "merge," "create new," "upload," etc.
- An action is almost **always** on the same page

However, there are certainly instances when the most important action for the user is a link, so presenting that link visually as Button is justified. **At this time, Button does support rendering into the browser as `<a>`, by setting the prop `as="a"` and a `href`. Optionally, `target` can be set as `_blank` to open in a new tab.**.

<Canvas
    of={ButtonStories.AsLink}
    source={{
        code: `<Button
  colorScheme="primary"
  data-id="cosmos-button"
  endIconName="LinkOut"
  id="cosmos-button-id"
  label="Learn more"
  level="primary"
  size="md"
  width="auto"
  as="a"
  href="https://drata.com"
  target="_blank"
/>`,
        language: 'jsx',
    }}
/>

## 🛠️ How it works

At a high level, the button component has three levels, two sizes, and a variety of content options.

<img src={hierarchy} alt="add control button picture" />

### **Size**

Buttons have specific sizes to ensure a user is able to target them. Medium (md) is the preferred size, but use a Button size that best fits the UI (e.g., don’t use the largest button in a table). On mobile devices, such as phones and other devices that have touch as the primary method of interaction, buttons should be the largest size to accommodate tapping with a finger instead of the more precise mouse cursor.

### **Spacing**

Buttons should be separated from other interactive elements by at least 12px, or for mobile/touch experiences at least 24px.

### Order, alignment, and grouping

While these guidelines don’t cover every scenario, they are intended to aid in the decision-making process when determining how to organize buttons and actions.

**Left alignment**

<img src={leftAlignment} alt="add control button picture" />

For most interfaces, aligning the buttons to the left when content is aligned to the left helps enforce the association between the content and the actions, and follows the natural reading order (for western-style languages). Following this principle improves accuracy and keeps the user from scanning through secondary actions first.

**Right Alignment**

<img src={rightAlignment} alt="add control button picture" />

For contained/constrained interfaces like dialogs, action or header bars (including card headers and CTA footers), multi-step interfaces, table or list rows, and some contained elements where the height is 1/5 the width or less, buttons should be right-aligned with the primary action in the right-most position. This leans on the pattern of dialogs in all major operating systems putting the terminating action in the right-most position. Secondary options should come to the left, next to the primary.

<br />

Tertiary, can be to the far left of the container on the same row as the Primary and Secondary, or for when other content is in the left position already the Tertiary can be to the left of the Secondary.

<img src={mobile} alt="add control button picture" />

**Mobile or small-width button alignment**

For mobile widths or sometimes for small-width containers, the stack should be either left aligned and wrap in order from Primary to Tertiary or use full width buttons that are stacked in the same order.

### Disabled property

We rarely use disabled buttons in Cosmos. This is a common pattern in the wild used as a blocking function when a key action, such as a required field in a form needs to be completed. Using disabled buttons can cause confusion for users—with no option to interact with them, users are forced to investigate what action they might need to take in order to proceed. Instead, by making the button interactive users can use it to trigger validation and get the inline alerts that they need to move forward.

**There are only a few exceptions for using the `disabled` property:**

- When its not possible to use the button to navigate to the next page when it is the last in page.
- To avoid double pressing while a process is taking place.

## Content Guidelines

For specific content guidelines for buttons see [Button standards ](https://www.notion.so/Button-standards-0b5614101e4d4820ab45ce9994398193?pvs=21)

## Icon usage

<img src={iconUsage} alt="add control button picture" />

Buttons are provided with flexible icon use, allowing for leading, trailing, or icon only buttons. Use icons intentionally and only when they provide the user with extra value. Do not create buttons with both leading and trailing icons.

**Start icon position**
In most cases, use leading icons. Choose icons that add meaning and clarity to the action described in the button’s text.

**End icon position**

Consider trailing icons when guiding the user forward through the product.

Use ChevronRight to indicate moving forward in a multi-step flow.

In the rare case that the primary action is a link, use link-out when using the Button for external links. In most cases, consider using a Link component instead.

**Icon-only buttons**

Do not add icons in a button for decoration. Verify that the button's purpose will still be understood using an icon without a text label. Consider your audience and the context where the button is being rendered. In most cases, you will want to include a button label even if the icon can be understood alone—if there is the space for a label, include it. If you do go with an icon-only button, use a tooltip with a text label for the button so users can hover or focus the button to read a button label that makes it clear what the button does.

Be ruthless—research underlines that icons often do not add the value that we, in the product industry, think they do. Labels always win.

<br />
<br />

#### a11yLabelOverride

The `a11yLabelOverride` prop supersedes the `label` prop for assistive technology only, it does not visually replace the `label` prop.

This usage helps improve the accessibility of the button in situations where the default label might not be sufficient or contextually relevant.

## Accessibility

The Button component is built with support for keyboard interaction and assistive technologies.

**Keyboard Support**

<table>
    <tbody>
        <tr>
            <th>Key</th>
            <th>Function</th>
        </tr>
        <tr>
            <th>
                <kbd>Tab</kbd>
            </th>
            <th>Focus forward into the button</th>
        </tr>
        <tr>
            <th>
                <kbd>Shift</kbd> + <kbd>Tab</kbd>
            </th>
            <th>Focus backwards into the button</th>
        </tr>
        <tr>
            <th>
                <kbd>Enter</kbd>
            </th>
            <th>Activates the button</th>
        </tr>
        <tr>
            <th>
                <kbd>Space</kbd>
            </th>
            <th>Activates the button</th>
        </tr>
    </tbody>
</table>

**Assistive Technology Support**

Using VoiceOver for Mac, it will explicitly explain what the user is focusing on. In the example picture below, VoiceOver reads out loud the context of what the component is and what it does.

<img
    src={buttonScreenReader}
    height="150"
    width="300"
    alt="add control button picture"
/>

The text can be changed using the `a11yLabelOverride` prop to provide better information to the user instead of the label. Example below shows a download button read on VoiceOver. The `label` prop does not tell the user what is being downloaded, so it is recommended to use `a11yLabelOverride` to explicitly tell the user what is being downloaded.

<table>
    <tbody>
        <tr>
            <th>`label` prop</th>
            <th>`a11yLabelOverride` prop</th>
        </tr>
        <tr>
            <th>
                <img
                    src={buttonDownload}
                    height="100"
                    width="250"
                    alt="download button using label prop"
                />
            </th>
            <th>
                <img
                    src={buttonDownloadA11yLabelOverride}
                    height="100"
                    width="250"
                    alt="download button using a11yLabelOverride prop"
                />
            </th>
        </tr>
    </tbody>
</table>

It is also important to have good context if the button uses the `isLoading` prop and to use the `a11yLoadingLabel` prop. If a user uses Assistive Technology Support, they may not know the button is in a loading state. The example below shows the user using their keyboard to navigate to the button, pressing enter, and the button going into a loading state and VoiceOver reading it via audio.

<img
    src={buttonLoading}
    height="150"
    width="300"
    alt="button loading picture"
/>

If setting the `isIconOnly` prop to true, this makes Assistive Technology Support even more important, because users with vision impairments will need more context when using the Button component. The examples below shows an icon only, no label button with a good and bad case. Notice the good example is explicit and tells the user the button is used to <b>Edit Control</b>.

<table>
    <tbody>
        <tr>
            <th>Bad example</th>
            <th>Good example</th>
        </tr>
        <tr>
            <th>
                <img
                    src={buttonNoLabel}
                    height="100"
                    width="250"
                    alt="bad example of button"
                />
            </th>
            <th>
                <img
                    src={buttonWithLabel}
                    height="100"
                    width="250"
                    alt="good example of button"
                />
            </th>
        </tr>
    </tbody>
</table>
