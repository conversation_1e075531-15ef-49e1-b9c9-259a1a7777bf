import { DropdownContent } from './components/dropdown-content.component';
import { DropdownGroup } from './components/dropdown-group.component';
import { DropdownItem } from './components/dropdown-item.component';
import { DropdownRoot } from './components/dropdown-root.component';
import { DropdownSubMenu } from './components/dropdown-sub-menu.component';
import { DropdownSubMenuContent } from './components/dropdown-sub-menu-content.component';
import { DropdownSubMenuTrigger } from './components/dropdown-sub-menu-trigger.component';
import { DropdownTrigger } from './components/dropdown-trigger.component';

export const Dropdown = Object.assign(DropdownRoot, {
    Content: DropdownContent,
    Item: DropdownItem,
    Trigger: DropdownTrigger,
    Group: DropdownGroup,
    SubMenu: DropdownSubMenu,
    SubMenuContent: DropdownSubMenuContent,
    SubMenuTrigger: DropdownSubMenuTrigger,
});
