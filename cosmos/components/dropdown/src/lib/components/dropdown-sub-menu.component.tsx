import {
    type DropdownMenuSubProps as RadixDropdownMenuSubProps,
    Sub as RadixDropdownSub,
} from '@radix-ui/react-dropdown-menu';

export interface DropdownSubMenuProps extends RadixDropdownMenuSubProps {
    'data-id'?: string;
    children: React.ReactNode;
    // eslint-disable-next-line jsdoc/convert-to-jsdoc-comments -- this is not a JSDoc comment intentionally, I don't want it in storybook
    /* Had to explicitly include onOpenChange in the interface because of the unbound-method lint rule  */
    onOpenChange?: RadixDropdownMenuSubProps['onOpenChange'];
}

export const DropdownSubMenu = ({
    'data-id': dataId,
    children,
    defaultOpen,
    open,
    onOpenChange,
}: DropdownSubMenuProps): JSX.Element => {
    return (
        <RadixDropdownSub
            data-id={dataId}
            data-testid="DropdownSubMenu"
            defaultOpen={defaultOpen}
            open={open}
            onOpenChange={onOpenChange}
        >
            {children}
        </RadixDropdownSub>
    );
};
