import {
    type DropdownMenuProps as RadixDropdownMenuProps,
    Root as RadixDropdownRoot,
} from '@radix-ui/react-dropdown-menu';

export interface DropdownRootProps
    extends Omit<RadixDropdownMenuProps, 'modal' | 'dir'> {
    'data-id'?: string;
}

export const DropdownRoot = ({
    children,
    'data-id': dataId = 'cosmos-dropdown',
    defaultOpen = undefined,
    open = undefined,
    onOpenChange = undefined,
}: DropdownRootProps): JSX.Element => {
    return (
        <RadixDropdownRoot
            data-id={dataId}
            data-testid="DropdownRoot"
            defaultOpen={defaultOpen}
            open={open}
            onOpenChange={onOpenChange}
        >
            {children}
        </RadixDropdownRoot>
    );
};
