import {
    ListBoxGroup,
    type ListBoxGroupProps,
} from '@cosmos/components/list-box';
import { Group as RadixDropdownGroup } from '@radix-ui/react-dropdown-menu';

export interface DropdownGroupProps extends ListBoxGroupProps {
    'data-id'?: string;
    children: React.ReactNode;
}

export const DropdownGroup = ({
    'data-id': dataId,
    children,
    groupHeader,
    showBottomDivider = false,
    showTopDivider = false,
}: DropdownGroupProps): JSX.Element => {
    return (
        <RadixDropdownGroup
            asChild
            data-id={dataId}
            data-testid="DropdownGroup"
        >
            <ListBoxGroup
                groupHeader={groupHeader}
                showBottomDivider={showBottomDivider}
                showTopDivider={showTopDivider}
            >
                {children}
            </ListBoxGroup>
        </RadixDropdownGroup>
    );
};
