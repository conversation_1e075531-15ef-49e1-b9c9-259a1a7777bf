import {
    type DropdownMenuSubTriggerProps as RadixDropdownMenuSubTriggerProps,
    SubTrigger as RadixDropdownSubTrigger,
} from '@radix-ui/react-dropdown-menu';

export interface DropdownSubMenuTriggerProps
    extends Omit<RadixDropdownMenuSubTriggerProps, 'disabled' | 'asChild'> {
    'data-id'?: string;
    children: React.ReactNode;
}

export const DropdownSubMenuTrigger = ({
    'data-id': dataId,
    children,
    textValue = undefined,
}: DropdownSubMenuTriggerProps): JSX.Element => {
    return (
        <RadixDropdownSubTrigger
            asChild
            data-id={dataId}
            data-testid="DropdownSubMenuTrigger"
            textValue={textValue}
        >
            {children}
        </RadixDropdownSubTrigger>
    );
};
