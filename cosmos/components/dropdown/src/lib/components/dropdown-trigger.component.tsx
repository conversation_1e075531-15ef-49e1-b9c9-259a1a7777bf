import {
    type DropdownMenuTriggerProps as RadixDropdownMenuTriggerProps,
    Trigger as RadixDropdownTrigger,
} from '@radix-ui/react-dropdown-menu';

interface DropdownTriggerProps
    extends Omit<RadixDropdownMenuTriggerProps, 'asChild'> {
    'data-id'?: string;
}

export const DropdownTrigger = ({
    'data-id': dataId,
    children,
}: DropdownTriggerProps): JSX.Element => {
    return (
        <RadixDropdownTrigger
            asChild
            data-testid="DropdownTrigger"
            data-id={dataId}
        >
            {children}
        </RadixDropdownTrigger>
    );
};
