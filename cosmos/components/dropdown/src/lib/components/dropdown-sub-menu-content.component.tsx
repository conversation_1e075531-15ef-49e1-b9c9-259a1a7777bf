import {
    type DropdownMenuSubContentProps as RadixDropdownMenuSubContentProps,
    SubContent as RadixDropdownSubContent,
} from '@radix-ui/react-dropdown-menu';
import { SIDE_OFFSET } from '../constants/side-offset.constant';
import styles from '../dropdown.module.css';

export interface DropdownSubMenuContent
    extends Omit<
        RadixDropdownMenuSubContentProps,
        'loop' | 'asChild' | 'sideOffset' | 'arrowPadding'
    > {
    'data-id'?: string;
    children: React.ReactNode;
}

export const DropdownSubMenuContent = ({
    'data-id': dataId,
    children,
    collisionPadding = 12,
    ...restProps
}: DropdownSubMenuContent): JSX.Element => {
    return (
        <RadixDropdownSubContent
            asChild
            loop
            collisionPadding={collisionPadding}
            data-id={dataId}
            data-testid="DropdownSubMenuContent"
            sideOffset={SIDE_OFFSET}
            {...restProps}
        >
            <ul className={styles['list-box']}>{children}</ul>
        </RadixDropdownSubContent>
    );
};
