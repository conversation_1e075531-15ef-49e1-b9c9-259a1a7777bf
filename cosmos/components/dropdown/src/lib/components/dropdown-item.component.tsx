import {
    type DropdownMenuItemProps as RadixDropdownMenuItemProps,
    Item as RadixDropdownItem,
} from '@radix-ui/react-dropdown-menu';
import styles from '../dropdown.module.css';

export interface DropdownItemProps
    extends Omit<RadixDropdownMenuItemProps, 'asChild' | 'disabled'> {
    'data-id'?: string;
    children: React.ReactNode;
}

/**
 * Example usage:.
 * ```
 * <DropdownItem data-id="item-1">
 *     <StructuredListItem label="Item 1" description="Item 1 description" />
 * </DropdownItem>.
 * ```
 *
 * As link:
 * ```
 * <DropdownItem data-id="item-1">
 *     <AppLink {...appLinkProps}><StructuredListItem label="Item 1" description="Item 1 description" /></AppLink>
 * </DropdownItem>.
 * ```
 *
 */
export const DropdownItem = ({
    'data-id': dataId,
    children,
    textValue = undefined,
    onSelect = undefined,
}: DropdownItemProps): JSX.Element => {
    return (
        <RadixDropdownItem
            asChild
            data-id={dataId}
            data-testid="DropdownItem"
            textValue={textValue}
            className={styles.item}
            onSelect={onSelect}
        >
            {children}
        </RadixDropdownItem>
    );
};
