.content {
    z-index: var(--z-index-dropdown);
    max-height: var(--radix-dropdown-menu-content-available-height);
}

.list-box {
    display: flex;
    flex-direction: column;
    gap: var(--dimension-sm);
    margin: 0;
    min-width: fit-content;
    background-color: var(--neutral-background-surface-initial);
    border: var(--border-width-sm) solid var(--neutral-border-faded);
    border-radius: var(--border-radius-md);
    box-sizing: border-box;
    box-shadow: var(--shadow-200);
    padding: var(--dimension-md);
    overflow-y: auto;
}

.item {
    all: unset;
}
