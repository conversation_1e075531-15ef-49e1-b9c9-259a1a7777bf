import { But<PERSON> } from '@cosmos/components/button';
import { ListBoxItem } from '@cosmos/components/list-box';
import type { StoryObj } from '@storybook/react-vite';
import { Dropdown } from '../../dropdown';

export default {};
type Story = StoryObj<typeof Dropdown>;

export const WithGroups: Story = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- a value is required between the parens to fix the story source, but we don't need it
    render: (_args) => (
        <Dropdown data-id="groups-dropdown">
            <Dropdown.Trigger data-id="groups-trigger">
                <Button
                    label="File Menu"
                    endIconName="ChevronDown"
                    data-id="groups-button"
                />
            </Dropdown.Trigger>
            <Dropdown.Content data-id="groups-content">
                <Dropdown.Group
                    groupHeader="File Operations"
                    data-id="file-group"
                >
                    <Dropdown.Item data-id="new-item">
                        <ListBoxItem
                            label="New File"
                            data-id="new-structured-item"
                            slot={{
                                slotType: 'icon',
                                typeProps: { name: 'Plus', size: '200' },
                            }}
                        />
                    </Dropdown.Item>
                    <Dropdown.Item data-id="open-item">
                        <ListBoxItem
                            label="Open File"
                            data-id="open-structured-item"
                            slot={{
                                slotType: 'icon',
                                typeProps: { name: 'Folder', size: '200' },
                            }}
                        />
                    </Dropdown.Item>
                </Dropdown.Group>
                <Dropdown.Group
                    showTopDivider
                    groupHeader="Edit Operations"
                    data-id="edit-group"
                >
                    <Dropdown.Item data-id="copy-item">
                        <ListBoxItem
                            label="Copy"
                            data-id="copy-structured-item"
                            slot={{
                                slotType: 'icon',
                                typeProps: { name: 'Copy', size: '200' },
                            }}
                        />
                    </Dropdown.Item>
                </Dropdown.Group>
            </Dropdown.Content>
        </Dropdown>
    ),
};
