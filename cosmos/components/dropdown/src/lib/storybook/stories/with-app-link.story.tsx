import { But<PERSON> } from '@cosmos/components/button';
import { ListBoxItem } from '@cosmos/components/list-box';
import type { StoryObj } from '@storybook/react-vite';
import { Dropdown } from '../../dropdown';

export default {};
type Story = StoryObj<typeof Dropdown>;

/* eslint-disable jsx-a11y/anchor-is-valid -- we don't want to navigate anywhere in the story, so an invalid href is preferable */

export const WithAppLink: Story = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- a value is required between the parens to fix the story source, but we don't need it
    render: (_args) => (
        <Dropdown data-id="applink-dropdown">
            <Dropdown.Trigger data-id="applink-trigger">
                <Button
                    label="Navigation"
                    endIconName="ChevronDown"
                    data-id="applink-button"
                />
            </Dropdown.Trigger>
            <Dropdown.Content data-id="applink-content">
                <Dropdown.Item data-id="dashboard-item">
                    <a href="#" data-id="dashboard-link">
                        <ListBoxItem
                            label="Dashboard"
                            data-id="dashboard-structured-item"
                            slot={{
                                slotType: 'icon',
                                typeProps: { name: 'Home', size: '200' },
                            }}
                        />
                    </a>
                </Dropdown.Item>
                <Dropdown.Item data-id="settings-item">
                    <a href="#" data-id="settings-link">
                        <ListBoxItem
                            label="Settings"
                            data-id="settings-structured-item"
                            slot={{
                                slotType: 'icon',
                                typeProps: { name: 'Settings', size: '200' },
                            }}
                        />
                    </a>
                </Dropdown.Item>
            </Dropdown.Content>
        </Dropdown>
    ),
};
