import { Button } from '@cosmos/components/button';
import { ListBoxItem } from '@cosmos/components/list-box';
import type { StoryObj } from '@storybook/react-vite';
import { Dropdown } from '../../dropdown';

export default {};
type Story = StoryObj<typeof Dropdown>;

export const Playground: Story = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- a value is required between the parens to fix the story source, but we don't need it
    render: (_args) => (
        <Dropdown data-id="playground-dropdown">
            <Dropdown.Trigger data-id="playground-trigger">
                <Button
                    label="Actions"
                    endIconName="ChevronDown"
                    data-id="playground-button"
                />
            </Dropdown.Trigger>
            <Dropdown.Content data-id="playground-content">
                <Dropdown.Item data-id="edit-item">
                    <ListBoxItem
                        label="Edit"
                        data-id="edit-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Edit', size: '200' },
                        }}
                    />
                </Dropdown.Item>
                <Dropdown.Item data-id="duplicate-item">
                    <ListBoxItem
                        label="Duplicate"
                        data-id="duplicate-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Copy', size: '200' },
                        }}
                    />
                </Dropdown.Item>
                <Dropdown.Item data-id="delete-item">
                    <ListBoxItem
                        label="Delete"
                        colorScheme="critical"
                        data-id="delete-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Trash', size: '200' },
                        }}
                    />
                </Dropdown.Item>
            </Dropdown.Content>
        </Dropdown>
    ),
};
