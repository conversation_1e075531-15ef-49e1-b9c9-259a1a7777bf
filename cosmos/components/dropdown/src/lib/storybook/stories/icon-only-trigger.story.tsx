import { But<PERSON> } from '@cosmos/components/button';
import { ListBoxItem } from '@cosmos/components/list-box';
import type { StoryObj } from '@storybook/react-vite';
import { Dropdown } from '../../dropdown';

export default {};
type Story = StoryObj<typeof Dropdown>;

export const IconOnlyTrigger: Story = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- a value is required between the parens to fix the story source, but we don't need it
    render: (_args) => (
        <Dropdown data-id="icon-dropdown">
            <Dropdown.Trigger data-id="icon-trigger">
                <Button
                    isIconOnly
                    label="More actions"
                    startIconName="HorizontalMenu"
                    level="tertiary"
                    data-id="icon-button"
                />
            </Dropdown.Trigger>
            <Dropdown.Content data-id="icon-content">
                <Dropdown.Item data-id="view-item">
                    <ListBoxItem
                        label="View Details"
                        data-id="view-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Visible', size: '200' },
                        }}
                    />
                </Dropdown.Item>
                <Dropdown.Item data-id="edit-item">
                    <ListBoxItem
                        label="Edit"
                        data-id="edit-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Edit', size: '200' },
                        }}
                    />
                </Dropdown.Item>
                <Dropdown.Item data-id="archive-item">
                    <ListBoxItem
                        label="Archive"
                        data-id="archive-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Archive', size: '200' },
                        }}
                    />
                </Dropdown.Item>
            </Dropdown.Content>
        </Dropdown>
    ),
};
