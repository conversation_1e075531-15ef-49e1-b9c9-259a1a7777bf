import { But<PERSON> } from '@cosmos/components/button';
import { ListBoxItem } from '@cosmos/components/list-box';
import type { StoryObj } from '@storybook/react-vite';
import { Dropdown } from '../../dropdown';

export default {};
type Story = StoryObj<typeof Dropdown>;

export const WithSubMenus: Story = {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars -- a value is required between the parens to fix the story source, but we don't need it
    render: (_args) => (
        <Dropdown data-id="submenu-dropdown">
            <Dropdown.Trigger data-id="submenu-trigger">
                <Button
                    label="Advanced Actions"
                    endIconName="ChevronDown"
                    data-id="submenu-button"
                />
            </Dropdown.Trigger>
            <Dropdown.Content data-id="submenu-content">
                <Dropdown.Item data-id="edit-item">
                    <ListBoxItem
                        label="Edit"
                        data-id="edit-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Edit', size: '200' },
                        }}
                    />
                </Dropdown.Item>
                <Dropdown.SubMenu data-id="share-submenu">
                    <Dropdown.SubMenuTrigger data-id="share-trigger">
                        <ListBoxItem
                            label="Share"
                            data-id="share-structured-item"
                            endIconName="ChevronRight"
                            slot={{
                                slotType: 'icon',
                                typeProps: { name: 'Send', size: '200' },
                            }}
                        />
                    </Dropdown.SubMenuTrigger>
                    <Dropdown.SubMenuContent data-id="share-content">
                        <Dropdown.Item data-id="email-item">
                            <ListBoxItem
                                label="Email"
                                data-id="email-structured-item"
                                slot={{
                                    slotType: 'icon',
                                    typeProps: { name: 'Mail', size: '200' },
                                }}
                            />
                        </Dropdown.Item>
                        <Dropdown.Item data-id="link-item">
                            <ListBoxItem
                                label="Copy Link"
                                data-id="link-structured-item"
                                slot={{
                                    slotType: 'icon',
                                    typeProps: { name: 'Link', size: '200' },
                                }}
                            />
                        </Dropdown.Item>
                    </Dropdown.SubMenuContent>
                </Dropdown.SubMenu>
                <Dropdown.Item data-id="delete-item">
                    <ListBoxItem
                        label="Delete"
                        colorScheme="critical"
                        data-id="delete-structured-item"
                        slot={{
                            slotType: 'icon',
                            typeProps: { name: 'Trash', size: '200' },
                        }}
                    />
                </Dropdown.Item>
            </Dropdown.Content>
        </Dropdown>
    ),
};
