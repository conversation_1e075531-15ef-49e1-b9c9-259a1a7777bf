import type { Meta } from '@storybook/react-vite';
import { Dropdown } from '../dropdown';

const meta: Meta<typeof Dropdown> = {
    tags: ['Stable'],
    title: 'Actions/Dropdown',
    component: Dropdown,
    parameters: {
        docs: {
            description: {
                component:
                    'A composable dropdown component built with Radix UI primitives. Use this when you need full control over dropdown structure and content.',
            },
        },
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page
export { Playground } from './stories';
export { IconOnlyTrigger } from './stories';
export { WithAppLink } from './stories';
export { WithGroups } from './stories';
export { WithSubMenus } from './stories';
