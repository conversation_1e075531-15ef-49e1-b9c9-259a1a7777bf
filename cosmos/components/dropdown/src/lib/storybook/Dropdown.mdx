import {
    Canvas,
    Controls,
    Description,
    Meta,
    Primary,
    Title,
} from '@storybook/addon-docs/blocks';
import { Banner } from '@cosmos/components/banner';
import { Link } from '@cosmos/components/link';

import * as DropdownStories from './Dropdown.stories';

<Meta of={DropdownStories} />

<Title />

<Banner
    severity="warning"
    title="Consider using SchemaDropdown instead!"
    body={<>Most use cases are better served by <Link href="/?path=/docs/actions-schemadropdown--docs" label="SchemaDropdown" />, which provides a simpler API. Only use this composable Dropdown when you need full control over structure or custom content integration.</>}
/>

*The Dropdown component provides a composable, flexible way to create dropdown menus with full control over structure and content.*

<Description />

<Primary />

<Controls of={DropdownStories.Playground} />

## Import

```jsx
import { Dropdown } from '@cosmos/components/dropdown';
```

## 🟢 When to use the composable Dropdown

- **Custom dropdown structures**: When you need full control over the dropdown layout and content
- **Complex interactions**: When dropdown items need custom behavior like navigation links or complex actions
- **Integration with other components**: When dropdown items need to wrap other components like AppLink

## ❌ When not to use the composable Dropdown

- **Simple action lists**: Use `SchemaDropdown` for straightforward lists of actions with consistent styling
- **Form inputs**: Use `SelectField` or `ComboboxField` for form-based selections
- **Data-driven menus**: Use `SchemaDropdown` when you have a simple array of items to display

## 🛠️ How it works

The Dropdown component is built using a composable pattern with dot notation exports:

- **Dropdown** - Root container that manages open/close state
- **Dropdown.Trigger** - The element that triggers the dropdown (usually a Button)
- **Dropdown.Content** - The dropdown panel container (includes Portal for positioning)
- **Dropdown.Item** - Individual dropdown items (use with StructuredListItem for consistent styling)
- **Dropdown.Group** - Groups related items with optional headers and dividers
- **Dropdown.SubMenu** - Container for nested submenus
- **Dropdown.SubMenuTrigger** - Trigger for opening a submenu
- **Dropdown.SubMenuContent** - Content container for submenu items

## Examples

### Basic Usage

<Canvas of={DropdownStories.Playground} />

### With AppLink Integration

This example shows how to integrate navigation links within dropdown items:

<Canvas of={DropdownStories.WithAppLink} />

### With Groups

Organize related items using groups with headers and dividers:

<Canvas of={DropdownStories.WithGroups} />

### With Submenus

Create nested menus for complex hierarchical actions:

<Canvas of={DropdownStories.WithSubMenus} />

### Icon-Only Trigger

Use an icon-only button as the dropdown trigger:

<Canvas of={DropdownStories.IconOnlyTrigger} />

## Accessibility

This component is built using [Radix UI Dropdown Menu](https://www.radix-ui.com/primitives/docs/components/dropdown-menu) primitives and follows the [Menu Button WAI-ARIA design pattern](https://www.w3.org/WAI/ARIA/apg/patterns/menu-button/).

### Keyboard Support

| Key | Function |
|-----|----------|
| <kbd>Space</kbd> | When focus is on trigger, opens the dropdown. When focus is on an item, activates the item. |
| <kbd>Enter</kbd> | When focus is on trigger, opens the dropdown. When focus is on an item, activates the item. |
| <kbd>ArrowDown</kbd> | When focus is on trigger, opens the dropdown. When focus is on an item, moves focus to the next item. |
| <kbd>ArrowUp</kbd> | When focus is on an item, moves focus to the previous item. |
| <kbd>ArrowRight</kbd> | When focus is on a submenu trigger, opens the submenu. |
| <kbd>ArrowLeft</kbd> | When focus is on a submenu item, closes the submenu and moves focus to the submenu trigger. |
| <kbd>Escape</kbd> | Closes the dropdown and moves focus to the trigger. |

## Comparison with SchemaDropdown

| Feature | Dropdown (Composable) | SchemaDropdown |
|---------|----------------------|----------------|
| **Flexibility** | Full control over structure | Predefined structure |
| **Custom Content** | Any React components | Limited to predefined item types |
| **AppLink Integration** | Easy with Dropdown.Item wrapper | Not supported |
| **Submenus** | Full support with Dropdown.SubMenu | Limited support |
| **Groups** | Full control with Dropdown.Group | Automatic grouping |
| **Styling** | Use StructuredListItem for consistency | Built-in styling |
| **Use Case** | Complex, custom dropdowns | Simple action lists |

## Best Practices

1. **Use StructuredListItem** for consistent styling within Dropdown.Item
2. **Provide meaningful data-id attributes** for testing and analytics
3. **Use slot prop instead of deprecated startIconName/endIconName** for icons
4. **Keep submenu depth to one level** for better usability
5. **Use AppLink wrapper** for navigation items to maintain proper routing
6. **Group related actions** using Dropdown.Group with descriptive headers
