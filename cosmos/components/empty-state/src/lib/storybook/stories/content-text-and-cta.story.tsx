import { Button } from '@cosmos/components/button';
import { Link } from '@cosmos/components/link';
import type { StoryObj } from '@storybook/react-vite';
import type { EmptyState } from '../../empty-state';

export default {};
type Story = StoryObj<typeof EmptyState>;

export const ContentTextAndCTA: Story = {
    args: {
        // Values provided here will be selected by default.
        title: 'Aspernatur dolorem cumque ea est et et dolores.',

        description:
            'Aut magnam sed est alias sint repudiandae maiores voluptatem culpa.',

        leftAction: (
            <Button
                label="[Action][object]"
                level="primary"
                colorScheme="primary"
                startIconName="Connections"
                onClick={() => {
                    alert('Continue clicked');
                }}
            />
        ),
        rightAction: (
            <Link isExternal href="https://drata.com" label="Go to Drata" />
        ),
        imageSize: undefined,
        isStacked: false,
        'data-id': 'empty-state-test-id',
        illustrationName: undefined,
    },
};
