import { Link } from '@cosmos/components/link';
import type { StoryObj } from '@storybook/react-vite';
import type { Snackbar } from '../../snackbar';

export default {};
type Story = StoryObj<typeof Snackbar>;

export const WithStyledDescription: Story = {
    args: {
        severity: 'primary',
        description: (
            <>
                Lorem ipsum dolor sit amet,{' '}
                <strong>consectetur adipiscing elit</strong>, sed do eiusmod
                tempor incididunt ut labore et dolore magna aliqua.
            </>
        ),
        title: 'Add your message here',
        link: <Link label="Link" href="#" />,

        onClose: () => {
            alert('onClose clicked');
        },
        'data-id': 'snackbar',
        closeButtonAriaLabel: 'close notification',
    },
};
