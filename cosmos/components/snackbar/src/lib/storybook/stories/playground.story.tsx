import { Link } from '@cosmos/components/link';
import type { StoryObj } from '@storybook/react-vite';
import type { Snackbar } from '../../snackbar';

export default {};
type Story = StoryObj<typeof Snackbar>;

export const Playground: Story = {
    args: {
        severity: 'primary',
        title: 'Add your message here',
        description: 'Insert your optional subtext',
        link: <Link label="Link" href="#" />,
        onClose: () => {
            alert('onClose clicked');
        },
        closeButtonAriaLabel: 'close notification',
        'data-id': 'snackbar',
    },
};
