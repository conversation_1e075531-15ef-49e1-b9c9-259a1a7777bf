import { Dropdown } from '@cosmos/components/dropdown';
import { RecursiveListItems, Trigger } from './components';
import type { SchemaDropdownProps } from './types';

/**
 * A Dropdown enables users to choose from a predefined list of options or take an action. It includes
 * a popup menu that appears when the button is clicked or activated.
 */
export const SchemaDropdown = ({
    a11yLabelOverride = undefined,
    align = 'start',
    colorScheme = 'primary',
    'data-id': dataId = 'cosmos-dropdown',
    endIconName = undefined,
    isIconOnly = false,
    items,
    label,
    level = 'primary',
    onSelectGlobalOverride = undefined,
    side = 'bottom',
    size = 'md',
    startIconName = undefined,
}: SchemaDropdownProps): React.JSX.Element => {
    return (
        <Dropdown data-id={dataId} data-testid="SchemaDropdown">
            <Dropdown.Trigger data-id={`${dataId}-trigger`}>
                <Trigger
                    a11yLabelOverride={a11yLabelOverride}
                    colorScheme={colorScheme}
                    data-id={`${dataId}-trigger-button`}
                    endIconName={endIconName}
                    isIconOnly={isIconOnly}
                    label={label}
                    level={level}
                    size={size}
                    startIconName={startIconName}
                />
            </Dropdown.Trigger>

            <Dropdown.Content
                data-id={`${dataId}-content`}
                align={align}
                side={side}
                collisionPadding={12}
                forceMountPortal={undefined}
                portalContainer={undefined}
            >
                <RecursiveListItems
                    data-id={dataId}
                    id="dropdown-menu"
                    items={items}
                    onSelectGlobalOverride={onSelectGlobalOverride}
                />
            </Dropdown.Content>
        </Dropdown>
    );
};
