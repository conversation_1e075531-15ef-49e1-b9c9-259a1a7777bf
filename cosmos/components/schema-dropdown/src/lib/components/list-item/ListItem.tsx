import { Dropdown } from '@cosmos/components/dropdown';
import { getListBoxItemDataId, ListBoxItem } from '@cosmos/components/list-box';
import type { SchemaDropdownItemData, SchemaDropdownProps } from '../../types';

export interface ItemProps {
    'data-id'?: string;
    dropdownItemData: SchemaDropdownItemData;
    onSelectGlobalOverride?: SchemaDropdownProps['onSelectGlobalOverride'];
}

export const ListItem = ({
    'data-id': dataId = '',
    dropdownItemData,
    onSelectGlobalOverride = undefined,
}: ItemProps): React.JSX.Element => {
    // eslint-disable-next-line @typescript-eslint/prefer-nullish-coalescing -- need this
    const onSelect = onSelectGlobalOverride || dropdownItemData.onSelect;

    const handleSelect = (event: Event) => {
        if (onSelect) {
            onSelect({
                id: dropdownItemData.id,
                payload: { event, ...dropdownItemData },
            });
        }
    };

    const itemDataId = getListBoxItemDataId({
        'data-id': dataId,
        itemId: dropdownItemData.id,
    });

    return (
        <Dropdown.Item
            data-testid="ListItem"
            data-id={itemDataId}
            onSelect={handleSelect}
        >
            <ListBoxItem data-id={itemDataId} {...dropdownItemData} />
        </Dropdown.Item>
    );
};
