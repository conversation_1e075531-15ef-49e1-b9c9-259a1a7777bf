import { isEmpty } from 'lodash-es';
import { Dropdown } from '@cosmos/components/dropdown';
import {
    getListBoxGroupData,
    getListBoxGroupDataId,
    getListBoxGroupId,
    getListBoxGroupItemDataId,
    getListBoxGroupItemKey,
    getListBoxItemKey,
    ListBoxGroup,
    ListBoxItem,
    type ListBoxItems,
} from '@cosmos/components/list-box';
import type {
    SchemaDropdownItemData,
    SchemaDropdownItems,
    SchemaDropdownNestedItemData,
    SchemaDropdownProps,
} from '../../types';
import { ListItem } from '../list-item';

export interface RecursiveListItemsProps {
    id: string;
    'data-id'?: string;
    items: SchemaDropdownItems;
    onSelectGlobalOverride?: SchemaDropdownProps['onSelectGlobalOverride'];
}

// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types -- need this
export function RecursiveListItems({
    id,
    'data-id': dataId = '',
    items,
    onSelectGlobalOverride,
}: RecursiveListItemsProps) {
    return items.map((item, index) => {
        switch (true) {
            case item.type === 'group': {
                const { label, items: nestedItems } =
                    item as SchemaDropdownNestedItemData;

                if (isEmpty(nestedItems)) {
                    return null;
                }

                const { showTopDivider, isLast, groupId, groupDataId } =
                    getListBoxGroupData({
                        'data-id': dataId,
                        listBoxId: id,
                        itemIndex: index,
                        groupHeader: label,
                        groupItems: nestedItems,
                        items: items as ListBoxItems,
                    });

                return (
                    <ListBoxGroup
                        data-id={groupDataId}
                        id={groupId}
                        key={groupId}
                        groupHeader={label}
                        showBottomDivider={!isLast}
                        showTopDivider={showTopDivider}
                    >
                        <RecursiveListItems
                            id={id}
                            data-id={`${groupDataId}-recursive-list-items`}
                            items={nestedItems}
                            onSelectGlobalOverride={onSelectGlobalOverride}
                        />
                    </ListBoxGroup>
                );
            }
            case item.type === 'subMenu': {
                const { label = '', items: nestedItems } =
                    item as SchemaDropdownNestedItemData;

                if (isEmpty(nestedItems)) {
                    return null;
                }

                const groupId = getListBoxGroupId({
                    id,
                    label,
                });

                const groupDataId = getListBoxGroupDataId({
                    'data-id': dataId,
                    label,
                });

                const groupItemDataId = getListBoxGroupItemDataId({
                    groupDataId,
                    groupItemId: 'sub-menu',
                });

                const groupItemKey = getListBoxGroupItemKey({
                    groupId,
                    groupItemId: 'sub-menu',
                    listBoxId: id,
                });

                return (
                    <Dropdown.SubMenu
                        key={groupItemKey}
                        data-id={groupItemDataId}
                    >
                        <Dropdown.SubMenuTrigger
                            data-id={`${groupItemDataId}-trigger`}
                        >
                            <ListBoxItem
                                data-id={`${groupItemDataId}-item`}
                                {...item}
                                endIconName="ChevronRight"
                            />
                        </Dropdown.SubMenuTrigger>

                        <Dropdown.SubMenuContent
                            data-id={`${groupItemDataId}-content`}
                        >
                            <RecursiveListItems
                                id={`${groupId}-recursive-list-items`}
                                data-id={`${groupItemDataId}-recursive-list-items`}
                                items={nestedItems}
                                onSelectGlobalOverride={onSelectGlobalOverride}
                            />
                        </Dropdown.SubMenuContent>
                    </Dropdown.SubMenu>
                );
            }
            default: {
                const itemKey = getListBoxItemKey({
                    'data-id': dataId,
                    itemId: item.id,
                });

                return (
                    <ListItem
                        key={itemKey}
                        data-id={dataId}
                        dropdownItemData={item as SchemaDropdownItemData}
                        onSelectGlobalOverride={onSelectGlobalOverride}
                    />
                );
            }
        }
    });
}
