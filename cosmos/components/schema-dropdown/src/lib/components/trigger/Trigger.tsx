import { type ForwardedRef, forwardRef, type ReactElement } from 'react';
import { Button, type ButtonProps } from '@cosmos/components/button';
import type { DropdownMenuTriggerProps } from '@radix-ui/react-dropdown-menu';

type TriggerProps = DropdownMenuTriggerProps &
    ButtonProps & { 'data-id'?: string };

const BaseTrigger = (
    props: TriggerProps,
    ref: ForwardedRef<HTMLButtonElement>,
): ReactElement => {
    return (
        // eslint-disable-next-line custom/enforce-data-id -- included in props
        <Button {...props} ref={ref} data-testid="BaseTrigger" />
    );
};

export const Trigger = forwardRef(BaseTrigger);
