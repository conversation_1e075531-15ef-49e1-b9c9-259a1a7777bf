import { describe, expect, test } from 'vitest';
import type {
    ClientTypeEnum,
    EdrAgentResponseDto,
} from '@globals/api-sdk/types';
import { buildEdrAgentOperationalStateKVP } from './buildEdrAgentOperationalStateKVP.helper';

describe('buildEdrAgentOperationalStateKVP', () => {
    describe('when client type is CROWDSTRIKE', () => {
        const clientType: ClientTypeEnum = 'CROWDSTRIKE';

        test('returns "Enabled" when operationalState is CS_PREVENTION_APPLIED', () => {
            const agent: Pick<EdrAgentResponseDto, 'operationalState'> = {
                operationalState: 'CS_PREVENTION_APPLIED',
            };

            expect(
                buildEdrAgentOperationalStateKVP(clientType, agent),
            ).toStrictEqual({
                label: 'Prevention policy',
                value: 'Enabled',
            });
        });

        test('returns "Enabled" when operationalState is "na"', () => {
            const agent: Pick<EdrAgentResponseDto, 'operationalState'> = {
                operationalState: 'na',
            };

            expect(
                buildEdrAgentOperationalStateKVP(clientType, agent),
            ).toStrictEqual({
                label: 'Prevention policy',
                value: 'Enabled',
            });
        });

        test('returns "Disabled" for any other operationalState', () => {
            const agent: Pick<EdrAgentResponseDto, 'operationalState'> = {
                operationalState: 'partially_disabled',
            };

            expect(
                buildEdrAgentOperationalStateKVP(clientType, agent),
            ).toStrictEqual({
                label: 'Prevention policy',
                value: 'Disabled',
            });
        });
    });

    describe('when client type is not CROWDSTRIKE', () => {
        const clientType: ClientTypeEnum = 'SENTINEL_ONE';

        test('returns "Healthy" when operationalState is "na"', () => {
            const agent: Pick<EdrAgentResponseDto, 'operationalState'> = {
                operationalState: 'na',
            };

            expect(
                buildEdrAgentOperationalStateKVP(clientType, agent),
            ).toStrictEqual({
                label: 'Operational state',
                value: 'Healthy',
            });
        });

        test('returns operationalState value when present', () => {
            const agent: Pick<EdrAgentResponseDto, 'operationalState'> = {
                operationalState: 'CS_PREVENTION_APPLIED',
            };

            expect(
                buildEdrAgentOperationalStateKVP(clientType, agent),
            ).toStrictEqual({
                label: 'Operational state',
                value: 'CS_PREVENTION_APPLIED',
            });
        });

        test('returns "N/A" when agent is null', () => {
            expect(
                buildEdrAgentOperationalStateKVP(clientType, null),
            ).toStrictEqual({
                label: 'Operational state',
                value: 'N/A',
            });
        });

        test('returns "N/A" when agent is undefined', () => {
            expect(
                buildEdrAgentOperationalStateKVP(clientType, undefined),
            ).toStrictEqual({
                label: 'Operational state',
                value: 'N/A',
            });
        });
    });

    test('handles undefined client type', () => {
        const agent: Pick<EdrAgentResponseDto, 'operationalState'> = {
            operationalState: 'db_corruption',
        };

        expect(
            buildEdrAgentOperationalStateKVP(undefined, agent),
        ).toStrictEqual({
            label: 'Operational state',
            value: 'db_corruption',
        });
    });
});
