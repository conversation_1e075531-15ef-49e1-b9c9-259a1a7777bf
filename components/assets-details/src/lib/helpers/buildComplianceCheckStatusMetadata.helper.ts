import type { MetadataProps } from '@cosmos/components/metadata';
import type { ComplianceCheckResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const buildComplianceCheckStatusMetadata = (
    status?: ComplianceCheckResponseDto['status'],
): Pick<MetadataProps, 'label' | 'iconName' | 'colorScheme'> => {
    switch (status) {
        case 'EXCLUDED': {
            return {
                label: t`Excluded`,
                iconName: 'OutOfScope',
                colorScheme: 'neutral',
            };
        }
        case 'PASS': {
            return {
                label: t`Compliant`,
                iconName: 'CheckCircle',
                colorScheme: 'success',
            };
        }
        case 'FAIL':
        case 'MISCONFIGURED':
        default: {
            return {
                label: t`Not compliant`,
                iconName: 'NotReady',
                colorScheme: 'critical',
            };
        }
    }
};
