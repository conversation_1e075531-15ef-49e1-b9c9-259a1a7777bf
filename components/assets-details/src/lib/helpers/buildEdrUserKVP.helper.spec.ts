import { describe, expect, test } from 'vitest';
import { buildEdrUserKVP } from './buildEdrUserKVP.helper';

describe('buildEdrUserKVP', () => {
    test('returns correct structure with agent data', () => {
        const providerName = 'TestProvider';
        const agent = {
            lastLoggedInUserName: 'john.doe',
        };

        const result = buildEdrUserKVP(providerName, agent);

        expect(result).toStrictEqual({
            label: 'TestProvider user',
            value: 'john.doe',
        });
    });

    test('returns N/A value when agent is null', () => {
        const providerName = 'TestProvider';
        const agent = null;

        const result = buildEdrUserKVP(providerName, agent);

        expect(result).toStrictEqual({
            label: 'TestProvider user',
            value: 'N/A',
        });
    });
});
