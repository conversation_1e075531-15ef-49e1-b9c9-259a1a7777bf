import { beforeAll, describe, expect, test } from 'vitest';
import { Language } from '@drata/enums';
import { i18n, sharedDirectLocaleController } from '@globals/i18n';
import { formatDate } from '@helpers/date-time';
import { buildComplianceCheckStatusDescription } from './buildComplianceCheckStatusDescription.helper';

describe('buildComplianceCheckStatusDescription', () => {
    const mockDate = '2025-03-15T10:30:00Z';

    beforeAll(() => {
        sharedDirectLocaleController.setLocale(Language.ENGLISH_US);

        i18n.load('en-US', {});
        i18n.activate('en-US');
    });

    test('returns "Indefinitely" for EXCLUDED status without end date', () => {
        const result = buildComplianceCheckStatusDescription('EXCLUDED', null);

        expect(result).toBe('Indefinitely');
    });

    test('returns formatted date for EXCLUDED status with end date', () => {
        const result = buildComplianceCheckStatusDescription(
            'EXCLUDED',
            mockDate,
        );

        expect(result).toBe(`Until ${formatDate('table', mockDate)}`);
    });

    test('returns "Last checked unknown" for PASS status without lastCheckedAt', () => {
        const result = buildComplianceCheckStatusDescription(
            'PASS',
            null,
            null,
        );

        expect(result).toBe('Last checked unknown');
    });

    test('returns formatted last checked date for PASS status', () => {
        const result = buildComplianceCheckStatusDescription(
            'PASS',
            null,
            mockDate,
        );

        expect(result).toBe(`Last checked ${formatDate('overdue', mockDate)}`);
    });

    test('returns formatted last checked date for FAIL status', () => {
        const result = buildComplianceCheckStatusDescription(
            'FAIL',
            null,
            mockDate,
        );

        expect(result).toBe(`Last checked ${formatDate('overdue', mockDate)}`);
    });

    test('returns formatted last checked date for MISCONFIGURED status', () => {
        const result = buildComplianceCheckStatusDescription(
            'MISCONFIGURED',
            null,
            mockDate,
        );

        expect(result).toBe(`Last checked ${formatDate('overdue', mockDate)}`);
    });

    test('returns "Last checked unknown" for undefined status', () => {
        const result = buildComplianceCheckStatusDescription(
            undefined,
            null,
            null,
        );

        expect(result).toBe('Last checked unknown');
    });

    test('handles undefined parameters', () => {
        const result = buildComplianceCheckStatusDescription();

        expect(result).toBe('Last checked unknown');
    });
});
