import { describe, expect, test } from 'vitest';
import type {
    ClientTypeEnum,
    EdrAgentResponseDto,
} from '@globals/api-sdk/types';
import { buildEdrAgentVersionMessage } from './buildEdrAgentVersionMessage.helper';

describe('buildEdrAgentVersionMessage', () => {
    describe('client type SENTINEL_ONE', () => {
        const clientType: ClientTypeEnum = 'SENTINEL_ONE';

        test('returns version with UPDATED status when agent is up to date', () => {
            const agent: Pick<
                EdrAgentResponseDto,
                'agentVersion' | 'isUpToDate'
            > = {
                agentVersion: '1.2.3',
                isUpToDate: true,
            };

            expect(buildEdrAgentVersionMessage(clientType, agent)).toBe(
                '1.2.3 (UPDATED)',
            );
        });

        test('returns version with NOT UPDATED status when agent is not up to date', () => {
            const agent: Pick<
                EdrAgentResponseDto,
                'agentVersion' | 'isUpToDate'
            > = {
                agentVersion: '1.2.3',
                isUpToDate: false,
            };

            expect(buildEdrAgentVersionMessage(clientType, agent)).toBe(
                '1.2.3 (NOT UPDATED)',
            );
        });
    });

    describe('client type CROWDSTRIKE', () => {
        const clientType: ClientTypeEnum = 'CROWDSTRIKE';

        test('returns version when agentVersion is provided', () => {
            const agent: Pick<
                EdrAgentResponseDto,
                'agentVersion' | 'isUpToDate'
            > = {
                agentVersion: '2.0.0',
                isUpToDate: true,
            };

            expect(buildEdrAgentVersionMessage(clientType, agent)).toBe(
                '2.0.0',
            );
        });

        test('returns N/A when agentVersion is empty string', () => {
            const agent: Pick<
                EdrAgentResponseDto,
                'agentVersion' | 'isUpToDate'
            > = {
                agentVersion: '',
                isUpToDate: true,
            };

            expect(buildEdrAgentVersionMessage(clientType, agent)).toBe('N/A');
        });
    });

    describe('edge cases', () => {
        test('returns N/A when client type is undefined', () => {
            const agent: Pick<
                EdrAgentResponseDto,
                'agentVersion' | 'isUpToDate'
            > = {
                agentVersion: '1.0.0',
                isUpToDate: true,
            };

            expect(buildEdrAgentVersionMessage(undefined, agent)).toBe('N/A');
        });

        test('returns N/A when agent is null', () => {
            expect(buildEdrAgentVersionMessage('SENTINEL_ONE', null)).toBe(
                'N/A',
            );
        });

        test('returns N/A when agent is undefined', () => {
            expect(buildEdrAgentVersionMessage('SENTINEL_ONE', undefined)).toBe(
                'N/A',
            );
        });

        test('returns N/A for unknown client type', () => {
            const unknownType = 'UNKNOWN' as ClientTypeEnum;
            const agent: Pick<
                EdrAgentResponseDto,
                'agentVersion' | 'isUpToDate'
            > = {
                agentVersion: '1.0.0',
                isUpToDate: true,
            };

            expect(buildEdrAgentVersionMessage(unknownType, agent)).toBe('N/A');
        });
    });
});
