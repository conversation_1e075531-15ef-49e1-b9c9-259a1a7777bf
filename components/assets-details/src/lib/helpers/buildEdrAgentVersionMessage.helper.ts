import { isNil } from 'lodash-es';
import type {
    ClientTypeEnum,
    EdrAgentResponseDto,
} from '@globals/api-sdk/types';

export const buildEdrAgentVersionMessage = (
    clientType?: ClientTypeEnum,
    agent?: Pick<EdrAgentResponseDto, 'agentVersion' | 'isUpToDate'> | null,
): string => {
    const { agentVersion, isUpToDate } = agent ?? {};

    switch (clientType) {
        case 'SENTINEL_ONE': {
            return isNil(agentVersion)
                ? 'N/A'
                : `${agentVersion} (${isUpToDate ? 'UPDATED' : 'NOT UPDATED'})`;
        }
        case 'CROWDSTRIKE': {
            return agentVersion || 'N/A';
        }
        default: {
            return 'N/A';
        }
    }
};
