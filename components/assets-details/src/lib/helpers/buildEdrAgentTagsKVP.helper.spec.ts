import { describe, expect, test } from 'vitest';
import { buildEdrAgentTagsKVP } from './buildEdrAgentTagsKVP.helper';

describe('buildEdrAgentTagsKVP', () => {
    describe('when tags are empty or invalid', () => {
        test('returns N/A text for undefined input', () => {
            const result = buildEdrAgentTagsKVP(undefined);

            expect(result).toStrictEqual({
                type: 'TEXT',
                value: 'N/A',
            });
        });

        test('returns N/A text for empty string', () => {
            const result = buildEdrAgentTagsKVP('');

            expect(result).toStrictEqual({
                type: 'TEXT',
                value: 'N/A',
            });
        });
    });

    describe('when tags are present', () => {
        test('formats single tag correctly', () => {
            const result = buildEdrAgentTagsKVP('[tag1]');

            expect(result).toStrictEqual({
                type: 'TAG',
                value: [{ label: 'tag1' }],
            });
        });

        test('formats multiple tags correctly', () => {
            const result = buildEdrAgentTagsKVP('[tag1],[tag2],[tag3]');

            expect(result).toStrictEqual({
                type: 'TAG',
                value: [
                    { label: 'tag1' },
                    { label: 'tag2' },
                    { label: 'tag3' },
                ],
            });
        });

        test('handles tags with spaces', () => {
            const result = buildEdrAgentTagsKVP('[tag 1],[tag 2]');

            expect(result).toStrictEqual({
                type: 'TAG',
                value: [{ label: 'tag 1' }, { label: 'tag 2' }],
            });
        });

        test('handles tags with special characters', () => {
            const result = buildEdrAgentTagsKVP('[tag-1],[tag_2],[tag@3]');

            expect(result).toStrictEqual({
                type: 'TAG',
                value: [
                    { label: 'tag-1' },
                    { label: 'tag_2' },
                    { label: 'tag@3' },
                ],
            });
        });
    });
});
