import { isEmpty, isNil } from 'lodash-es';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';

const formatEdrAgentTags = (tags = ''): string[] => {
    if (isNil(tags)) {
        return [];
    }
    // This regex matches against the squared brackets of every tag pair
    const regex = /[[\]]/g;

    return tags.replaceAll(regex, '').split(',').filter(Boolean);
};

export const buildEdrAgentTagsKVP = (
    tags = '',
): Pick<KeyValuePairProps, 'type' | 'value'> => {
    const tagsFormatted = formatEdrAgentTags(tags);

    return {
        type: isEmpty(tagsFormatted) ? 'TEXT' : 'TAG',
        value: isEmpty(tagsFormatted)
            ? 'N/A'
            : tagsFormatted.map((tag) => ({
                  label: tag,
              })),
    };
};
