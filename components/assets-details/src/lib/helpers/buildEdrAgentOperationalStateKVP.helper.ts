import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type {
    ClientTypeEnum,
    EdrAgentResponseDto,
} from '@globals/api-sdk/types';

export const buildEdrAgentOperationalStateKVP = (
    clientType?: ClientTypeEnum,
    agent?: Pick<EdrAgentResponseDto, 'operationalState'> | null,
): Pick<KeyValuePairProps, 'label' | 'value'> => {
    if (clientType === 'CROWDSTRIKE') {
        return {
            label: 'Prevention policy',
            value:
                agent?.operationalState === 'CS_PREVENTION_APPLIED' ||
                agent?.operationalState === 'na'
                    ? 'Enabled'
                    : 'Disabled',
        };
    }

    return {
        label: 'Operational state',
        value:
            agent?.operationalState === 'na'
                ? 'Healthy'
                : agent?.operationalState || 'N/A',
    };
};
