import { isNil } from 'lodash-es';
import type { ComplianceCheckResponseDto } from '@globals/api-sdk/types';
import { formatDate } from '@helpers/date-time';

export const buildComplianceCheckStatusDescription = (
    status?: ComplianceCheckResponseDto['status'],
    exclusionEndDate?: string | null,
    lastCheckedAt?: string | null,
): string => {
    switch (status) {
        case 'EXCLUDED': {
            return isNil(exclusionEndDate)
                ? 'Indefinitely'
                : `Until ${formatDate('table', exclusionEndDate)}`;
        }
        case 'PASS':
        case 'FAIL':
        case 'MISCONFIGURED':
        default: {
            return isNil(lastCheckedAt)
                ? 'Last checked unknown'
                : `Last checked ${formatDate('overdue', lastCheckedAt)}`;
        }
    }
};
