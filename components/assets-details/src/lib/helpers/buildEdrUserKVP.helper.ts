import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { EdrAgentResponseDto } from '@globals/api-sdk/types';

export const buildEdrUserKVP = (
    providerName: string,
    agent?: Pick<EdrAgentResponseDto, 'lastLoggedInUserName'> | null,
): Pick<KeyValuePairProps, 'label' | 'value'> => {
    return {
        label: `${providerName} user`,
        value: agent?.lastLoggedInUserName || 'N/A',
    };
};
