import type { ComponentProps } from 'react';
import type { ActionStack } from '@cosmos/components/action-stack';

const API_KEYS_DETAILS_PAGE_HEADER_ID = 'settings-api-keys-details-page-header';

export const ApiKeysDetailsPageHeaderActionStacks = [
    {
        actions: [
            {
                actionType: 'button',
                id: `${API_KEYS_DETAILS_PAGE_HEADER_ID}-actions-stack-btn`,
                typeProps: {
                    label: 'Revoke',
                    level: 'tertiary',
                    colorScheme: 'danger',
                },
            },
        ],
        id: `${API_KEYS_DETAILS_PAGE_HEADER_ID}-action-stacks`,
    },
] as const satisfies ComponentProps<typeof ActionStack>['stacks'];
