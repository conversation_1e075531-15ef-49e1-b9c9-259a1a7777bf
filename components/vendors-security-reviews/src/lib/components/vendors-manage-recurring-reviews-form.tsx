import {
    VENDOR_MODAL_FORM_ID,
    VENDOR_RENEWAL_SCHEDULE_OPTIONS,
} from '@components/vendor-questionnaires';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { CheckboxField } from '@cosmos/components/checkbox-field';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { DateTime } from '@cosmos-lab/components/date-time';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    calculateReviewDeadlineDate,
    calculateReviewStartDate,
} from '../helpers/calculate-review-start-date.helper';

export const VendorsManageRecurringReviewsForm = observer(
    ({
        renewalScheduleType,
        reminderToggle,
        scheduleToggle,
        contactsEmail,
        selectedQuestionnaire,
        onRenewalScheduleTypeChange,
        setReminderToggle,
        setScheduleToggle,
        onQuestionnaireChange,
        onContactEmailChange,
    }: {
        renewalScheduleType: VendorResponseDto['renewalScheduleType'];
        reminderToggle: boolean;
        scheduleToggle: boolean;
        contactsEmail: string;
        selectedQuestionnaire: object;
        onRenewalScheduleTypeChange: (
            value: NonNullable<VendorResponseDto['renewalScheduleType']>,
        ) => void;
        setReminderToggle: (value: boolean) => void;
        setScheduleToggle: (value: boolean) => void;
        onQuestionnaireChange: (questionnaire: {
            id: string;
            label: string;
            value: string;
        }) => void;
        onContactEmailChange: (email: string) => void;
    }): React.JSX.Element => {
        const { allVendorsQuestionnaires } =
            sharedVendorsTypeformQuestionnairesController;

        return (
            <Stack
                direction="column"
                gap="xl"
                data-testid="VendorsManageRecurringReviewsForm"
                data-id="bv_XjdNC"
            >
                <CheckboxField
                    formId={VENDOR_MODAL_FORM_ID}
                    aria-labelledby={undefined}
                    name="scheduleRecurringReviews"
                    label={t`Schedule recurring reviews`}
                    helpText={t`Choose how frequently you'd like to review this vendor. The vendor's status will change to "needs review" at the start of your review window.`}
                    value={reminderToggle.toString()}
                    onChange={(value) => {
                        setReminderToggle(value);
                    }}
                />

                {reminderToggle && (
                    <>
                        <Box width="100%">
                            <SelectField
                                label={t`Review frequency`}
                                options={VENDOR_RENEWAL_SCHEDULE_OPTIONS}
                                formId={VENDOR_MODAL_FORM_ID}
                                loaderLabel={t`Loading`}
                                name="reviewFrequency"
                                value={
                                    VENDOR_RENEWAL_SCHEDULE_OPTIONS.find(
                                        (opt) =>
                                            opt.value === renewalScheduleType,
                                    ) ?? undefined
                                }
                                onChange={(option) => {
                                    if (option.value) {
                                        onRenewalScheduleTypeChange(
                                            option.value as NonNullable<
                                                VendorResponseDto['renewalScheduleType']
                                            >,
                                        );
                                    }
                                }}
                            />
                        </Box>
                        <CheckboxField
                            formId={VENDOR_MODAL_FORM_ID}
                            aria-labelledby={undefined}
                            name="scheduledQuestionnaires"
                            label={t`Scheduled questionnaires`}
                            helpText={t`Automatically send questionnaires to New on the review start date.`}
                            value={scheduleToggle.toString()}
                            onChange={(value) => {
                                setScheduleToggle(value);
                            }}
                        />
                        {scheduleToggle && (
                            <>
                                <Box width="100%">
                                    <SelectField
                                        label={t`Questionnaire to send`}
                                        formId={VENDOR_MODAL_FORM_ID}
                                        loaderLabel={t`Loading`}
                                        name="questionnaireToSend"
                                        value={
                                            selectedQuestionnaire as ListBoxItemData
                                        }
                                        options={allVendorsQuestionnaires.map(
                                            (questionnaire) => ({
                                                id: questionnaire.id.toString(),
                                                label: questionnaire.title,
                                                value: questionnaire.id.toString(),
                                            }),
                                        )}
                                        onChange={(option) => {
                                            if (option.id && option.value) {
                                                onQuestionnaireChange({
                                                    id: option.id,
                                                    label: option.label,
                                                    value: option.value,
                                                });
                                            }
                                        }}
                                    />
                                </Box>

                                <Box width="100%">
                                    <TextField
                                        formId={VENDOR_MODAL_FORM_ID}
                                        name="vendorContactEmailAddress"
                                        label={t`Vendor contact email address`}
                                        value={contactsEmail}
                                        onChange={(event) => {
                                            onContactEmailChange(
                                                event.target.value,
                                            );
                                        }}
                                    />
                                </Box>
                            </>
                        )}

                        <Stack gap="xl">
                            <KeyValuePair
                                label={t`Review start date`}
                                type="REACT_NODE"
                                value={
                                    <DateTime
                                        format="table"
                                        date={calculateReviewStartDate(
                                            renewalScheduleType,
                                        )}
                                    />
                                }
                            />

                            <KeyValuePair
                                label={t`Review deadline`}
                                type="REACT_NODE"
                                value={
                                    <DateTime
                                        format="table"
                                        date={calculateReviewDeadlineDate(
                                            renewalScheduleType,
                                        )}
                                    />
                                }
                            />
                        </Stack>
                    </>
                )}
            </Stack>
        );
    },
);
