import type { ListBoxItems } from '@cosmos/components/list-box';

export const ASSET_CLASS_ENUM = {
    HARDWARE: 'HARDWARE',
    DOCUMENT: 'DOCUMENT',
    PERSONNEL: 'PERSONNEL',
    SOFTWARE: 'SOFTWARE',
    CODE: 'CODE',
    CONTAINER: 'CONTAINER',
    COMPUTE: 'COMPUTE',
    NETWORKING: 'NETWORKING',
    DATABASE: 'DATABASE',
    STORAGE: 'STORAGE',
};

export type AssetClassEnum =
    (typeof ASSET_CLASS_ENUM)[keyof typeof ASSET_CLASS_ENUM];

export const ASSET_CLASS_OPTIONS = [
    {
        id: 'code',
        label: 'Code',
        value: 'CODE',
    },
    {
        id: 'compute',
        label: 'Compute',
        value: 'COMPUTE',
    },
    {
        id: 'container',
        label: 'Container',
        value: 'CONTAINER',
    },
    {
        id: 'database',
        label: 'Database',
        value: 'DATABASE',
    },
    {
        id: 'document',
        label: 'Document',
        value: 'DOCUMENT',
    },
    {
        id: 'hardware',
        label: 'Hardware',
        value: 'HARDWARE',
    },
    {
        id: 'networking',
        label: 'Networking',
        value: 'NETWORKING',
    },
    {
        id: 'personnel',
        label: 'Personnel',
        value: 'PERSONNEL',
    },
    {
        id: 'software',
        label: 'Software',
        value: 'SOFTWARE',
    },
    {
        id: 'storage',
        label: 'Storage',
        value: 'STORAGE',
    },
] as const satisfies ListBoxItems;

export const ASSET_TYPE_OPTIONS = [
    {
        id: 'virtual',
        label: 'Virtual',
        value: 'VIRTUAL',
    },
    {
        id: 'physical',
        label: 'Physical',
        value: 'PHYSICAL',
    },
];
