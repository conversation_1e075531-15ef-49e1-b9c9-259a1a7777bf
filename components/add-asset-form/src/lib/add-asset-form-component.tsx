import { constant, isEmpty, noop } from 'lodash-es';
import { useState } from 'react';
import { Button } from '@cosmos/components/button';
import { ComboboxField } from '@cosmos/components/combobox-field';
import { Grid } from '@cosmos/components/grid';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { RadioFieldGroup } from '@cosmos/components/radio-field-group';
import { Stack } from '@cosmos/components/stack';
import { TextField } from '@cosmos/components/text-field';
import { TextareaField } from '@cosmos/components/textarea-field';
import { observer } from '@globals/mobx';
import { AssetsModel } from '@models/assets';
import {
    ASSET_CLASS_OPTIONS,
    ASSET_TYPE_OPTIONS,
} from './constants/add-asset.constants';

const FORM_ID = 'addAssetForm';
const VIEW_ID = 'addAsset';

export const AddAssetFormComponent = observer((): React.JSX.Element => {
    const { userSelect, currentUser } = new AssetsModel();
    const [notes, setNotes] = useState('');

    const handleNotesChange = (
        event: React.ChangeEvent<HTMLTextAreaElement>,
    ) => {
        setNotes(event.target.value);
    };

    const [selectedType, setSelectedType] = useState<
        ListBoxItemData | ListBoxItemData[]
    >([]);

    const handleTypeChange = (event: ListBoxItemData[] | ListBoxItemData) => {
        setSelectedType(event);
    };

    const [selectedAssetType, setSelectedAssetType] = useState<string>(
        ASSET_TYPE_OPTIONS[0].value,
    );

    const handleAssetTypeChange = (
        event: React.ChangeEvent<HTMLInputElement>,
    ) => {
        setSelectedAssetType(event.target.value);
    };

    return (
        <form
            id={FORM_ID}
            data-id={FORM_ID}
            data-testid="AddAssetFormComponent"
        >
            <Grid gap="lg" flow="row">
                <TextField
                    required
                    formId={FORM_ID}
                    name="name"
                    label="Name"
                    value={undefined}
                    data-id={`${VIEW_ID}-${FORM_ID}-nameField`}
                    onChange={noop}
                />

                <TextField
                    required
                    formId={FORM_ID}
                    name="description"
                    label="Description"
                    value={undefined}
                    data-id={`${VIEW_ID}-${FORM_ID}-descriptionField`}
                    onChange={noop}
                />

                <TextareaField
                    formId={FORM_ID}
                    name="notes"
                    label="Notes (optional)"
                    value={notes}
                    data-id={`${VIEW_ID}-${FORM_ID}-noteField`}
                    maxCharacters={192}
                    onChange={handleNotesChange}
                    onFocus={noop}
                    onKeyDown={noop}
                    onBlur={noop}
                />

                <ComboboxField
                    required
                    isMultiSelect
                    formId={FORM_ID}
                    name="class"
                    label="Class"
                    options={ASSET_CLASS_OPTIONS}
                    getSearchEmptyState={constant('No classes found')}
                    loaderLabel="Loading classes..."
                    removeAllSelectedItemsLabel="Clear all"
                    getRemoveIndividualSelectedItemClickLabel={undefined}
                    data-id={`${VIEW_ID}-${FORM_ID}-classField`}
                    onChange={handleTypeChange}
                />

                <RadioFieldGroup
                    required
                    formId={FORM_ID}
                    name="type"
                    label="Type"
                    options={ASSET_TYPE_OPTIONS}
                    cosmosUseWithCaution_forceOptionOrientation="vertical"
                    value={selectedAssetType}
                    data-id={`${VIEW_ID}-${FORM_ID}-typeField`}
                    onChange={handleAssetTypeChange}
                />

                {!isEmpty(selectedType) &&
                    (Array.isArray(selectedType)
                        ? selectedType.some(
                              (type) =>
                                  type.value === 'HARDWARE' ||
                                  type.value === 'SOFTWARE',
                          )
                        : selectedType.value === 'HARDWARE' ||
                          selectedType.value === 'SOFTWARE') && (
                        <TextField
                            formId={FORM_ID}
                            name="uniqueId"
                            label="Unique ID (optional)"
                            helpText="The unique ID should be a serial number or hardware ID"
                            value={undefined}
                            data-id={`${VIEW_ID}-${FORM_ID}-uniqueIdField`}
                            onChange={noop}
                        />
                    )}

                <ComboboxField
                    required
                    isMultiSelect
                    formId={FORM_ID}
                    defaultValue={currentUser}
                    name="owner"
                    label="Owner"
                    loaderLabel="Loading personnel..."
                    removeAllSelectedItemsLabel="Remove personnel"
                    getRemoveIndividualSelectedItemClickLabel={undefined}
                    getSearchEmptyState={constant('No personnel found')}
                    data-id={`${VIEW_ID}-${FORM_ID}-ownerField`}
                    options={userSelect}
                    onChange={noop}
                />

                <Stack gap="md">
                    <Button
                        type="submit"
                        label="Save asset"
                        colorScheme="primary"
                        data-id={`${VIEW_ID}-${FORM_ID}-submitButton`}
                    />
                    <Button
                        type="button"
                        label="Cancel"
                        level="secondary"
                        data-id={`${VIEW_ID}-${FORM_ID}-cancelButton`}
                    />
                </Stack>
            </Grid>
        </form>
    );
});
