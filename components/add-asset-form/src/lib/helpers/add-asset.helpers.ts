import type { AssetClassEnum } from '../constants/add-asset.constants';

export const getAssetClassLabel = (
    assetClass: AssetClassEnum,
): AssetClassEnum => {
    switch (assetClass) {
        case 'HARDWARE': {
            return 'Hardware';
        }
        case 'DOCUMENT': {
            return 'Document';
        }
        case 'PERSONNEL': {
            return 'Personnel';
        }
        case 'SOFTWARE': {
            return 'Software';
        }
        case 'CODE': {
            return 'Code';
        }
        case 'CONTAINER': {
            return 'Container';
        }
        case 'COMPUTE': {
            return 'Compute';
        }
        case 'NETWORKING': {
            return 'Networking';
        }
        case 'DATABASE': {
            return 'Database';
        }
        case 'STORAGE': {
            return 'Storage';
        }
        default: {
            return 'Unknown';
        }
    }
};
