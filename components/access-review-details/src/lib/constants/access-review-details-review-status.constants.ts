import type { AccessReviewPeriodApplicationUserResponseDto } from '@globals/api-sdk/types';
import { getReviewStatusLabel } from '../adaptors/access-review-personnel-details.adaptors';

export const ACCESS_REVIEW_DETAILS_REVIEW_STATUS_VALUES: AccessReviewPeriodApplicationUserResponseDto['status'][] =
    ['OUT_OF_SCOPE', 'NOT_REVIEWED', 'REJECTED', 'APPROVED'] as const;

export const ACCESS_REVIEW_DETAILS_REVIEW_STATUS_OPTIONS =
    ACCESS_REVIEW_DETAILS_REVIEW_STATUS_VALUES.map((value) => {
        return {
            id: `access-review-details-review-status-${value}`,
            label: getReviewStatusLabel(value),
            value,
        };
    });
