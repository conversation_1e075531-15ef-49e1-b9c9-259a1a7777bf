import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';

export const ReviewStatusFormField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    return (
        <>
            <Text>
                <Trans>
                    There are one or more users with unreviewed access. In order
                    to complete the review, select the default status for these
                    users.
                </Trans>
            </Text>

            <UniversalFormField
                __fromCustomRender
                formId={formId}
                name={name}
                data-id={dataId}
            />
        </>
    );
};
