import { Banner } from '@cosmos/components/banner';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';

export const AccessReviewCompletedHeaderBanner = (): React.JSX.Element => {
    return (
        <Banner
            data-id="approvers-banner"
            displayMode="section"
            severity="primary"
            title={t`You can request changes if additional revisions are needed for this application's review.`}
            data-testid="AccessReviewCompletedHeaderBanner"
            body={
                <Text colorScheme="primary">
                    <Trans>
                        Reviewers of this application will be notified.
                    </Trans>
                </Text>
            }
        />
    );
};
