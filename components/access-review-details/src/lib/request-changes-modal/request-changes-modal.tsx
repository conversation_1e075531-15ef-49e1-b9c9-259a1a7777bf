import { use<PERSON><PERSON>back, useMemo, useState } from 'react';
import {
    activeAccessReviewCompletedDetailsController,
    sharedAccessReviewCompletedRequestChangesController,
} from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { AccessReviewRequestChangesModel } from '@models/access-review-request-changes';
import { useNavigate } from '@remix-run/react';
import { type FormValues, useFormSubmit } from '@ui/forms';
import { RequestChangesForm } from './request-changes-form';

/** Unique identifier for the request changes modal. */
const MODAL_ID = 'request-changes-request-button';

/**
 * Closes the request changes modal.
 *
 * @returns Void.
 */
const handleClose = (): void => {
    modalController.closeModal(MODAL_ID);
};

/**
 * Modal component for requesting changes to an access review application.
 *
 * This modal allows reviewers to request changes to an application's review,
 * which will reset the review status back to "in progress" and notify all reviewers.
 *
 * @returns JSX element containing the modal with form for requesting changes.
 */
export const RequestChangesModal = action((): React.JSX.Element => {
    // Form management hooks
    const { formRef, triggerSubmit } = useFormSubmit();
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Navigation and workspace context
    const navigate = useNavigate();
    const requestChangesModel = useMemo(
        () => new AccessReviewRequestChangesModel(),
        [],
    );
    const workspaceId = sharedWorkspacesController.currentWorkspaceId;

    // Application details from controller
    const { applicationDetails } = activeAccessReviewCompletedDetailsController;
    const applicationId = applicationDetails?.application.id ?? 0;
    const periodId = applicationDetails?.reviewPeriod.id ?? 0;
    const reviewPeriod = applicationDetails?.reviewPeriod;

    /**
     * Closes the modal and navigates to the active review overview page.
     *
     * @returns Void.
     */
    const handleNavigation = useCallback(() => {
        handleClose();

        if (workspaceId) {
            const navigationPath =
                requestChangesModel.getNavigationPathAfterRequestChanges(
                    workspaceId,
                    applicationId,
                    periodId,
                );

            navigate(navigationPath);
        }
    }, [workspaceId, applicationId, periodId, navigate, requestChangesModel]);

    /**
     * Sets loading state and delegates to the request change controller.
     *
     * @param values - Generic form values from the form submission.
     * @returns Void.
     */
    const handleSubmit = useCallback(
        (values: FormValues) => {
            if (!reviewPeriod) {
                return;
            }

            setIsSubmitting(true);

            sharedAccessReviewCompletedRequestChangesController
                .requestChanges(
                    values.note as string,
                    applicationId,
                    periodId,
                    reviewPeriod,
                )
                .then(() => {
                    setIsSubmitting(false);
                    handleNavigation();
                })
                .catch(() => {
                    setIsSubmitting(false);
                });
        },
        [applicationId, periodId, reviewPeriod, handleNavigation],
    );

    return (
        <>
            <Modal.Header
                title={t`Request changes`}
                closeButtonAriaLabel={t`Close request changes dialog`}
                onClose={handleClose}
            />
            <Modal.Body size="md">
                <Stack gap="xl" direction="column">
                    <Text type="body">
                        <Trans>
                            Requesting changes to this application&apos;s review
                            will mean the review status will go back to &quot;in
                            progress&quot;. Provide rationale for the rejection
                            in the &quot;Explanation of changes&quot; and a
                            message will be sent to everyone listed as a
                            reviewer of this application.
                        </Trans>
                    </Text>
                    <RequestChangesForm
                        formRef={formRef}
                        onSubmit={handleSubmit}
                    />
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Cancel`,
                        level: 'secondary',
                        onClick: handleClose,
                    },
                    {
                        label: t`Confirm`,
                        isLoading: isSubmitting,
                        onClick: () => {
                            triggerSubmit().catch((error) => {
                                console.error(
                                    'Failed to submit request changes form:',
                                    error,
                                );
                                setIsSubmitting(false);
                            });
                        },
                    },
                ]}
            />
        </>
    );
});
