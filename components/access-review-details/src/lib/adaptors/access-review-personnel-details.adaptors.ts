import { isNil } from 'lodash-es';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type {
    AccessApplicationResponseDto,
    AccessReviewPeriodApplicationUserResponseDto,
    ClientTypeEnum,
    ConnectionResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const hasMfaToKVPAdaptor = (
    hasMfa?: boolean | null,
): {
    value: KeyValuePairProps['value'];
    feedbackProps?: KeyValuePairProps['feedbackProps'];
    iconName?: KeyValuePairProps['iconName'];
} => {
    const NO_MFA_INFO = t`No information provided by this platform's API.`;

    if (isNil(hasMfa)) {
        return { value: NO_MFA_INFO };
    }

    if (hasMfa) {
        return {
            value: null,
            feedbackProps: {
                title: t`Enabled`,
                severity: 'success',
            },
            iconName: 'CheckCircle',
        };
    }

    return {
        value: null,
        feedbackProps: {
            title: t`Not enabled`,
            severity: 'critical',
        },
        iconName: 'Cancel',
    };
};

export const groupsToKVPValueAdaptor = (
    groups?: string[] | null,
): KeyValuePairProps['value'] =>
    (groups ?? []).map((group) => ({ label: group }));

export const applicationDetailsAdaptor = (
    data?: {
        application?: Pick<
            AccessApplicationResponseDto,
            'name' | 'source'
        > | null;
        connection?: Pick<
            ConnectionResponseDto,
            'clientAlias' | 'clientId' | 'clientType'
        > | null;
    } | null,
    logo?: string | null,
): {
    name: string;
    alias: string;
    logo: string | null;
    clientId: string;
    clientType: ClientTypeEnum | null | undefined;
    source: AccessApplicationResponseDto['source'];
} => ({
    name: data?.application?.name || '',
    alias: data?.connection?.clientAlias || 'None',
    logo: logo ?? null,
    clientId: data?.connection?.clientId || 'None',
    clientType: data?.connection?.clientType,
    source: data?.application?.source || 'DIRECT_CONNECTION',
});

export const getReviewStatusLabel = (
    reviewStatus?:
        | AccessReviewPeriodApplicationUserResponseDto['status']
        | null,
): string => {
    switch (reviewStatus) {
        case 'APPROVED': {
            return 'Approved';
        }
        case 'REJECTED': {
            return 'Rejected';
        }
        case 'OUT_OF_SCOPE': {
            return 'Out of scope';
        }
        case 'NOT_REVIEWED':
        default: {
            return 'Not reviewed';
        }
    }
};
