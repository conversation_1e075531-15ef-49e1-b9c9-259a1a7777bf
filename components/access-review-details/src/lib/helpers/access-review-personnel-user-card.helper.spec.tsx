import { describe, expect, test, vi } from 'vitest';
import { getInitials } from '@helpers/formatters';
import {
    formatGroupsForDisplay,
    getApplicationLogoSrc,
    shouldShowPermissionLink,
} from './access-review-personnel-user-card.helper';

// Mock the providers and getInitials
vi.mock('@globals/providers', () => ({
    providers: {
        OKTA_IDENTITY: { logo: 'okta-logo.png' },
        GOOGLE_WORKSPACE: { logo: 'google-logo.png' },
    },
}));

vi.mock('@helpers/formatters', () => ({
    getInitials: vi.fn(() => `Te`),
}));

// Mock the i18n functionality to prevent Lingui errors
vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));

// Mock the Lingui core to prevent activation errors
vi.mock('@lingui/core', () => ({
    i18n: {
        _: (str: string) => str,
        activate: vi.fn(),
    },
}));

describe('access-review-personnel-user-card.helper', () => {
    describe('getApplicationLogoSrc', () => {
        test('returns logo when provided', () => {
            const result = getApplicationLogoSrc('Test App', 'logo.png');

            expect(result).toBe('logo.png');
        });

        test('returns provider logo when clientType matches a provider', () => {
            const result = getApplicationLogoSrc(
                'Test App',
                null,
                'OKTA_IDENTITY',
            );

            expect(result).toBe('okta-logo.png');
        });

        test('returns initials when no logo or matching provider', () => {
            const result = getApplicationLogoSrc('Test App');

            expect(result).toBe('Te');
            expect(getInitials).toHaveBeenCalledWith('Test App');
        });
    });

    describe('formatGroupsForDisplay', () => {
        test('returns empty array when groups is empty', () => {
            expect(formatGroupsForDisplay([])).toStrictEqual([]);
        });

        test('returns empty array when groups is not an array', () => {
            expect(formatGroupsForDisplay('not-an-array')).toStrictEqual([]);
        });

        test('returns array of groups when provided', () => {
            const groups = [{ label: 'group1' }, 'group2'];

            expect(formatGroupsForDisplay(groups)).toStrictEqual([
                { label: 'group1' },
                'group2',
            ]);
        });
    });

    describe('shouldShowPermissionLink', () => {
        test('returns true when clientType is OKTA_IDENTITY and permissionLink exists', () => {
            expect(
                shouldShowPermissionLink(
                    'OKTA_IDENTITY',
                    'https://example.com',
                ),
            ).toBeTruthy();
        });

        test('returns false when clientType is not OKTA_IDENTITY', () => {
            expect(
                shouldShowPermissionLink(
                    'GOOGLE_WORKSPACE',
                    'https://example.com',
                ),
            ).toBeFalsy();
        });

        test('returns false when permissionLink is null', () => {
            expect(shouldShowPermissionLink('OKTA_IDENTITY', null)).toBeFalsy();
        });
    });
});
