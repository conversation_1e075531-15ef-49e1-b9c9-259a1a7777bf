import { isArray, isEmpty, isNil } from 'lodash-es';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { providers } from '@globals/providers';
import { getInitials } from '@helpers/formatters';

export const getApplicationLogoSrc = (
    applicationName: string,
    logo?: string | null,
    clientType?: string,
): string => {
    if (logo) {
        return logo;
    }

    if (clientType && clientType in providers) {
        return providers[clientType as keyof typeof providers].logo;
    }

    return getInitials(applicationName);
};

export const formatGroupsForDisplay = (
    groups: KeyValuePairProps['value'],
): ({ label: string } | string)[] => {
    if (!isArray(groups) || isEmpty(groups)) {
        return [];
    }

    return groups as ({ label: string } | string)[];
};

export const shouldShowPermissionLink = (
    clientType: string,
    permissionLink: string | null,
): boolean => {
    return clientType === 'OKTA_IDENTITY' && !isNil(permissionLink);
};
