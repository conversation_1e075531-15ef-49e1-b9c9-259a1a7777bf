import { ActionStack } from '@cosmos/components/action-stack';
import { dimensionLg } from '@cosmos/constants/tokens';

export const AccessReviewHeaderActionStack = ({
    isApplicationCompleted = true,
}: {
    isApplicationCompleted: boolean | undefined;
}): React.JSX.Element => {
    return isApplicationCompleted ? (
        <ActionStack
            data-id="page-header-completed-action-stack"
            gap={dimensionLg}
            actions={[
                {
                    id: 'download-evidence-button',
                    actionType: 'button',
                    typeProps: {
                        label: 'Download evidence',
                        level: 'tertiary',
                        startIconName: 'Download',
                    },
                },
                {
                    id: 'request-changes-button',
                    actionType: 'button',
                    typeProps: {
                        label: 'Request changes',
                        level: 'secondary',
                    },
                },
            ]}
        />
    ) : (
        <ActionStack
            data-id="page-header-action-stack"
            gap={dimensionLg}
            actions={[
                {
                    id: 'sync-button',
                    actionType: 'button',
                    typeProps: {
                        label: 'Sync',
                        level: 'secondary',
                    },
                },
                {
                    id: 'complete-review-button',
                    actionType: 'button',
                    typeProps: {
                        label: 'Complete review',
                        level: 'primary',
                    },
                },
            ]}
        />
    );
};
