import { Banner } from '@cosmos/components/banner';
import { Text } from '@cosmos/components/text';
import { AppLink } from '@ui/app-link';

export const AccessReviewHeaderBanner = (): React.JSX.Element => {
    return (
        <Banner
            data-id="approvers-banner"
            displayMode="section"
            severity="primary"
            title="Ensure the accuracy of your data"
            data-testid="AccessReviewHeaderBanner"
            body={
                <>
                    <Text colorScheme="primary">
                        Read how Dr<PERSON> pulls information regarding permissions
                        and warnings as well as how to resolve them in the{' '}
                    </Text>
                    <AppLink label="help document" />
                </>
            }
        />
    );
};
