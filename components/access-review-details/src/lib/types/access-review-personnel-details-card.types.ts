import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type { ClientTypeEnum, UserResponseDto } from '@globals/api-sdk/types';
import type { IconProps } from '@radix-ui/themes';

export interface AccessReviewPersonnelDetailsCardProps {
    applicationDetails: {
        name: string;
        alias: string;
        clientId: string;
        clientType: ClientTypeEnum | null | undefined;
        logo?: string | null;
        source: string;
    };
    accountName: string;
    email?: string;
    jobTitle?: string;
    employmentStatus: string;
    groups: KeyValuePairProps['value'];
    mfa: {
        iconName: IconProps['name'] | undefined;
        value: KeyValuePairProps['value'];
        feedbackProps?: KeyValuePairProps['feedbackProps'];
    };
    permissionLink: string | null;
    rawAccessData: string;
    connection: {
        clientAlias: string;
        accountId: string;
        clientType: string;
        user?: {
            roles?: UserResponseDto['roles'];
        } | null;
    };
    roles?: string[] | UserResponseDto['roles'] | undefined;
}
