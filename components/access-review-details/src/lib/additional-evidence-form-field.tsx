import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { type CustomFieldRenderProps, UniversalFormField } from '@ui/forms';

export const AdditionalEvidenceFormField = ({
    'data-id': dataId,
    name,
    formId,
}: CustomFieldRenderProps): React.JSX.Element => {
    return (
        <Stack
            gap="4x"
            direction="column"
            data-id="cDAP6AxN"
            data-testid="AdditionalEvidenceFormField"
        >
            <Text>
                {t`We'll generate a report and use it as access review evidence. If you'd like to provide additional evidence you can add it below.`}
            </Text>

            <UniversalFormField
                __fromCustomRender
                formId={formId}
                name={name}
                data-id={dataId}
            />
        </Stack>
    );
};
