// TODO: Replace with actual data from the API
export const ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_DATA = [
    {
        id: 1,
        account: 'Acme corporation',
        owner: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Sally',
            lastName: 'Socpilot',
            jobTitle: 'Senior Director',
            avatarUrl: '',
            employmentStatus: [
                'CURRENT_EMPLOYEE',
                'FORMER_EMPLOYEE',
                'CURRENT_CONTRACTOR',
                'OUT_OF_SCOPE',
                'SERVICE_ACCOUNT',
            ],
            status: 'Current Employee',
        },
        reviewStatus: 'Not Started',
        group: 'GCP-Devops',
    },
    {
        id: 2,
        account: 'Acme corporation',
        owner: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Sally',
            lastName: 'Socpilot',
            jobTitle: 'Senior Director',
            avatarUrl: '',
            employmentStatus: ['OUT_OF_SCOPE', 'SERVICE_ACCOUNT'],
            status: 'Current Employee',
        },
        reviewStatus: 'Not Started',
        group: 'GCP-Devops',
    },
    {
        id: 3,
        account: 'Acme corporation',
        owner: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Sally',
            lastName: 'Socpilot',
            jobTitle: 'Senior Director',
            avatarUrl: '',
            employmentStatus: [
                'FORMER_CONTRACTOR',
                'OUT_OF_SCOPE',
                'SERVICE_ACCOUNT',
            ],
            status: 'Current Employee',
        },
        reviewStatus: 'Not Started',
        group: 'GCP-Devops',
    },
    {
        id: 4,
        account: 'Acme corporation',
        owner: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Sally',
            lastName: 'Socpilot',
            jobTitle: 'Senior Director',
            avatarUrl: '',
            employmentStatus: [
                'FORMER_EMPLOYEE',
                'CURRENT_CONTRACTOR',
                'OUT_OF_SCOPE',
                'SERVICE_ACCOUNT',
                'CURRENT_EMPLOYEE',
            ],
            status: 'Current Employee',
        },
        reviewStatus: 'Not Started',
        group: 'GCP-Devops',
    },
    {
        id: 5,
        account: 'Acme corporation',
        owner: {
            id: 1,
            email: '<EMAIL>',
            firstName: 'Sally',
            lastName: 'Socpilot',
            jobTitle: 'Senior Director',
            avatarUrl: '',
            employmentStatus: ['CURRENT_EMPLOYEE'],
            status: 'Current Employee',
        },
        reviewStatus: 'Not Started',
        group: 'GCP-Devops',
    },
];
