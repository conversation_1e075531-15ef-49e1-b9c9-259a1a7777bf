import { isNil } from 'lodash-es';
import type { DatatableProps } from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { AccessReviewActionCell } from '../cells/access-review-action-cell';
import { AccessReviewApplicationCell } from '../cells/access-review-application-cell';
import { AccessReviewStatusCell } from '../cells/access-review-status-cell';
import { AccessReviewWarningsCell } from '../cells/access-review-warnings-cell';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

export const getColumns =
    (): DatatableProps<AccessReviewPeriodDatatable>['columns'] =>
        [
            {
                id: 'access-review-active-action',
                size: 60,
                isActionColumn: true,
                meta: {
                    shouldIgnoreRowClick: true,
                },
                cell: AccessReviewActionCell,
            },
            {
                id: 'access-review-active-application',
                accessorFn: ({ name, logo }) => !isNil(name) || isNil(logo),
                enableSorting: false,
                header: t`Application`,
                cell: AccessReviewApplicationCell,
            },
            {
                id: 'access-review-active-status',
                accessorKey: 'statusMetadata',
                enableSorting: false,
                header: t`Review status`,
                cell: AccessReviewStatusCell,
            },
            {
                id: 'access-review-active-warnings',
                accessorKey: 'warningsMetadata',
                enableSorting: false,
                header: t`Warnings`,
                cell: AccessReviewWarningsCell,
            },
            {
                id: 'access-review-active-type',
                accessorKey: 'type',
                enableSorting: false,
                header: t`Type`,
            },
        ] as const satisfies DatatableProps<AccessReviewPeriodDatatable>['columns'];
