import type { ComponentProps } from 'react';
import type {
    Datatable,
    DatatableProps,
    FilterProps,
} from '@cosmos/components/datatable';
import { PersonnelGroupsCell } from '../cells/personnel-groups-cell';
import { PersonnelOwnerCell } from '../cells/personnel-owner-cell';
import { PersonnelReviewStatusCell } from '../cells/personnel-review-status-cell';
import {
    PERSONNEL_TABLE_REVIEW_STATUS_FILTERS,
    PERSONNEL_TABLE_STATUS_FILTERS,
    PERSONNEL_TABLE_WARNINGS_FILTERS,
} from './access-review-application-details-personnel-table.constants';
// Replace unknown with the actual type once you connect to the API
export const ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_PERIOD_TABLE_COLUMNS: DatatableProps<unknown>['columns'] =
    [
        {
            id: 'ACCOUNT',
            accessorKey: 'account',
            header: 'Account',
            enableSorting: false,
        },
        {
            accessorKey: 'PERSONNEL',
            header: 'Name',
            id: 'name',
            enableSorting: false,
            cell: PersonnelOwnerCell,
        },
        {
            id: 'REVIEW_STATUS',
            header: 'Review Status',
            accessorKey: 'reviewStatus',
            enableSorting: false,
            minSize: 334,
            cell: PersonnelReviewStatusCell,
        },
        {
            id: 'JOB_TITLE',
            header: 'Job title',
            accessorKey: 'owner.jobTitle',
            enableSorting: false,
        },
        {
            id: 'PERSONNEL_STATUS',
            header: 'Personnel status',
            accessorKey: 'owner.status',
            enableSorting: false,
        },
        {
            id: 'ROLE',
            header: 'Role',
            accessorKey: 'role',
            enableSorting: false,
            minSize: 250,
        },
        {
            header: 'Group',
            id: 'group',
            accessorKey: 'group',
            enableSorting: false,
        },
    ];
// Replace unknown with the actual type once you connect to the API
export const ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_COLUMNS: DatatableProps<unknown>['columns'] =
    [
        {
            id: 'ACCOUNT',
            accessorKey: 'account',
            header: 'Account',
            enableSorting: false,
        },
        {
            id: 'PERSONNEL',
            accessorKey: 'owner.firstName',
            header: 'Personnel',
            enableSorting: false,
            cell: PersonnelOwnerCell,
        },
        {
            id: 'JOB_TITLE',
            header: 'Job title',
            accessorKey: 'owner.jobTitle',
            enableSorting: false,
        },
        {
            id: 'EMPLOYEE_STATUS',
            header: 'Employment status',
            accessorKey: 'owner.status',
            enableSorting: false,
        },
        {
            id: 'ROLE',
            header: 'Roles',
            accessorKey: 'role',
            enableSorting: false,
            minSize: 250,
        },
        {
            header: 'Groups',
            id: 'group',
            accessorKey: 'group',
            enableSorting: false,
            cell: PersonnelGroupsCell,
        },
    ];

export const ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_PERIOD_TABLE_FILTERS: FilterProps =
    {
        clearAllButtonLabel: 'Reset',
        triggerLabel: 'Filters',
        filters: [
            {
                filterType: 'radio',
                id: 'review-status',
                label: 'Review status',
                options: PERSONNEL_TABLE_REVIEW_STATUS_FILTERS,
            },
            {
                filterType: 'radio',
                id: 'warnings-filter',
                label: 'Warnings',
                options: PERSONNEL_TABLE_WARNINGS_FILTERS,
            },
            {
                filterType: 'select',
                id: 'permissions-filter',
                label: 'Filter by connection',
                options: [
                    {
                        id: 'admin',
                        label: 'Admin',
                        value: 'admin',
                    },
                ],
            },
            {
                filterType: 'select',
                id: 'employee-status',
                label: 'Filter by employment status',
                options: PERSONNEL_TABLE_STATUS_FILTERS,
            },
            {
                filterType: 'select',
                id: 'group',
                label: 'Filter by group',
                options: PERSONNEL_TABLE_STATUS_FILTERS,
            },
        ],
    };

export const ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_FILTERS: FilterProps =
    {
        clearAllButtonLabel: 'Reset',
        triggerLabel: 'Filters',
        filters: [
            {
                filterType: 'select',
                id: 'employmentStatus',
                label: 'Personnel status',
                options: PERSONNEL_TABLE_STATUS_FILTERS,
                value: PERSONNEL_TABLE_STATUS_FILTERS.find(
                    (option) => option.id === 'ALL_CURRENT_PERSONNEL',
                ),
            },
            {
                filterType: 'radio',
                id: 'warnings-filter',
                label: 'Warnings',
                options: PERSONNEL_TABLE_WARNINGS_FILTERS,
            },
            {
                filterType: 'radio',
                id: 'permissions-filter',
                label: 'Permissions',
                options: [
                    {
                        label: 'Admin',
                        value: 'admin',
                    },
                ],
            },
            {
                filterType: 'select',
                id: 'connection',
                label: 'Filter Connection',
                options: [],
            },
            {
                filterType: 'select',
                id: 'group',
                label: 'Group',
                options: PERSONNEL_TABLE_STATUS_FILTERS,
            },
        ],
    };

export const PERSONNEL_TABLE_ACTIONS: ComponentProps<
    typeof Datatable
>['tableActions'] = [
    {
        actionType: 'dropdown',
        id: 'download',
        typeProps: {
            label: 'Settings',
            isIconOnly: true,
            side: 'left',
            align: 'start',
            colorScheme: 'neutral',
            level: 'tertiary',
            startIconName: 'Settings',
            items: [
                {
                    id: 'summary',
                    label: 'Summary',
                },
                {
                    id: 'detailed',
                    label: 'Detailed',
                },
            ],
        },
    },
];
