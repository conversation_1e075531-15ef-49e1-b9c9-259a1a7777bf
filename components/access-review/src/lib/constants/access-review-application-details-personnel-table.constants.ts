import type {
    EmploymentStatusEnum,
    ReviewPeriodApplicationAllUserStatusRequestDto,
    UserAccessReviewApplicationResponseDto,
} from '@globals/api-sdk/types';

export const USER_ROLES_LABELS = {
    ALL_CURRENT_PERSONNEL: 'All current personnel',
    ALL_PERSONNEL: 'All personnel - all time',
    CURRENT_EMPLOYEE: 'Current employee',
    FORMER_EMPLOYEE: 'Former employee',
    CURRENT_CONTRACTOR: 'Current contractor',
    FORMER_CONTRACTOR: 'Former contractor',
    OUT_OF_SCOPE: 'Out of scope',
    UNKNOWN: 'Unknown',
    SPECIAL_FORMER_EMPLOYEE: 'Special former employee',
    SPECIAL_FORMER_CONTRACTOR: 'Special former contractor',
    SERVICE_ACCOUNT: 'Service account',
    FUTURE_HIRE: 'Future hire',
} as const satisfies Record<EmploymentStatusEnum, string> & {
    ALL_CURRENT_PERSONNEL: string;
    ALL_PERSONNEL: string;
};

const PERSONNEL_TABLE_WARNINGS_FILTERS_LABELS = {
    ACCESS_LEVEL_CHANGE: 'Access level change',
    FORMER_PERSONNEL_WITH_ACCESS: 'Former personnel with access',
    MISSING_MFA: 'Missing MFA',
    UNLINKED_USERS: 'Unlinked users',
    SERVICE_ACCOUNTS: 'Service accounts',
} as const satisfies Record<
    // TODO: When you connect the API to it update it to use the response dto
    NonNullable<ReviewPeriodApplicationAllUserStatusRequestDto['warning']>,
    string
>;

const PERSONNEL_TABLE_REVIEW_STATUS_LABELS = {
    OUT_OF_SCOPE: 'Out of scope',
    NOT_REVIEWED: 'Not reviewed',
    REJECTED: 'Rejected',
    APPROVED: 'Approved',
} as const satisfies Record<
    NonNullable<
        UserAccessReviewApplicationResponseDto['applicationUserStatus']
    >,
    string
>;

export const PERSONNEL_TABLE_REVIEW_STATUS_FILTERS: {
    id: string;
    label: string;
    value: string;
}[] = Object.entries(PERSONNEL_TABLE_REVIEW_STATUS_LABELS).map(
    ([value, label]) => {
        return {
            id: value,
            label,
            value,
        };
    },
);

export const PERSONNEL_TABLE_WARNINGS_FILTERS: {
    id: string;
    label: string;
    value: string;
}[] = Object.entries(PERSONNEL_TABLE_WARNINGS_FILTERS_LABELS).map(
    ([value, label]) => {
        return {
            id: value,
            label,
            value,
        };
    },
);

export const PERSONNEL_TABLE_STATUS_FILTERS: {
    id: string;
    label: string;
    value: string;
}[] = Object.entries(USER_ROLES_LABELS).map(([value, label]) => {
    return {
        id: value,
        label,
        value,
    };
});

export const PERSONNEL_TABLE_ACTIONS = [
    [
        {
            actionType: 'dropdown',
            id: 'download',
            typeProps: {
                label: 'Settings',
                isIconOnly: true,
                side: 'left',
                align: 'start',
                colorScheme: 'neutral',
                level: 'tertiary',
                startIconName: 'Settings',
                items: [
                    {
                        id: 'summary',
                        label: 'Summary',
                    },
                    {
                        id: 'detailed',
                        label: 'Detailed',
                    },
                ],
            },
        },
    ],
];
