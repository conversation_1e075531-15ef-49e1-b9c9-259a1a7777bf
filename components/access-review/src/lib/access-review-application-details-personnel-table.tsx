import { Datatable } from '@cosmos/components/datatable';
import { ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_DATA } from './constants/access-review-application-details-personnel-table.config';
import {
    ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_PERIOD_TABLE_COLUMNS,
    ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_COLUMNS,
    ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_FILTERS,
} from './constants/access-review-application-details-personnel-table-components.constants';

interface PersonnelTableProps {
    emptyStateTitle: string;
}

export const AccessReviewApplicationDetailsPersonnelTable = ({
    emptyStateTitle,
}: PersonnelTableProps): React.JSX.Element => {
    return (
        <Datatable
            isLoading={false}
            tableId="personnel-datatable"
            data-id="personnel-datatable"
            data={ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_DATA}
            columns={ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_COLUMNS}
            data-testid="AccessReviewApplicationDetailsPersonnelTable"
            total={
                ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_PERIOD_TABLE_COLUMNS.length
            }
            emptyStateProps={{
                title: emptyStateTitle,
                description: '',
            }}
            filterProps={
                ACCESS_REVIEW_APPLICATION_DETAILS_PERSONNEL_TABLE_FILTERS
            }
            tableSettingsTriggerProps={{
                actionType: 'button',
                id: 'table-settings-trigger',
                typeProps: {
                    isIconOnly: true,
                    startIconName: 'Settings',
                    colorScheme: 'neutral',
                    align: 'start',
                    label: 'Settings',
                    level: 'tertiary',
                },
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: 'Pin filters to page',
                    toggleUnpinnedLabel: 'Move filters to dropdown',
                },
                viewMode: 'toggleable',
            }}
        />
    );
};
