import { sharedAccessReviewPeriodApplicationUserController } from '@controllers/access-reviews';
import {
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { ApplicationDataRowCellProps } from '../types/access-review-cell.types';

export const AccessReviewApplicationActionEvidenceCell = observer(
    ({ row: { original } }: ApplicationDataRowCellProps): React.JSX.Element => {
        const { id } = original;
        const reviewPeriodId =
            'reviewPeriodId' in original ? original.reviewPeriodId : id;

        const { downloadEvidencePackage } =
            sharedAccessReviewPeriodApplicationUserController;

        const items: SchemaDropdownItems = [
            {
                id: `${id}-download-evidence`,
                label: t`Download evidence`,
                type: 'item',
                onClick: () => {
                    downloadEvidencePackage({
                        periodId: reviewPeriodId ?? id,
                        reviewAppId: id,
                    });
                },
            },
        ];

        return (
            <SchemaDropdown
                isIconOnly
                startIconName="HorizontalMenu"
                level="tertiary"
                label={t`Action menu`}
                colorScheme="neutral"
                items={items}
                data-testid="AccessReviewApplicationActionEvidenceCell"
                data-id="6GvBWQ_R-evidence"
            />
        );
    },
);
