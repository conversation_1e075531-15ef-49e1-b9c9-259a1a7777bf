import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';

export const FileNameCell = ({
    row: { original },
}: {
    row: { original: AccessReviewPeriodApplicationEvidenceResponseDto };
}): React.JSX.Element => {
    return (
        <Stack
            align="center"
            data-testid="FileNameCell"
            data-id="_5Hy0itY"
            height="100%"
        >
            <Text>{original.name}</Text>
        </Stack>
    );
};
