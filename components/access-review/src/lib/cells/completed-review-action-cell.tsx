import {
    sharedAccessReviewCompletedRequestChangesController,
    sharedAccessReviewPeriodApplicationUserController,
} from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type {
    AccessReviewPeriodApplicationResponseDto,
    AccessReviewPeriodListResponseDto,
    UpdateReviewPeriodResponseDto,
} from '@globals/api-sdk/types';
import { observer } from '@globals/mobx';
import { AccessReviewCompletedActionCellModel } from '@models/access-review-completed-action-cell';
import { RequestChangesCompletedModal } from '../request-changes-completed-modal';

const actionCellModel = new AccessReviewCompletedActionCellModel();

interface CompletedReviewActionCellProps {
    row: {
        original: AccessReviewPeriodApplicationResponseDto;
    };
    /**
     * Additional context data passed from the parent component.
     */
    periodData?: {
        id: number;
        startDate: string;
        endDate: string;
        completedAt?: string;
    };
}

export const CompletedReviewActionCell = observer(
    ({
        row: { original },
        periodData,
    }: CompletedReviewActionCellProps): React.JSX.Element => {
        const { id, reviewPeriodId } = original;

        const { downloadEvidencePackage } =
            sharedAccessReviewPeriodApplicationUserController;
        const {
            activePeriodsData,
        }: { activePeriodsData: AccessReviewPeriodListResponseDto | null } =
            sharedAccessReviewCompletedRequestChangesController;

        // Create review period object using model
        const reviewPeriod: UpdateReviewPeriodResponseDto | undefined =
            actionCellModel.createReviewPeriodFromData(periodData);
        const periodId = actionCellModel.getPeriodId(original, periodData);
        const modalId = actionCellModel.generateRequestChangesModalId();

        const handleDownloadEvidence = () => {
            downloadEvidencePackage({
                periodId: reviewPeriodId ?? id,
                reviewAppId: id,
            });
        };

        const handleRequestChanges = () => {
            const {
                id: configId,
                modalProps,
                centered,
                disableClickOutsideToClose,
                size,
            } = actionCellModel.createRequestChangesModalConfig(
                modalId,
                periodId,
                reviewPeriod,
            );

            modalController.openModal({
                id: configId,
                content: () => (
                    <RequestChangesCompletedModal
                        {...modalProps}
                        data-id="lt2j_lUJ"
                    />
                ),
                centered,
                disableClickOutsideToClose,
                size,
            });
        };

        // Create dropdown items using model
        const items = actionCellModel.createDropdownItems(
            original,
            reviewPeriod,
            activePeriodsData,
            handleDownloadEvidence,
            handleRequestChanges,
        );

        return (
            <SchemaDropdown
                isIconOnly
                startIconName="HorizontalMenu"
                level="tertiary"
                label="Action menu"
                colorScheme="neutral"
                items={items}
                data-testid="CompletedReviewActionCell"
                data-id="completed-review-action-cell"
            />
        );
    },
);
