import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import { getInitials } from '@helpers/formatters';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

export const AccessReviewApplicationCell = ({
    row: {
        original: { name, logo },
    },
}: {
    row: { original: Pick<AccessReviewPeriodDatatable, 'name' | 'logo'> };
}): React.JSX.Element => (
    <OrganizationIdentity
        data-testid="AccessReviewApplicationCell"
        primaryLabel={name}
        imgSrc={logo}
        fallbackText={getInitials(name)}
        data-id="fLg5l424"
    />
);
