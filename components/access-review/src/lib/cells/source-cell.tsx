import { isNil } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { providers } from '@globals/providers';
import {
    ACCESS_REVIEW_APPLICATION_SOURCE,
    ACCESS_REVIEW_APPLICATION_SOURCE_LABEL,
} from '../constants/access-review-cell.constant';
import type { ApplicationDataRowCellProps } from '../types/access-review-cell.types';

export const AccessReviewApplicationSourceCell = ({
    row: { original },
}: ApplicationDataRowCellProps): React.JSX.Element => {
    const { source, clientType } = original;

    if (source === ACCESS_REVIEW_APPLICATION_SOURCE.PARTNER_CONNECTION) {
        const provider = Object.values(providers).find(
            (p) => p.id === clientType,
        );

        if (isNil(provider)) {
            return (
                <Text>{ACCESS_REVIEW_APPLICATION_SOURCE_LABEL[source]}</Text>
            );
        }

        return <Text>{`${provider.name} connection`}</Text>;
    }

    return (
        <Text
            data-testid="AccessReviewApplicationSourceCell"
            data-id="qksTqi2z"
        >
            {ACCESS_REVIEW_APPLICATION_SOURCE_LABEL[source]}
        </Text>
    );
};
