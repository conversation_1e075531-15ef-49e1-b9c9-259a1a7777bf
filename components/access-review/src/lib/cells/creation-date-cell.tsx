import { isNil } from 'lodash-es';
import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';

export const CreationDateCell = ({
    row: { original },
}: {
    row: { original: AccessReviewPeriodApplicationEvidenceResponseDto };
}): React.JSX.Element => {
    if (isNil(original.currentVersion?.createdAt)) {
        return <EmptyValue label="No creation date" />;
    }

    const date = original.currentVersion.createdAt as string;

    return (
        <Stack
            align="center"
            data-testid="CreationDateCell"
            data-id="_8rT0iZS"
            height="100%"
        >
            <DateTime
                date={new Date(date)}
                format="table"
                data-testid="CreationDateCell"
            />
        </Stack>
    );
};
