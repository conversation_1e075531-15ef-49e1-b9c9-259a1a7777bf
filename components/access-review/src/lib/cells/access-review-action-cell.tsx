import { isNil } from 'lodash-es';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

export const AccessReviewActionCell = ({
    row: { original },
}: {
    row: {
        original: Pick<AccessReviewPeriodDatatable, 'actions'>;
    };
}): React.JSX.Element => {
    const actionConfig = original.actions;

    if (isNil(actionConfig)) {
        return <div />;
    }

    return (
        <SchemaDropdown
            isIconOnly
            size="sm"
            startIconName="HorizontalMenu"
            level="tertiary"
            label={t`Horizontal menu`}
            colorScheme={actionConfig.colorScheme}
            items={actionConfig.items}
            data-testid="AccessReviewActionCell"
            data-id="6GvBWQ_R"
        />
    );
};
