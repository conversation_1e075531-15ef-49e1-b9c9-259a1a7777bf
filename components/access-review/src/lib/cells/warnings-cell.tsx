import { isNil } from 'lodash-es';
import { Metadata } from '@cosmos/components/metadata';
import type { ApplicationDataRowCellProps } from '../types/access-review-cell.types';

export const AccessReviewApplicationWarningsCell = ({
    row: { original },
}: ApplicationDataRowCellProps): React.JSX.Element => {
    const { totalWarnings, hasFailed } = original;
    const MINIMUM_ERROR_VALUE = 1;

    if (hasFailed) {
        return (
            <Metadata
                iconName="WarningDiamond"
                colorScheme="critical"
                label="Error"
                type="status"
                data-testid="AccessReviewApplicationWarningsCell"
            />
        );
    }

    if (isNil(totalWarnings) || totalWarnings < MINIMUM_ERROR_VALUE) {
        return (
            <div
                data-id="warnings-column"
                data-testid="AccessReviewApplicationWarningsCell"
            >
                -
            </div>
        );
    }

    return (
        <Metadata
            iconName="WarningTriangle"
            colorScheme="warning"
            label={`${totalWarnings} ${totalWarnings >= MINIMUM_ERROR_VALUE ? 'warnings' : 'warning'}`}
            type="status"
            data-testid="AccessReviewApplicationWarningsCell"
            data-id="CVLFnlgW"
        />
    );
};
