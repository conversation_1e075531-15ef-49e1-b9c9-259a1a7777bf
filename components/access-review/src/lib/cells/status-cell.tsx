import { Metadata } from '@cosmos/components/metadata';
import { t } from '@globals/i18n/macro';

export const AccessReviewApplicationStatusCell = (): React.JSX.Element => {
    return (
        <Metadata
            iconName="CheckCircle"
            colorScheme="success"
            label={t`Completed`}
            type="status"
            data-testid="AccessReviewApplicationStatusCell"
            data-id="AvZLxt91"
        />
    );
};
