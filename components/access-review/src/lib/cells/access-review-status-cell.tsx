import { Metadata } from '@cosmos/components/metadata';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

export const AccessReviewStatusCell = ({
    row: { original },
}: {
    row: {
        original: Pick<AccessReviewPeriodDatatable, 'statusMetadata'>;
    };
}): React.JSX.Element => {
    const metadata = original.statusMetadata;

    return (
        <Metadata
            label={metadata.label}
            type="status"
            colorScheme={metadata.colorScheme}
            iconName={metadata.iconName}
            data-testid="AccessReviewStatusCell"
            data-id="MfU7AjCf"
        />
    );
};
