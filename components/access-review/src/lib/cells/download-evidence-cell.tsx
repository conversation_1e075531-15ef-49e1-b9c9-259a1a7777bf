import { activeAccessReviewCompletedDetailsController } from '@controllers/access-reviews';
import { Button } from '@cosmos/components/button';
import { Stack } from '@cosmos/components/stack';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';

export const DownloadEvidenceCell = action(
    ({
        row: { original },
    }: {
        row: { original: AccessReviewPeriodApplicationEvidenceResponseDto };
    }): React.JSX.Element => {
        const { downloadLinkedEvidence, applicationDetails } =
            activeAccessReviewCompletedDetailsController;

        const handleOnclickDownload = () => {
            downloadLinkedEvidence({
                periodId: applicationDetails?.reviewPeriod.id,
                reviewAppId: applicationDetails?.application.id,
                evidenceId: original.id,
            });
        };

        return (
            <Stack justify="end" data-id="wmEnfGp2">
                <Button
                    isIconOnly
                    label={t`Download evidence`}
                    startIconName="Download"
                    level="tertiary"
                    colorScheme="neutral"
                    data-testid="DownloadEvidenceCell"
                    data-id="lld5PZFp"
                    onClick={handleOnclickDownload}
                />
            </Stack>
        );
    },
);
