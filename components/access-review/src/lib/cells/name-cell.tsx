import { useMemo } from 'react';
import drataLogo from '@assets/img/svg/drata-icon-blue-logo.svg';
import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import { providers } from '@globals/providers';
import { getInitials } from '@helpers/formatters';
import type { ApplicationDataRowCellProps } from '../types/access-review-cell.types';

export const AccessReviewApplicationNameCell = ({
    row: { original },
}: ApplicationDataRowCellProps): React.JSX.Element => {
    const logoSrc = useMemo(() => {
        const { logo, source, clientType } = original;

        if (logo) {
            return logo;
        }

        switch (source) {
            case 'DIRECT_CONNECTION': {
                return clientType && clientType in providers
                    ? providers[clientType as keyof typeof providers].logo
                    : providers.CUSTOM.logo;
            }

            case 'PARTNER_CONNECTION': {
                if (clientType === 'MICROSOFT_365') {
                    return logo ?? undefined;
                }

                return drataLogo;
            }

            case 'MANUALLY_ADDED': {
                return drataLogo;
            }

            default: {
                return providers.CUSTOM.logo;
            }
        }
    }, [original]);

    return (
        <OrganizationIdentity
            data-id={`${original.name}-name-column`}
            data-testid="AccessReviewApplicationNameCell"
            primaryLabel={original.name}
            imgSrc={logoSrc}
            fallbackText={getInitials(original.name)}
        />
    );
};
