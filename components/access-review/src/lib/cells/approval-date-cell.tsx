import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';

export const ApprovalDateCell = ({
    row: { original },
}: {
    row: { original: AccessReviewPeriodApplicationEvidenceResponseDto };
}): React.JSX.Element => {
    return (
        <Stack
            align="center"
            data-testid="ApprovalDateCell"
            data-id="_8rT0itY"
            height="100%"
        >
            <DateTime
                date={original.approvedAt}
                format="table"
                data-testid="ApprovalDateCell"
            />
        </Stack>
    );
};
