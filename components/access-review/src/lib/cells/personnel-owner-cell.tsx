import { AvatarIdentity } from '@cosmos-lab/components/identity';

export const PersonnelOwnerCell = (): React.JSX.Element => {
    return (
        // OrganizationIdentity and AvatarIdentity are presets of Identity. Exchange if necessary.
        <AvatarIdentity
            primaryLabel="<PERSON>"
            secondaryLabel="Software Engineer"
            tertiaryLabel="<EMAIL>"
            fallbackText="EJ"
            imgSrc="https://images.pexels.com/photos/762020/pexels-photo-762020.jpeg?auto=compress&cs=tinysrgb&w=600"
            data-testid="PersonnelOwnerCell"
            data-id="OBQoq0mC"
        />
    );
};
