import { isNil } from 'lodash-es';
import { Metadata } from '@cosmos/components/metadata';
import type { AccessReviewPeriodDatatable } from '../types/access-review-data-table.types';

export const AccessReviewWarningsCell = ({
    row: { original },
}: {
    row: {
        original: Pick<AccessReviewPeriodDatatable, 'warningsMetadata'>;
    };
}): React.JSX.Element => {
    const metadata = original.warningsMetadata;

    if (isNil(metadata)) {
        return (
            <div data-id="9TaDSZsP" data-testid="AccessReviewWarningsCell">
                -
            </div>
        );
    }

    return (
        <Metadata
            label={metadata.label}
            type="status"
            colorScheme={metadata.colorScheme}
            iconName={metadata.iconName}
            data-testid="AccessReviewWarningsCell"
            data-id="9TaDSZsP"
        />
    );
};
