import { isEmpty, noop } from 'lodash-es';
import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import {
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { useNavigate } from '@remix-run/react';
import type { ApplicationDataRowCellProps } from '../types/access-review-cell.types';

export const AccessReviewApplicationActionCell = observer(
    ({ row: { original } }: ApplicationDataRowCellProps): React.JSX.Element => {
        const { id, hasFailed, source } = original;
        const navigate = useNavigate();
        const { hasLimitedAccess } = sharedFeatureAccessModel;
        const { currentWorkspaceId } = sharedWorkspacesController;

        const items: SchemaDropdownItems = [];

        if (hasFailed) {
            if (hasLimitedAccess) {
                items.push({
                    id: `${id}-contact-administrator`,
                    label: t`Contact your administrator to review the issue`,
                    type: 'item',
                    onClick: () => noop,
                });
            } else {
                items.push({
                    id: `${id}-fix-connections`,
                    label: t`Fix in Connections`,
                    type: 'item',
                    onClick: () => {
                        navigate(
                            `/workspaces/${currentWorkspaceId}/connections/all/active`,
                        );
                    },
                });
            }
        }

        if (source === 'MANUALLY_ADDED') {
            if (hasLimitedAccess) {
                return <div />;
            }

            items.push({
                id: `${id}-delete-application`,
                label: t`Remove application`,
                type: 'item',
                colorScheme: 'critical',
                onClick: () => {
                    activeAccessReviewsApplicationsController.deleteApplication(
                        id,
                    );
                },
            });
        }
        if (isEmpty(items)) {
            return <div />;
        }

        return (
            <SchemaDropdown
                isIconOnly
                startIconName="HorizontalMenu"
                level="tertiary"
                label={t`Horizontal menu`}
                colorScheme={hasFailed ? 'danger' : 'neutral'}
                items={items}
                data-testid="AccessReviewApplicationActionCell"
                data-id="6GvBWQ_R"
            />
        );
    },
);
