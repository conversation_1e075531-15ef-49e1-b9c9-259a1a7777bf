import { noop } from 'lodash-es';
import { ToggleGroup } from '@cosmos/components/toggle-group';
import { t } from '@globals/i18n/macro';

export const PersonnelReviewStatusCell = (): React.JSX.Element => {
    return (
        <ToggleGroup
            orientation="horizontal"
            data-id="framework-readiness-toggle"
            data-testid="PersonnelReviewStatusCell"
            options={[
                {
                    label: t`Approved`,
                    value: 'approved',
                },
                {
                    label: t`Rejected`,
                    value: 'rejected',
                },
                {
                    label: t`Out of scope`,
                    value: 'out',
                },
                {
                    label: t`Not reviewed`,
                    value: 'not',
                },
            ]}
            onChange={noop}
        />
    );
};
