import '@globals/i18n'; // Import to ensure i18n is initialized
import { describe, expect, test } from 'vitest';
import type { AccessReviewApplicationWithProvider } from '@controllers/access-reviews';
import type {
    AccessReviewApplicationToDatatableAdapter,
    BuildAccessReviewApplicationActions,
} from '../types/access-review-data-table.types';
import { accessReviewApplicationToDatatableAdaptor } from './access-review-data-table.adaptor';

const mockBuildActionsFn: BuildAccessReviewApplicationActions = () => ({
    items: [],
    colorScheme: 'neutral',
});

describe('access-review-active-data-table helpers', () => {
    describe('accessReviewApplicationToDatatableAdaptor', () => {
        const mockApplication: AccessReviewApplicationToDatatableAdapter = {
            id: 123,
            reviewPeriodId: 1,
            name: 'Test App',
            logo: 'test-logo.png',
            source: 'PARTNER_CONNECTION',
            provider: {
                id: 'AWS',
                name: 'Test Provider',
                logo: 'test-provider-logo.png',
                companyName: 'Test Provider Company',
                companyUrl: 'company-url',
                description: 'Test Provider Description',
                providerTypes: ['USER_ACCESS_REVIEW'],
            },
            totalWarnings: 0,
            hasFailed: false,
            status: 'IN_PROGRESS',
        };

        test('should correctly transform application data', () => {
            const result = accessReviewApplicationToDatatableAdaptor(
                [mockApplication],
                mockBuildActionsFn,
            );

            expect(result).toHaveLength(1);
            expect(result[0]).toStrictEqual({
                id: 123,
                reviewPeriodId: 1,
                name: 'Test App',
                logo: 'test-logo.png',
                type: 'Test Provider connection',
                warningsMetadata: null,
                statusMetadata: {
                    label: 'In progress',
                    colorScheme: 'neutral',
                    iconName: 'InProgress',
                },
                hasFailed: false,
                status: 'IN_PROGRESS',
                source: 'PARTNER_CONNECTION',
                actions: {
                    items: [],
                    colorScheme: 'neutral',
                },
            });
        });

        test('should handle warnings metadata for failed status', () => {
            const failedApp = { ...mockApplication, hasFailed: true };
            const result = accessReviewApplicationToDatatableAdaptor(
                [failedApp],
                mockBuildActionsFn,
            );

            expect(result[0].warningsMetadata).toStrictEqual({
                label: 'Error',
                colorScheme: 'critical',
                iconName: 'WarningDiamond',
            });
        });

        test('should handle warnings metadata for warnings count', () => {
            const appWithWarnings = { ...mockApplication, totalWarnings: 2 };
            const result = accessReviewApplicationToDatatableAdaptor(
                [appWithWarnings],
                mockBuildActionsFn,
            );

            expect(result[0].warningsMetadata).toStrictEqual({
                label: '2 warnings',
                colorScheme: 'warning',
                iconName: 'WarningTriangle',
            });
        });

        test('should handle single warning case', () => {
            const appWithOneWarning = { ...mockApplication, totalWarnings: 1 };
            const result = accessReviewApplicationToDatatableAdaptor(
                [appWithOneWarning],
                mockBuildActionsFn,
            );

            expect(result[0].warningsMetadata).toStrictEqual({
                label: '1 warning',
                colorScheme: 'warning',
                iconName: 'WarningTriangle',
            });
        });

        test('should handle application source without provider', () => {
            const appWithoutProvider = {
                ...mockApplication,
                provider: null,
                source: 'PARTNER_CONNECTION',
            } as AccessReviewApplicationWithProvider;
            const result = accessReviewApplicationToDatatableAdaptor(
                [appWithoutProvider],
                mockBuildActionsFn,
            );

            expect(result[0].type).toBe('Partner connection');
        });
    });
});
