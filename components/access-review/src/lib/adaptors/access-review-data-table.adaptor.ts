import { isNil } from 'lodash-es';
import type { MetadataProps } from '@cosmos/components/metadata';
import { t } from '@globals/i18n/macro';
import { providers } from '@globals/providers';
import { MINIMUM_WARNINGS_VALUE } from '../constants/access-review-data-table.constants';
import type {
    AccessReviewApplicationToDatatableAdapter,
    AccessReviewPeriodDatatable,
    BuildAccessReviewApplicationActions,
} from '../types/access-review-data-table.types';

const getAccessReviewWarningsMetadata = ({
    totalWarnings,
    hasFailed,
}: Pick<
    AccessReviewApplicationToDatatableAdapter,
    'totalWarnings' | 'hasFailed'
>): MetadataProps | null => {
    if (hasFailed) {
        return {
            label: t`Error`,
            colorScheme: 'critical',
            iconName: 'WarningDiamond',
        };
    }

    const hasWarnings =
        !isNil(totalWarnings) && totalWarnings >= MINIMUM_WARNINGS_VALUE;

    if (hasWarnings) {
        const warningText =
            totalWarnings > MINIMUM_WARNINGS_VALUE ? t`warnings` : t`warning`;

        return {
            label: `${totalWarnings} ${warningText}`,
            colorScheme: 'warning',
            iconName: 'WarningTriangle',
        };
    }

    return null;
};

const getApplicationSourceLabel = ({
    source,
    provider,
}: Pick<
    AccessReviewApplicationToDatatableAdapter,
    'source' | 'provider'
>): string => {
    if (source === 'PARTNER_CONNECTION' && !isNil(provider)) {
        const providerName = provider.name;

        return t`${providerName} connection`;
    }

    switch (source) {
        case 'DIRECT_CONNECTION': {
            return t`Direct connection`;
        }
        case 'MANUALLY_ADDED': {
            return t`Manually added`;
        }
        case 'PARTNER_CONNECTION': {
            return t`Partner connection`;
        }
        case 'PARTNER_PUBLIC_CONNECTION':
        default: {
            return '-';
        }
    }
};

const getApplicationLogo = ({
    logo,
    source,
    provider,
}: Pick<
    AccessReviewApplicationToDatatableAdapter,
    'logo' | 'source' | 'provider'
>): string => {
    // First priority: use the application's own logo if available
    if (logo) {
        return logo;
    }

    // Second priority: for partner/direct connections, use provider logo
    if (
        (source === 'PARTNER_CONNECTION' || source === 'DIRECT_CONNECTION') &&
        !isNil(provider)
    ) {
        return provider.logo;
    }

    // Third priority: for manually added applications, use CUSTOM provider logo
    if (source === 'MANUALLY_ADDED') {
        return providers.CUSTOM.logo;
    }

    // Fallback: empty string (will use fallbackText in component)
    return '';
};

const getStatusMetadata = ({
    status,
}: Pick<
    AccessReviewApplicationToDatatableAdapter,
    'status'
>): MetadataProps => {
    switch (status) {
        case 'COMPLETED': {
            return {
                label: t`Completed`,
                colorScheme: 'success',
                iconName: 'CheckCircle',
            };
        }
        case 'IN_PROGRESS': {
            return {
                label: t`In progress`,
                colorScheme: 'neutral',
                iconName: 'InProgress',
            };
        }
        case 'OVERDUE': {
            return {
                label: t`Overdue`,
                colorScheme: 'critical',
                iconName: 'WarningDiamond',
            };
        }
        case 'PREPARING': {
            return {
                label: t`Syncing`,
                colorScheme: 'neutral',
                iconName: 'Sync',
            };
        }
        case 'NEEDS_REVISION': {
            return {
                colorScheme: 'warning',
                iconName: 'WarningTriangle',
                label: t`Needs review`,
            };
        }
        case 'DELETED': {
            return {
                label: t`Deleted`,
                colorScheme: 'neutral',
                iconName: 'Cancel',
            };
        }
        case 'NOT_STARTED':
        default: {
            return {
                label: t`Not started`,
                colorScheme: 'neutral',
                iconName: 'NotStarted',
            };
        }
    }
};

export const accessReviewApplicationToDatatableAdaptor = (
    applications: AccessReviewApplicationToDatatableAdapter[],
    buildActions: BuildAccessReviewApplicationActions,
): AccessReviewPeriodDatatable[] => {
    return applications.map((application) => ({
        id: application.id,
        reviewPeriodId: application.reviewPeriodId ?? 0,
        name: application.name || '-',
        logo: getApplicationLogo(application),
        type: getApplicationSourceLabel(application),
        warningsMetadata: getAccessReviewWarningsMetadata(application),
        statusMetadata: getStatusMetadata(application),
        hasFailed: Boolean(application.hasFailed),
        status: application.status,
        actions: buildActions(application),
        source: application.source,
    }));
};
