import type { AccessReviewStatus } from '@controllers/access-reviews';

export function getReviewStatusLabel(status?: AccessReviewStatus): string {
    switch (status) {
        case 'OUT_OF_SCOPE': {
            return 'Out of scope';
        }
        case 'REJECTED': {
            return 'Rejected';
        }
        case 'APPROVED': {
            return 'Approved';
        }
        case 'NOT_REVIEWED':
        default: {
            return 'Not reviewed';
        }
    }
}
