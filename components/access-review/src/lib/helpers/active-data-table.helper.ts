/**
 * Handles navigation logic for access review application rows.
 *
 * @param id - The application ID.
 * @param reviewPeriodId - The review period ID.
 * @param status - The application status.
 * @param source - The application source.
 * @param workspaceId - The current workspace ID.
 * @param hasLimitedAccess - Whether the user has limited access.
 * @param navigate - The navigation function.
 * @returns Void - Early returns if navigation should be prevented.
 */
export function handleAccessReviewRowNavigation(
    id: number,
    reviewPeriodId: number,
    status: string,
    source: string,
    workspaceId: number | null,
    hasLimitedAccess: boolean,
    navigate: (path: string) => void,
): void {
    // Prevent navigation for manually added applications when user has limited access
    if (source === 'MANUALLY_ADDED' && hasLimitedAccess) {
        return;
    }

    // Prevent navigation if workspaceId is not available
    if (workspaceId === null) {
        return;
    }

    // Navigate based on application status
    if (status === 'COMPLETED') {
        navigate(
            `/workspaces/${workspaceId}/governance/access-review/completed/period/${reviewPeriodId}/applications/${id}`,
        );
    } else {
        navigate(
            `/workspaces/${workspaceId}/governance/access-review/active/${id}/period/${reviewPeriodId}/overview`,
        );
    }
}
