import type { AccessReviewWarningStatus } from '@controllers/access-reviews';

export const getWarningStatusLabel = (
    warning?: AccessReviewWarningStatus,
): string => {
    switch (warning) {
        case 'FORMER_PERSONNEL': {
            return 'Former personnel with access';
        }
        case 'MISSING_MFA': {
            return 'Missing MFA';
        }
        case 'UNLINKED_USERS': {
            return 'Unlinked users';
        }
        case 'SERVICE_ACCOUNTS': {
            return 'Service accounts';
        }
        default: {
            return 'Unknown';
        }
    }
};
