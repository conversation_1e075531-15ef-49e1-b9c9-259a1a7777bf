import { describe, expect, test } from 'vitest';
import type { AccessReviewWarningStatus } from '@controllers/access-reviews';
import { getWarningStatusLabel } from './getWarningStatusLabel.helper';

describe('getApplicationWarningLabel', () => {
    test.each([
        ['FORMER_PERSONNEL', 'Former personnel with access'],
        ['MISSING_MFA', 'Missing MFA'],
        ['UNLINKED_USERS', 'Unlinked users'],
        ['SERVICE_ACCOUNTS', 'Service accounts'],
        ['INVALID_WARNING', 'Unknown'],
        [undefined, 'Unknown'],
        [null, 'Unknown'],
    ])('returns "%s" for %s warning', (warning, expected) => {
        expect(
            getWarningStatusLabel(warning as AccessReviewWarningStatus),
        ).toBe(expected);
    });
});
