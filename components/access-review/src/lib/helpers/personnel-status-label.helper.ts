import { t } from '@globals/i18n/macro';

export const getPersonnelStatusLabel = (status?: string | null): string => {
    if (!status) {
        return t`Unknown`;
    }

    switch (status.toUpperCase()) {
        case 'ACTIVE': {
            return t`Active`;
        }
        case 'INACTIVE': {
            return t`Inactive`;
        }
        case 'SUSPENDED': {
            return t`Suspended`;
        }
        case 'TERMINATED': {
            return t`Terminated`;
        }
        default: {
            return status;
        }
    }
};
