import { describe, expect, test } from 'vitest';
import type { AccessReviewStatus } from '@controllers/access-reviews';
import { getReviewStatusLabel } from './getReviewStatusLabels.helper';

describe('getReviewStatusLabel', () => {
    test.each([
        ['OUT_OF_SCOPE', 'Out of scope'],
        ['NOT_REVIEWED', 'Not reviewed'],
        ['REJECTED', 'Rejected'],
        ['APPROVED', 'Approved'],
        ['UNKNOWN', 'Not reviewed'],
        [undefined, 'Not reviewed'],
        [null, 'Not reviewed'],
    ])('returns "%s" for %s status', (status, expected) => {
        expect(getReviewStatusLabel(status as AccessReviewStatus)).toBe(
            expected,
        );
    });
});
