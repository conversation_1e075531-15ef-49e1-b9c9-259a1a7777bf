import type { AccessReviewPersonnelStatus } from '@controllers/access-reviews';
import { t } from '@globals/i18n/macro';

export const getPersonnelStatusLabel = (
    personnelStatus?: AccessReviewPersonnelStatus,
): string => {
    switch (personnelStatus) {
        case 'CURRENT_EMPLOYEE': {
            return t`Current employee`;
        }
        case 'FORMER_EMPLOYEE': {
            return t`Former employee`;
        }
        case 'CURRENT_CONTRACTOR': {
            return t`Current contractor`;
        }
        case 'FORMER_CONTRACTOR': {
            return t`Former contractor`;
        }
        case 'OUT_OF_SCOPE': {
            return t`Out of scope (ignore)`;
        }
        case 'SPECIAL_FORMER_EMPLOYEE': {
            return t`Former Employee`;
        }
        case 'SPECIAL_FORMER_CONTRACTOR': {
            return t`Former Contractor`;
        }
        case 'FUTURE_HIRE': {
            return t`Future hire`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Out of scope (service account)`;
        }
        case 'UNKNOWN':
        default: {
            return t`Unknown`;
        }
    }
};
