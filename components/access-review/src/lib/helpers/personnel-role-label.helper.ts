import { t } from '@globals/i18n/macro';

export const getPersonnelRolLabelHelper = (roles: string[]): string[] => {
    return roles.map((role) => {
        switch (role.toUpperCase()) {
            case 'ADMIN': {
                return t`Administrator`;
            }
            case 'USER': {
                return t`User`;
            }
            case 'VIEWER': {
                return t`Viewer`;
            }
            case 'OWNER': {
                return t`Owner`;
            }
            default: {
                return role;
            }
        }
    });
};
