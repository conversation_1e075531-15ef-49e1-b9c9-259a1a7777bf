import { isNil } from 'lodash-es';
import { sharedActiveAccessReviewPeriodsController } from '@controllers/access-reviews';
import { ActionStack } from '@cosmos/components/action-stack';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import { useNavigate } from '@remix-run/react';

export const AccessReviewPeriodRangeComponent = observer(
    (): React.JSX.Element => {
        const navigate = useNavigate();

        const { activeAccessReviewPeriodRange, activeAccessReviewPeriod } =
            sharedActiveAccessReviewPeriodsController;

        const { currentWorkspace } = sharedWorkspacesController;

        const { hasLimitedAccess } = sharedFeatureAccessModel;

        const reviewPeriod =
            !isNil(activeAccessReviewPeriodRange.startDate) &&
            !isNil(activeAccessReviewPeriodRange.endDate)
                ? formatDate(
                      'sentence_range',
                      activeAccessReviewPeriodRange.startDate,
                      activeAccessReviewPeriodRange.endDate,
                  )
                : '-';

        return (
            <Stack
                justify="between"
                align="center"
                data-testid="AccessReviewPeriodRangeComponent"
                data-id="ohkmfhAN"
            >
                <Text type="title">
                    <Trans>Review period: {reviewPeriod}</Trans>
                </Text>
                <ActionStack
                    isFullWidth={false}
                    data-id="action-stack"
                    gap="3x"
                    actions={
                        hasLimitedAccess
                            ? []
                            : [
                                  {
                                      actionType: 'button',
                                      id: 'edit-review-period',
                                      typeProps: {
                                          label: t`Edit review period`,
                                          level: 'tertiary',
                                          onClick: () => {
                                              navigate(
                                                  `/workspaces/${currentWorkspace?.id}/governance/access-review/edit-period/${activeAccessReviewPeriod?.id}`,
                                              );
                                          },
                                      },
                                  },
                                  {
                                      actionType: 'button',
                                      id: 'complete-review',
                                      typeProps: {
                                          label: 'Complete review',
                                          level: 'primary',
                                      },
                                  },
                              ]
                    }
                />
            </Stack>
        );
    },
);
