import { activeAccessReviewCompletedDetailsController } from '@controllers/access-reviews';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

export const ReviewersCard = observer((): React.JSX.Element => {
    const { applicationDetails, isCompleted } =
        activeAccessReviewCompletedDetailsController;

    return (
        <Box data-id="reviewers-card-completed" height="fit-content">
            <Card
                title={t`Reviewers`}
                data-testid="ReviewersCard"
                size="lg"
                data-id="JP0tWWqK"
                body={
                    <StackedList data-id="V0smpIue">
                        {applicationDetails?.application.reviewers.map(
                            ({ firstName, lastName, id, avatarUrl }) => {
                                const name = getFullName(firstName, lastName);
                                const initials = getInitials(name);

                                return (
                                    <StackedListItem
                                        data-id="XJzjA1c4"
                                        key={`${id}-${firstName}-${lastName}`}
                                        primaryColumn={
                                            <AvatarIdentity
                                                primaryLabel={name}
                                                fallbackText={initials}
                                                imgSrc={avatarUrl ?? undefined}
                                            />
                                        }
                                    />
                                );
                            },
                        )}
                    </StackedList>
                }
                actions={
                    isCompleted
                        ? []
                        : [
                              {
                                  actionType: 'button',
                                  id: 'edit-button',
                                  typeProps: {
                                      label: t`Edit`,
                                      level: 'secondary',
                                      'data-id': 'edit-button',
                                  },
                              },
                          ]
                }
            />
        </Box>
    );
});
