import { activeAccessReviewCompletedDetailsController } from '@controllers/access-reviews';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Datatable, type DatatableProps } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AccessReviewPeriodApplicationEvidenceResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { ApprovalDateCell } from '../cells/approval-date-cell';
import { CreationDateCell } from '../cells/creation-date-cell';
import { DownloadEvidenceCell } from '../cells/download-evidence-cell';
import { FileNameCell } from '../cells/file-name';

export const LinkedEvidenceCard = observer((): React.JSX.Element => {
    const LINKED_EVIDENCE_COLUMNS: DatatableProps<AccessReviewPeriodApplicationEvidenceResponseDto>['columns'] =
        [
            {
                accessorKey: 'name',
                id: 'filename',
                header: t`File name`,
                enableSorting: false,
                size: 450,
                cell: FileNameCell,
            },
            {
                accessorKey: 'currentVersion',
                id: 'creation-date',
                header: t`Creation date`,
                enableSorting: false,
                size: 150,
                cell: CreationDateCell,
            },
            {
                accessorKey: 'approvedAt',
                id: 'approval-date',
                header: t`Approval date`,
                enableSorting: false,
                size: 150,
                cell: ApprovalDateCell,
            },
            {
                accessorKey: '',
                id: 'download-evidence',
                cell: DownloadEvidenceCell,
                isActionColumn: true,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
        ];

    const { applicationDetails, isCompleted, isLoading } =
        activeAccessReviewCompletedDetailsController;
    const totalEvidences =
        applicationDetails?.application.evidences?.length ?? 0;

    return (
        <Box data-id="linked-evidence-card-completed" height="fit-content">
            <Card
                title={t`Linked evidence`}
                size="lg"
                data-testid="LinkedEvidenceCard"
                data-id="DJXn19JL"
                body={
                    <Stack direction="column" gap="2x">
                        {isCompleted || (
                            <Text>
                                <Trans>
                                    Link evidence to show you&apos;ve performed
                                    a user access review on this application.
                                </Trans>
                            </Text>
                        )}
                        <Datatable
                            hidePagination
                            isLoading={isLoading}
                            tableId="datatable-linked-evidence"
                            columns={LINKED_EVIDENCE_COLUMNS}
                            data-id="datatable-linked-evidence"
                            total={totalEvidences}
                            data={
                                applicationDetails?.application.evidences ?? []
                            }
                            emptyStateProps={{
                                illustrationName: 'Warning',
                                title: t`Linked evidence`,
                                description: t`No linked evidence was found`,
                            }}
                            tableSearchProps={{
                                hideSearch: true,
                            }}
                        />
                    </Stack>
                }
                actions={
                    isCompleted
                        ? []
                        : [
                              {
                                  actionType: 'button',
                                  id: 'add-button',
                                  typeProps: {
                                      label: t`Add evidence`,
                                      level: 'secondary',
                                      'data-id': 'add-button',
                                  },
                              },
                          ]
                }
            />
        </Box>
    );
});
