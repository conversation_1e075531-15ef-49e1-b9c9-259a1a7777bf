import type { AccessReviewApplicationWithProvider } from '@controllers/access-reviews';
import type { MetadataProps } from '@cosmos/components/metadata';
import type {
    SchemaDropdownItems,
    SchemaDropdownProps,
} from '@cosmos/components/schema-dropdown';
import type { AccessReviewPeriodApplicationResponseDto } from '@globals/api-sdk/types';

export type AccessReviewApplicationToDatatableAdapter = Pick<
    AccessReviewApplicationWithProvider,
    | 'id'
    | 'reviewPeriodId'
    | 'name'
    | 'logo'
    | 'source'
    | 'provider'
    | 'totalWarnings'
    | 'hasFailed'
    | 'hasPendingEvidence'
    | 'status'
>;

export interface AccessReviewPeriodActions {
    items: SchemaDropdownItems;
    colorScheme: SchemaDropdownProps['colorScheme'];
}

export interface AccessReviewPeriodDatatable {
    id: number;
    reviewPeriodId: number;
    name: string;
    logo: string;
    type: string;
    warningsMetadata: MetadataProps | null;
    statusMetadata: MetadataProps;
    hasFailed: boolean;
    status: AccessReviewPeriodApplicationResponseDto['status'];
    actions: AccessReviewPeriodActions | null;
    source: AccessReviewApplicationToDatatableAdapter['source'];
}

export type BuildAccessReviewApplicationActions = (
    application: Pick<
        AccessReviewApplicationToDatatableAdapter,
        | 'id'
        | 'reviewPeriodId'
        | 'hasFailed'
        | 'hasPendingEvidence'
        | 'status'
        | 'source'
    >,
) => AccessReviewPeriodActions;
