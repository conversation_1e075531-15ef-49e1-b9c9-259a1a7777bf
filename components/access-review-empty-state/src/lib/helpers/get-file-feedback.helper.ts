import type { AccessReviewUsersCsvValidationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

/**
 * Type for file upload feedback.
 */
export interface FileFeedback {
    type: 'success' | 'error';
    message: string;
}

/**
 * Gets feedback for file upload based on validation data.
 *
 * @param validationData - The validation data from CSV validation.
 * @returns Feedback object or undefined if no validation data is available.
 */
export function getFileFeedback(
    validationData?: AccessReviewUsersCsvValidationResponseDto,
): FileFeedback | undefined {
    if (validationData === undefined) {
        return undefined;
    }

    const validIdentitiesCount =
        validationData.validationStatistics.uarValidUsersCount;
    const totalIdentitiesCount = validationData.validationStatistics.totalData;

    const validIdentitiesCountStr = String(validIdentitiesCount);
    const totalIdentitiesCountStr = String(totalIdentitiesCount);

    const countMessage = t`${validIdentitiesCountStr}/${totalIdentitiesCountStr} personnel to be uploaded.`;

    if (validationData.status === 'VALID') {
        return {
            type: 'success' as const,
            message: countMessage,
        };
    }

    return {
        type: 'error' as const,
        message: t`${countMessage} The CSV you uploaded is empty. Please upload a CSV with personnel records`,
    };
}
