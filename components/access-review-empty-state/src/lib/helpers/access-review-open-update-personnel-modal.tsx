import { sharedAccessReviewApplicationPersonnelCsvTemplateController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { AccessReviewUpdatePersonnelModal } from '../access-review-update-personnel-modal';

export const openAccessReviewUpdatePersonnelModal = (): void => {
    sharedAccessReviewApplicationPersonnelCsvTemplateController.loadCsvTemplate();

    modalController.openModal({
        id: 'access-review-update-personnel-modal',
        size: 'lg',
        content: () => (
            <AccessReviewUpdatePersonnelModal
                data-id="I_3ZfGh7"
                isActivePeriod={false}
                onClose={() => {
                    modalController.closeModal(
                        'access-review-update-personnel-modal',
                    );
                }}
            />
        ),
        centered: true,
        disableClickOutsideToClose: true,
    });
};
