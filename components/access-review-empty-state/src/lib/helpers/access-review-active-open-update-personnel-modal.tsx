import { sharedAccessReviewApplicationPersonnelCsvTemplateController } from '@controllers/access-reviews';
import { modalController } from '@controllers/modal';
import { AccessReviewUpdatePersonnelModal } from '../access-review-update-personnel-modal';

export const openAccessReviewActiveUpdatePersonnelModal = (): void => {
    sharedAccessReviewApplicationPersonnelCsvTemplateController.loadCsvTemplate();

    modalController.openModal({
        id: 'access-review-update-personnel-modal',
        size: 'lg',
        content: () => (
            <AccessReviewUpdatePersonnelModal
                isActivePeriod
                data-id="I_3ZfGh7"
                onClose={() => {
                    modalController.closeModal(
                        'access-review-update-personnel-modal',
                    );
                }}
            />
        ),
        centered: true,
        disableClickOutsideToClose: true,
    });
};
