import { capitalize, trim } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import type { AccessReviewUserIdentityCsvResponseDto } from '@globals/api-sdk/types';

interface ReviewStatusCellProps {
    row: {
        original: AccessReviewUserIdentityCsvResponseDto;
    };
}

export const ReviewStatusCell = ({
    row,
}: ReviewStatusCellProps): React.JSX.Element => {
    const { accessData } = row.original;
    const permission = (accessData?.raw.permission as string) || '';
    const formattedPermission = capitalize(trim(permission));

    return (
        <Text
            size="100"
            colorScheme="neutral"
            type="body"
            data-id="06Jm7li3"
            data-testid="ReviewStatusCell"
        >
            {formattedPermission}
        </Text>
    );
};
