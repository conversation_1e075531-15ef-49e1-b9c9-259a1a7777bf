import { capitalize } from 'lodash-es';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { AccessReviewUserIdentityCsvResponseDto } from '@globals/api-sdk/types';
import { getInitials } from '@helpers/formatters';

interface PersonnelCellProps {
    row: {
        original: AccessReviewUserIdentityCsvResponseDto;
    };
}

export const PersonnelCell = ({
    row,
}: PersonnelCellProps): React.JSX.Element => {
    const { username, email } = row.original;
    const displayName = username || '';
    const [firstName, lastName] = displayName.split(' ').map(capitalize);
    const initials = getInitials(`${firstName} ${lastName}`);

    return (
        <AvatarIdentity
            primaryLabel={displayName}
            secondaryLabel={email || undefined}
            data-id="06Jm7li3"
            fallbackText={initials}
            data-testid="PersonnelCell"
        />
    );
};
