import { useMemo } from 'react';
import { Datatable } from '@cosmos/components/datatable';
import type { AccessReviewUsersCsvValidationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { PersonnelCell } from './cells/personnel-cell';
import { ReviewStatusCell } from './cells/review-status-cell';

const MAX_ROWS_TO_DISPLAY = 5;

interface AccessReviewValidationDataTableProps {
    validationData?: AccessReviewUsersCsvValidationResponseDto;
    isValidating: boolean;
}

export const AccessReviewValidationDataTable = ({
    validationData,
    isValidating,
}: AccessReviewValidationDataTableProps): React.JSX.Element | null => {
    const columns = useMemo(
        () => [
            {
                id: 'personnel',
                header: t`Personnel`,
                accessorKey: 'username',
                enableSorting: false,
                cell: PersonnelCell,
            },
            {
                id: 'access',
                header: t`Access`,
                accessorKey: 'accessData',
                enableSorting: false,
                cell: ReviewStatusCell,
            },
        ],
        [],
    );

    const identities = validationData?.identities ?? [];
    const totalIdentities = identities.length;
    const displayedData = identities.slice(0, MAX_ROWS_TO_DISPLAY);
    const displayedTotal =
        totalIdentities > MAX_ROWS_TO_DISPLAY
            ? MAX_ROWS_TO_DISPLAY
            : totalIdentities;

    return (
        <Datatable
            hidePagination
            tableId="datatable-access-review-validation-data"
            isLoading={isValidating}
            data-testid="AccessReviewValidationDataTable"
            data-id="HwsMeN1q"
            tableSearchProps={{ hideSearch: true }}
            columns={columns}
            data={displayedData}
            total={displayedTotal}
        />
    );
};
