import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyState } from '@cosmos/components/empty-state';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { breakpointMd } from '@cosmos/constants/tokens';
import { observer } from '@globals/mobx';
import { openAccessReviewActiveUpdatePersonnelModal } from './helpers/access-review-active-open-update-personnel-modal';
import { openAccessReviewUpdatePersonnelModal } from './helpers/access-review-open-update-personnel-modal';

interface AccessReviewEmptyStateComponentProps {
    isPersonnelApplication?: boolean;
}

export const AccessReviewEmptyStateComponent = observer(
    ({
        isPersonnelApplication = false,
    }: AccessReviewEmptyStateComponentProps): React.JSX.Element => {
        const {
            isLoading,
            accessReviewsApplicationsDetails: { name },
        } = activeAccessReviewsApplicationsController;

        if (isLoading) {
            return (
                <Stack
                    direction="column"
                    align="center"
                    gap="6x"
                    p="8x"
                    data-testid="PoliciesBuilderPolicyViewLoader"
                    data-id="5DYP_Z0x"
                >
                    <Loader isSpinnerOnly label="Loading" />
                </Stack>
            );
        }

        return (
            <Stack
                data-testid="AccessReviewEmptyStateComponent"
                data-id="7J51HLv7"
                height="100%"
                justify="center"
                align="center"
            >
                <Box width={breakpointMd}>
                    <EmptyState
                        illustrationName="AddCircle"
                        imageSize="md"
                        title={`You’ve manually added ${name} for access review`}
                        description="Upload a list of personnel to get it ready for an access review"
                        rightAction={
                            isPersonnelApplication && (
                                <Button
                                    label="Upload personnel"
                                    level="primary"
                                    onClick={
                                        openAccessReviewUpdatePersonnelModal
                                    }
                                />
                            )
                        }
                        leftAction={
                            isPersonnelApplication ? null : (
                                <Button
                                    label="Upload personnel"
                                    level="primary"
                                    onClick={
                                        openAccessReviewActiveUpdatePersonnelModal
                                    }
                                />
                            )
                        }
                    />
                </Box>
            </Stack>
        );
    },
);
