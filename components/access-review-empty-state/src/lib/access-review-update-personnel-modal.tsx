import { isEmpty } from 'lodash-es';
import { useRef, useState } from 'react';
import {
    sharedAccessReviewApplicationPersonnelCsvTemplateController,
    sharedAccessReviewPeriodApplicationController,
    sharedAccessReviewPeriodApplicationUsersController,
} from '@controllers/access-reviews';
import {
    activeAccessReviewsApplicationsController,
    activeAccessReviewsApplicationsUserController,
} from '@controllers/access-reviews-applications';
import { snackbarController } from '@controllers/snackbar';
import { Button } from '@cosmos/components/button';
import type { CosmosFileObject } from '@cosmos/components/file-upload';
import { FileUploadField } from '@cosmos/components/file-upload-field';
import { Loader } from '@cosmos/components/loader';
import { Modal } from '@cosmos/components/modal';
import { RadioFieldGroup } from '@cosmos/components/radio-field-group';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AccessReviewUsersCsvValidationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import { useParams } from '@remix-run/react';
import { AccessReviewValidationDataTable } from './access-review-validation-data-table';
import {
    FORM_ID,
    getUploadModeOptions,
} from './constants/access-review-update-personnel.constants';
import { getFileFeedback } from './helpers/get-file-feedback.helper';

export type UploadMode = 'CHANGES_ONLY' | 'OVERWRITE_ALL';

interface AccessReviewUpdatePersonnelModalProps {
    onClose: () => void;
    isActivePeriod: boolean;
}

export const AccessReviewUpdatePersonnelModal = observer(
    ({
        onClose,
        isActivePeriod,
    }: AccessReviewUpdatePersonnelModalProps): React.JSX.Element => {
        // State
        const [uploadMode, setUploadMode] =
            useState<UploadMode>('CHANGES_ONLY');
        const [isValidating, setIsValidating] = useState(false);
        const [validationData, setValidationData] = useState<
            AccessReviewUsersCsvValidationResponseDto | undefined
        >(undefined);
        const [isSubmitting, setIsSubmitting] = useState(false);
        const [hasFileUploaded, setHasFileUploaded] = useState(false);
        const fileRef = useRef<CosmosFileObject[]>([]);
        const { periodId } = useParams();

        // Application details based on period type
        const [periodController, applicationsController] = [
            sharedAccessReviewPeriodApplicationController,
            activeAccessReviewsApplicationsController,
        ];

        const { isLoadingApplicationDetails, applicationId, applicationName } =
            isActivePeriod
                ? {
                      isLoadingApplicationDetails: periodController.isLoading,
                      applicationId:
                          periodController.accessReviewPeriodApplicationDetails
                              ?.application.accessApplicationId ?? 0,
                      applicationName:
                          periodController.accessReviewPeriodApplicationDetails
                              ?.application.name ?? '',
                  }
                : {
                      isLoadingApplicationDetails:
                          applicationsController.isLoading,
                      applicationId:
                          applicationsController
                              .accessReviewsApplicationsDetails.id,
                      applicationName:
                          applicationsController
                              .accessReviewsApplicationsDetails.name,
                  };

        const { accessReviewApplicationUsersList } =
            activeAccessReviewsApplicationsUserController;

        const {
            accessReviewPeriodApplicationUsersList,
            accessReviewPeriodApplicationUsers,
        } = sharedAccessReviewPeriodApplicationUsersController;

        const { isLoading: isCsvTemplateLoading, csvTemplate } =
            sharedAccessReviewApplicationPersonnelCsvTemplateController;

        // Derived state
        const hasExistingPersonnel = isActivePeriod
            ? !isEmpty(accessReviewPeriodApplicationUsersList)
            : !isEmpty(accessReviewApplicationUsersList);

        const isLoaderVisible =
            isLoadingApplicationDetails || isCsvTemplateLoading;

        const handleDownloadTemplate = () => {
            Promise.resolve()
                .then(() => {
                    downloadFileFromSignedUrl(csvTemplate);
                    snackbarController.addSnackbar({
                        id: 'access-review-download-template-success',
                        props: {
                            title: t`Template downloaded successfully.`,
                            severity: 'success',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                })
                .catch(() => {
                    snackbarController.addSnackbar({
                        id: 'access-review-download-template-error',
                        props: {
                            title: t`Unable to download template, try again later.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                });
        };

        const handleFileUpload = (files: CosmosFileObject[]) => {
            setHasFileUploaded(true);
            if (isEmpty(files)) {
                // File was removed
                fileRef.current = [];
                setValidationData(undefined);
                setIsValidating(false);
                setHasFileUploaded(false);

                return;
            }

            const rawFile = files[0].file;

            fileRef.current = files;

            setIsValidating(true);

            activeAccessReviewsApplicationsController
                .handleCsvFileValidation(
                    rawFile,
                    () => {
                        setIsValidating(true);
                    },
                    (data) => {
                        setValidationData(data);
                        setIsValidating(false);
                    },
                    () => {
                        setIsValidating(false);
                    },
                )
                .catch(() => {
                    // Error handling is done via callbacks in controller
                });
        };

        const handleComplete = () => {
            setIsSubmitting(true);

            activeAccessReviewsApplicationsController
                .handlePersonnelUploadComplete(
                    fileRef,
                    validationData,
                    uploadMode,
                    isActivePeriod,
                    applicationId,
                    periodId ? Number(periodId) : undefined,
                    () => {
                        onClose();
                        if (isActivePeriod) {
                            accessReviewPeriodApplicationUsers.invalidate();
                        }
                        activeAccessReviewsApplicationsUserController.invalidateAccessReviewApplicationUsers();
                    },
                )
                .catch(() => {
                    // Error handling is done via callbacks in controller
                })
                .finally(() => {
                    setIsSubmitting(false);
                });
        };

        if (isLoaderVisible) {
            return (
                <Modal.Body>
                    <Loader isSpinnerOnly label={t`Loading`} />
                </Modal.Body>
            );
        }

        return (
            <>
                <Modal.Header
                    size="md"
                    title={t`Update assigned personnel list to ${applicationName}`}
                    closeButtonAriaLabel={t`Close update personnel modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Stack gap="4x" direction="column" align="start">
                        <Stack gap="2x" direction="column" align="start">
                            <Text size="200">
                                {t`Download the sample data template to upload the personnel assigned to this application. If you already have a prepared sheet, make sure it contains the following columns: First name, Last name, Email, Access.`}
                            </Text>
                            <Button
                                label={t`Download template`}
                                size="md"
                                hasPadding={false}
                                level="tertiary"
                                startIconName="Download"
                                onClick={handleDownloadTemplate}
                            />
                        </Stack>
                        {hasExistingPersonnel && (
                            <RadioFieldGroup
                                required
                                formId={FORM_ID}
                                name="uploadMode"
                                cosmosUseWithCaution_forceOptionOrientation="vertical"
                                data-id="uploadModeField"
                                options={[...getUploadModeOptions()]}
                                value={uploadMode}
                                label={t`Choose how to re-upload your data`}
                                onChange={(event) => {
                                    setUploadMode(
                                        event.target.value as UploadMode,
                                    );
                                }}
                            />
                        )}
                        <FileUploadField
                            oneFileOnly
                            isMulti={false}
                            required={false}
                            innerLabel={t`Or drop file here`}
                            label={t`Upload the completed CSV template`}
                            labelStyleOverrides={{ size: 'md' }}
                            name="csvTemplate"
                            removeButtonText={t`Remove file`}
                            selectButtonText={t`Upload file`}
                            formId={FORM_ID}
                            acceptedFormats={['csv']}
                            isReadOnly={isValidating}
                            feedback={getFileFeedback(validationData)}
                            errorCodeMessages={{
                                'file-invalid-type': t`Not a valid file type.`,
                                'file-too-large': t`File size is too large.`,
                                'file-too-small': t`File size is too small.`,
                                'too-many-files': t`Contains too many files.`,
                            }}
                            onUpdate={handleFileUpload}
                        />
                        {validationData?.status === 'VALID' && (
                            <Text size="100">
                                {t`Here's a preview of the first few personnel in your file:`}
                                ’
                            </Text>
                        )}
                        {hasFileUploaded &&
                            (validationData?.status === undefined ||
                                validationData.status === 'VALID') && (
                                <AccessReviewValidationDataTable
                                    validationData={validationData}
                                    isValidating={isValidating}
                                    data-testid="renderDataTable"
                                    data-id="poZtdpaz"
                                />
                            )}
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Complete`,
                            onClick: handleComplete,
                            isLoading: isSubmitting,
                        },
                    ]}
                />
            </>
        );
    },
);
