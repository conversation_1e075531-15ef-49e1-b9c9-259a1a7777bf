import { Text } from '@cosmos/components/text';
import { getAssetTypeLabel } from '../assets.constants';
import type { AssetsTableRowCellProps } from '../assets.type';

export const AssetsTypeCell = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    return (
        <Text
            data-id="assets-table-row-asset-type"
            data-testid="AssetsTypeCell"
        >
            {getAssetTypeLabel(original.assetType)}
        </Text>
    );
};
