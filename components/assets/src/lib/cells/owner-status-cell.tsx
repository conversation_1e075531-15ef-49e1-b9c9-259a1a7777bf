import { Text } from '@cosmos/components/text';
import { getPersonnelEmploymentStatusLabel } from '../assets.constants';
import type { AssetsTableRowCellProps } from '../assets.type';

export const OwnerStatusCell = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    if (!original.employmentStatus) {
        return <Text>-</Text>;
    }

    return (
        <Text
            data-id="assets-table-row-employment-status"
            data-testid="OwnerStatusCell"
        >
            {getPersonnelEmploymentStatusLabel(original.employmentStatus)}
        </Text>
    );
};
