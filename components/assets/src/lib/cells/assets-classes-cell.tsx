import { useMemo, useRef, useState } from 'react';
import { Button } from '@cosmos/components/button';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { AssetsTableRowCellProps } from '../assets.type';
import { processAssetClasses } from '../helpers/assets-classes.helper';

export const AssetsClassesCellComponent = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isOpen, setIsOpen] = useState(false);

    const { displayedClassLabels, remainingCount, remainingClassLabels } =
        useMemo(() => {
            return processAssetClasses(original.assetClassTypes);
        }, [original.assetClassTypes]);

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(!isOpen);
    };

    return (
        <Stack
            direction="row"
            align="center"
            gap="xs"
            data-testid="AssetsClassesCellComponent"
            data-id="G9IcH2WX"
        >
            <Text data-id="G9IcH2WX-text">{displayedClassLabels}</Text>
            {remainingCount > 0 && (
                <>
                    <Button
                        ref={buttonRef}
                        colorScheme="neutral"
                        data-id="G9IcH2WX-show-more"
                        label={`+${remainingCount}`}
                        level="tertiary"
                        size="sm"
                        type="button"
                        onClick={handleClick}
                    />
                    <Popover
                        anchor={buttonRef.current}
                        isOpen={isOpen}
                        placement="bottom-start"
                        padding="xl"
                        content={
                            <Text size="100" data-id="G9IcH2WX-remaining">
                                {remainingClassLabels}
                            </Text>
                        }
                        onDismiss={() => {
                            setIsOpen(false);
                        }}
                    />
                </>
            )}
        </Stack>
    );
};
