import { getAssetsDeviceComplianceState } from '@controllers/assets';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { buildDeviceComplianceKVP } from '@views/asset-details';
import type { AssetsTableRowCellProps } from '../assets.type';

export const AssetsDeviceComplianceCellComponent = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    const status = getAssetsDeviceComplianceState(
        [original.device],
        original.device.complianceChecksWithExclusions,
        original.employmentStatus,
    );

    const deviceComplianceKVPs = buildDeviceComplianceKVP({ status });

    return (
        <Stack
            direction="column"
            gap="2x"
            data-testid="AssetsDeviceComplianceCellComponent"
            data-id="KpmxF4Ev"
        >
            {deviceComplianceKVPs.map((kvpProps) => (
                <KeyValuePair
                    key={kvpProps.id}
                    id={kvpProps.id}
                    type={kvpProps.type}
                    label={''}
                    value={kvpProps.value}
                    data-id="TXkejdAZ"
                />
            ))}
        </Stack>
    );
};
