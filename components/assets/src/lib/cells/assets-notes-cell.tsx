import { useRef, useState } from 'react';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import { t } from '@globals/i18n/macro';
import type { AssetsTableRowCellProps } from '../assets.type';

const MAX_LENGTH_TRUNCATION = 60;

export const AssetsNoteCellComponent = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    const { notes } = original;

    const buttonRef = useRef<HTMLButtonElement>(null);
    const [isOpen, setIsOpen] = useState(false);

    const handleClick = (e: React.MouseEvent) => {
        e.preventDefault();
        e.stopPropagation();
        setIsOpen(!isOpen);
    };

    if (!notes || notes.trim() === '') {
        return (
            <EmptyStateTableCell
                data-id="assets-notes-empty"
                data-testid="AssetsNoteCellComponent"
            />
        );
    }

    return (
        <Stack
            direction="column"
            align="start"
            gap="xs"
            data-id="assets-notes-tooltip"
            data-testid="AssetsNoteCellComponent"
        >
            <Text
                data-testid="AssetsNoteCellComponent"
                data-id="assets-notes-text"
            >
                <Truncation mode="end" maxLength={MAX_LENGTH_TRUNCATION}>
                    {notes}
                </Truncation>
            </Text>
            {notes.length > MAX_LENGTH_TRUNCATION && (
                <>
                    <Button
                        ref={buttonRef}
                        colorScheme="neutral"
                        data-id="G9IcH2WX-show-more"
                        label={t`Show more`}
                        level="tertiary"
                        size="sm"
                        type="button"
                        onClick={handleClick}
                    />
                    <Popover
                        anchor={buttonRef.current}
                        isOpen={isOpen}
                        placement="bottom-start"
                        padding="xl"
                        content={
                            <Box maxWidth="200px" data-id="G9IcH2WX-box">
                                <Text data-id="G9IcH2WX-remaining">
                                    {notes}
                                </Text>
                            </Box>
                        }
                        onDismiss={() => {
                            setIsOpen(false);
                        }}
                    />
                </>
            )}
        </Stack>
    );
};
