import { DateTime } from '@cosmos-lab/components/date-time';
import type { AssetsTableRowCellProps } from '../assets.type';

export const AssetsDateCellComponent = ({
    row: { original },
    column: { id },
}: AssetsTableRowCellProps): React.JSX.Element => {
    const { createdAt, updatedAt, removedAt } = original;

    const dateMap = {
        UPDATED: updatedAt,
        REMOVED_AT: removedAt,
        CREATED: createdAt,
    };

    const date = dateMap[id as keyof typeof dateMap];

    return (
        <DateTime
            date={date}
            format="table"
            data-testid="AssetsDateCellComponent"
            data-id={id === 'createdAt' ? 'lk6CYFS1' : 'At_gQBrH'}
        />
    );
};
