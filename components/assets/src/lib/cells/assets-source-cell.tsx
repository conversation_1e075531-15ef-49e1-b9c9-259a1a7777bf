import { isNil, isString } from 'lodash-es';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import { providers } from '@globals/providers';
import { getInitials } from '@helpers/formatters';
import {
    getAssetProviderLabel,
    isValidAssetProvider,
} from '../assets.constants';
import type { AssetsTableRowCellProps } from '../assets.type';

export const AssetsSourceCellComponent = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    const { assetProvider, externalOwnerId, assetReferenceType } = original;

    if (assetProvider in providers) {
        const provider = Object.values(providers).find(
            (p) => p.id === assetProvider,
        );

        return (
            <OrganizationIdentity
                fallbackText={provider?.name}
                data-testid="AssetsSourceCellComponent"
                data-id="MuQ3GqT-"
                imgSrc={provider?.logo ?? ''}
                size="sm"
                primaryLabel={
                    (provider?.name ?? '') +
                    (externalOwnerId ? ` | ${externalOwnerId}` : '')
                }
            />
        );
    }

    if (isValidAssetProvider(assetProvider)) {
        if (assetReferenceType === 'VIRTUAL' && !isNil(externalOwnerId)) {
            const provider = Object.values(providers).find(
                (p) => p.id === assetProvider,
            );

            return (
                <OrganizationIdentity
                    fallbackText={`${provider?.name} | ${externalOwnerId}`}
                    data-testid="AssetsSourceCellComponent"
                    data-id="MuQ3GqT-"
                    imgSrc={provider?.logo ?? ''}
                    primaryLabel={`${provider?.name} | ${externalOwnerId}`}
                    size="sm"
                />
            );
        }

        return (
            <OrganizationIdentity
                data-testid="AssetsSourceCellComponent"
                data-id="MuQ3GqT-"
                size="sm"
                imgSrc=""
                primaryLabel={getAssetProviderLabel(assetProvider)}
                fallbackText={
                    isString(assetProvider) ? getInitials(assetProvider) : ''
                }
            />
        );
    }

    return (
        <EmptyStateTableCell
            data-testid="AssetsSourceCellComponent"
            data-id="MuQ3GqT-"
        />
    );
};
