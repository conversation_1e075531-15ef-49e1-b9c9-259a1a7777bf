import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { getFullName, getInitials } from '@helpers/formatters';
import type { AssetsTableRowCellProps } from '../assets.type';

export const AssetsOwnerCellComponent = ({
    row: { original },
}: AssetsTableRowCellProps): React.JSX.Element => {
    const { firstName, lastName, avatarUrl } = original.owner;

    return (
        <AvatarIdentity
            primaryLabel={getFullName(firstName, lastName)}
            fallbackText={getInitials(`${firstName} ${lastName}`)}
            imgSrc={avatarUrl ?? undefined}
            data-testid="AssetsOwnerCellComponent"
            data-id="kg8sm-9C"
        />
    );
};
