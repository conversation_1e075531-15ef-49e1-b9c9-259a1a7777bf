import { beforeAll, describe, expect, test } from 'vitest';
import type { AssetResponseDto } from '@globals/api-sdk/types';
import { i18n } from '@globals/i18n';
import type { AssetsClassType } from '../assets.type';
import { processAssetClasses } from './assets-classes.helper';

const createMockAssetClassTypes = (
    assetClassTypes: AssetsClassType[],
): AssetResponseDto['assetClassTypes'] => {
    return assetClassTypes.map((type, index) => ({
        id: index + 1,
        assetClassType: type,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
    }));
};

describe('processAssetClasses helper', () => {
    beforeAll(() => {
        // Initialize i18n with empty messages to prevent activation errors
        i18n.load('en', {});
        i18n.activate('en');
    });

    test('processes single asset class type', () => {
        const assetClassTypes = createMockAssetClassTypes(['SOFTWARE']);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe('Software');
        expect(result.remainingCount).toBe(0);
        expect(result.remainingClassLabels).toBe('');
    });

    test('processes multiple asset class types within limit', () => {
        const assetClassTypes = createMockAssetClassTypes(['PERSONNEL']);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe('Personnel');
        expect(result.remainingCount).toBe(0);
        expect(result.remainingClassLabels).toBe('');
    });

    test('processes asset class types exceeding limit', () => {
        const assetClassTypes = createMockAssetClassTypes([
            'PERSONNEL',
            'HARDWARE',
            'DOCUMENT',
            'SOFTWARE',
        ]);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe(
            'Personnel, Hardware, Document',
        );
        expect(result.remainingCount).toBe(1);
        expect(result.remainingClassLabels).toBe('Software');
    });

    test('filters out POLICY asset class types', () => {
        const assetClassTypes = createMockAssetClassTypes([
            'PERSONNEL',
            'POLICY',
            'HARDWARE',
        ]);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe('Personnel, Hardware');
        expect(result.remainingCount).toBe(0);
        expect(result.remainingClassLabels).toBe('');
    });

    test('handles only POLICY asset class types', () => {
        const assetClassTypes = createMockAssetClassTypes(['POLICY']);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe('');
        expect(result.remainingCount).toBe(0);
        expect(result.remainingClassLabels).toBe('');
    });

    test('handles empty asset class types array', () => {
        const assetClassTypes = createMockAssetClassTypes([]);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe('');
        expect(result.remainingCount).toBe(0);
        expect(result.remainingClassLabels).toBe('');
    });

    test('handles mixed asset class types with POLICY filtered out', () => {
        const assetClassTypes = createMockAssetClassTypes([
            'SOFTWARE',
            'POLICY',
            'HARDWARE',
            'POLICY',
            'DOCUMENT',
            'CODE',
        ]);

        const result = processAssetClasses(assetClassTypes);

        expect(result.displayedClassLabels).toBe(
            'Software, Hardware, Document',
        );
        expect(result.remainingCount).toBe(1);
        expect(result.remainingClassLabels).toBe('Code');
    });
});
