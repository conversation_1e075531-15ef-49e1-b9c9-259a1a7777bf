import type { AssetResponseDto } from '@globals/api-sdk/types';
import {
    getAssetsClassTypeLabel,
    MAX_ASSETS_CLASS_TYPES_ITEMS_TO_DISPLAY,
} from '../assets.constants';

export interface ProcessedAssetClasses {
    displayedClassLabels: string;
    remainingCount: number;
    remainingClassLabels: string;
}

function getFilteredLabels(
    assetClassTypes: AssetResponseDto['assetClassTypes'],
): string[] {
    return assetClassTypes
        .map((classType) => getAssetsClassTypeLabel(classType.assetClassType))
        .filter((classType) => classType !== undefined);
}

export function processAssetClasses(
    assetClassTypes: AssetResponseDto['assetClassTypes'],
): ProcessedAssetClasses {
    const labels = getFilteredLabels(assetClassTypes);
    const displayed = labels.slice(0, MAX_ASSETS_CLASS_TYPES_ITEMS_TO_DISPLAY);
    const remaining = labels.slice(MAX_ASSETS_CLASS_TYPES_ITEMS_TO_DISPLAY);

    return {
        displayedClassLabels: displayed.join(', '),
        remainingCount: remaining.length,
        remainingClassLabels: remaining.join(', '),
    };
}

export function getFilteredAssetClassLabels(
    assetClassTypes: AssetResponseDto['assetClassTypes'],
): string[] {
    return getFilteredLabels(assetClassTypes);
}
