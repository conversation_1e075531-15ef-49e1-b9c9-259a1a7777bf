import type {
    AssetResponseDto,
    DeviceResponseDto,
} from '@globals/api-sdk/types';

export interface AssetsTableRowCellProps {
    row: { original: AssetResponseDto };
    column: { id: string };
}

export type AssetsClassType =
    | 'HARDWARE'
    | 'POLICY'
    | 'DOCUMENT'
    | 'PERSONNEL'
    | 'SOFTWARE'
    | 'CODE'
    | 'CONTAINER'
    | 'COMPUTE'
    | 'NETWORKING'
    | 'DATABASE'
    | 'STORAGE';

export type AssetType = 'VIRTUAL' | 'PHYSICAL';

export type PersonnelEmploymentStatusType = NonNullable<
    AssetResponseDto['employmentStatus']
>;

export type DeviceSourceType = DeviceResponseDto['sourceType'];
