import { sharedAssetsController } from '@controllers/assets';
import type { TableAction } from '@cosmos/components/datatable';
import type { AssetResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type {
    AssetsClassType,
    AssetType,
    PersonnelEmploymentStatusType,
} from './assets.type';

export function getAssetsClassTypeLabel(
    classType: AssetsClassType,
): string | undefined {
    switch (classType) {
        case 'HARDWARE': {
            return t`Hardware`;
        }
        case 'DOCUMENT': {
            return t`Document`;
        }
        case 'PERSONNEL': {
            return t`Personnel`;
        }
        case 'SOFTWARE': {
            return t`Software`;
        }
        case 'CODE': {
            return t`Code`;
        }
        case 'CONTAINER': {
            return t`Container`;
        }
        case 'COMPUTE': {
            return t`Compute`;
        }
        case 'NETWORKING': {
            return t`Networking`;
        }
        case 'DATABASE': {
            return t`Database`;
        }
        case 'STORAGE': {
            return t`Storage`;
        }
        default: {
            return undefined;
        }
    }
}

export function getAssetTypeLabel(assetType: AssetType): string {
    switch (assetType) {
        case 'VIRTUAL': {
            return t`Virtual`;
        }
        case 'PHYSICAL': {
            return t`Physical`;
        }
        default: {
            return t`Unknown`;
        }
    }
}

export function getPersonnelEmploymentStatusLabel(
    status: PersonnelEmploymentStatusType,
): string {
    switch (status) {
        case 'CURRENT_EMPLOYEE': {
            return t`Current employee`;
        }
        case 'FORMER_EMPLOYEE': {
            return t`Former employee`;
        }
        case 'CURRENT_CONTRACTOR': {
            return t`Current contractor`;
        }
        case 'FORMER_CONTRACTOR': {
            return t`Former contractor`;
        }
        case 'OUT_OF_SCOPE': {
            return t`Out of scope`;
        }
        case 'UNKNOWN': {
            return t`Unknown`;
        }
        case 'SPECIAL_FORMER_EMPLOYEE': {
            return t`Special former employee`;
        }
        case 'SPECIAL_FORMER_CONTRACTOR': {
            return t`Special former contractor`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Service account`;
        }
        case 'FUTURE_HIRE': {
            return t`Future hire`;
        }
        default: {
            return t`Unknown`;
        }
    }
}

export const MAX_ASSETS_CLASS_TYPES_ITEMS_TO_DISPLAY = 3;

export const getAssetsTableActionsDownloadAll = action((): TableAction[] => {
    const { executeReport } = sharedAssetsController;

    return [
        {
            actionType: 'button',
            id: 'download-all-assets',
            typeProps: {
                label: t`Download`,
                level: 'tertiary',
                startIconName: 'Download',
                colorScheme: 'neutral',
                onClick: (): void => {
                    executeReport(false);
                },
            },
        },
    ];
});

export const getDownloadFilteredTableActions = action((): TableAction[] => {
    const { executeReport } = sharedAssetsController;

    return [
        {
            actionType: 'dropdown',
            id: 'download',
            typeProps: {
                label: t`Download`,
                side: 'bottom',
                align: 'start',
                colorScheme: 'neutral',
                items: [
                    {
                        id: 'download-csv',
                        type: 'group',
                        items: [
                            {
                                id: 'all-assets',
                                label: t`All assets`,
                                type: 'item',
                                value: 'all-assets',
                                onClick: (): void => {
                                    executeReport(false);
                                },
                            },
                            {
                                id: 'filtered-view',
                                label: t`Filtered view`,
                                type: 'item',
                                value: 'filtered-view',
                                onClick: (): void => {
                                    executeReport(true);
                                },
                            },
                        ],
                    },
                ],
                level: 'tertiary',
                startIconName: 'Download',
            },
        },
    ];
});

export function isValidAssetProvider(assetProvider: string): boolean {
    switch (assetProvider) {
        case 'DRATA':
        case 'AGENT':
        case 'GOOGLE':
        case 'MICROSOFT_365':
        case 'MICROSOFT_365_GCC_HIGH':
        case 'OKTA_IDENTITY':
        case 'JAMF':
        case 'INTUNE':
        case 'INTUNE_GCC_HIGH':
        case 'KANDJI':
        case 'JUMPCLOUD':
        case 'MERGEDEV_JUMPCLOUD':
        case 'MERGEDEV_CYBERARK':
        case 'DRATA_DEV':
        case 'AWS':
        case 'AWS_ORG_UNITS':
        case 'AWS_GOV_CLOUD':
        case 'HEXNODE_UEM':
        case 'RIPPLING':
        case 'WORKSPACE_ONE':
        case 'CSV_IDP':
        case 'AZURE':
        case 'MERGEDEV_ONELOGIN':
        case 'GCP':
        case 'KOLIDE':
        case 'MERGEDEV_PINGONE': {
            return true;
        }
        default: {
            return false;
        }
    }
}

export function getAssetProviderLabel(
    assetProvider: AssetResponseDto['assetProvider'],
): string {
    switch (assetProvider) {
        case 'DRATA': {
            return 'Drata';
        }
        case 'AGENT': {
            return 'Agent';
        }
        case 'GOOGLE': {
            return 'Google Workspace';
        }
        case 'MICROSOFT_365': {
            return 'Microsoft 365';
        }
        case 'MICROSOFT_365_GCC_HIGH': {
            return 'Microsoft 365 GCC High';
        }
        case 'OKTA_IDENTITY': {
            return 'Okta';
        }
        case 'JAMF': {
            return 'Jamf';
        }
        case 'INTUNE': {
            return 'Intune';
        }
        case 'INTUNE_GCC_HIGH': {
            return 'Intune GCC High';
        }
        case 'KANDJI': {
            return 'Kandji';
        }
        case 'JUMPCLOUD': {
            return 'JumpCloud MDM';
        }
        case 'MERGEDEV_JUMPCLOUD': {
            return 'JumpCloud IdP';
        }
        case 'MERGEDEV_CYBERARK': {
            return 'CyberArk';
        }
        case 'DRATA_DEV': {
            return 'Drata Dev';
        }
        case 'AWS': {
            return 'AWS';
        }
        case 'AWS_ORG_UNITS': {
            return 'AWS Org Units';
        }
        case 'AWS_GOV_CLOUD': {
            return 'AWS GovCloud';
        }
        case 'HEXNODE_UEM': {
            return 'Hexnode UEM';
        }
        case 'RIPPLING': {
            return 'Rippling';
        }
        case 'WORKSPACE_ONE': {
            return 'Workspace ONE';
        }
        case 'CSV_IDP': {
            return 'CSV';
        }
        case 'AZURE': {
            return 'Azure';
        }
        case 'MERGEDEV_ONELOGIN': {
            return 'MergeDev OneLogin';
        }
        case 'GCP': {
            return 'GCP';
        }
        case 'KOLIDE': {
            return 'Kolide';
        }
        case 'MERGEDEV_PINGONE': {
            return 'PingOne';
        }
        default: {
            return 'Unknown';
        }
    }
}
