import { Banner } from '@cosmos/components/banner';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedPoliciesRenewalDateBannerController } from './controllers/policies-renewal-date-banner.controller';

export const PoliciesRenewalDateBanner = observer(
    (): React.JSX.Element | null => {
        const { isVisible } = sharedPoliciesRenewalDateBannerController;

        if (!isVisible) {
            return null;
        }

        const handleClose = (): void => {
            sharedPoliciesRenewalDateBannerController.handleClose();
        };

        return (
            <Banner
                body={t`It's a best practice to have personnel acknowledge policies at least once a year and many frameworks require it. Tasks and tests are automated based on renewal dates to help you stay on top of your goals. Default renewal dates are 1 year from the last approval or creation date, but you can change them as needed.`}
                closeButtonAriaLabel={t`Hide banner`}
                displayMode="full"
                severity="primary"
                title={t`Policies have renewal dates to make it easier to prepare for compliance goals`}
                data-id="renewal-date-banner"
                onClose={handleClose}
            />
        );
    },
);
