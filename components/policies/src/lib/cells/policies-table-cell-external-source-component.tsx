import { isNil } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableCellExternalSourceComponent = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { version, policyStatus } = row.original;

    if (
        isNil(version) ||
        version.type === 'BUILDER' ||
        version.type === 'UPLOADED'
    ) {
        return (
            <Text
                data-testid="PoliciesTableCellExternalSourceComponent"
                data-id="Zty0t8J7"
            >
                {t({
                    message: 'Not Linked',
                    comment:
                        'Status for policies not linked to external source',
                })}
            </Text>
        );
    }

    // If is external policy linked
    if (version.type === 'EXTERNAL' && policyStatus === 'UNACCEPTABLE') {
        return (
            <Text>
                {t({
                    message: 'Linked',
                    comment: 'Status for policies linked to external source',
                })}
            </Text>
        );
    }

    // isExternalPolicyUnacceptable
    if (policyStatus === 'UNACCEPTABLE' || version.type === 'EXTERNAL') {
        return (
            <Text>
                {t({
                    message: 'Missing',
                    comment: 'Status for missing external policies',
                })}
            </Text>
        );
    }

    return (
        <Text
            data-testid="PoliciesTableCellExternalSourceComponent"
            data-id="Zty0t8J7"
        >
            {t({
                message: 'Not Linked',
                comment: 'Status for policies not linked to external source',
            })}
        </Text>
    );
};
