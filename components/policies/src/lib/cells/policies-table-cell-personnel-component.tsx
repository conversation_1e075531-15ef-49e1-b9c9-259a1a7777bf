import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableCellPersonnelComponent = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { assignedTo, groups = [] } = row.original;

    switch (assignedTo) {
        case 'ALL': {
            return (
                <Metadata
                    colorScheme="neutral"
                    type="tag"
                    label={t({
                        message: 'All personnel',
                        comment: 'Label for policies assigned to all personnel',
                    })}
                />
            );
        }
        case 'GROUP': {
            return (
                <Stack gap="1x" direction="column">
                    {groups.map((group) => (
                        <Metadata
                            key={`Metadata-${group.name}`}
                            label={group.name}
                            colorScheme="neutral"
                            type="tag"
                            data-id="E9WP0tXV"
                        />
                    ))}
                </Stack>
            );
        }
        default: {
            return <EmptyStateTableCell />;
        }
    }
};
