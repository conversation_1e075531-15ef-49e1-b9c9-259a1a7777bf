import { isNil } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import type { PolicyTableVersionResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableCellTextComponent = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { version, templateId } = row.original;

    const getVersionType = (policyVersion: PolicyTableVersionResponseDto) => {
        switch (true) {
            case policyVersion.type === 'UPLOADED': {
                return t({
                    message: 'Uploaded custom',
                    comment: 'Policy type for uploaded custom policies',
                });
            }

            case policyVersion.type === 'BUILDER' && isNil(templateId): {
                return t({
                    message: 'Authored custom',
                    comment: 'Policy type for authored custom policies',
                });
            }

            case policyVersion.type === 'BUILDER' && !isNil(templateId): {
                return t({
                    message: 'via Drata',
                    comment: 'Policy type for Drata template policies',
                });
            }

            default: {
                return t({
                    message: 'External Policy',
                    comment: 'Policy type for external policies',
                });
            }
        }
    };

    if (isNil(version.createdAt)) {
        return <>-</>;
    }

    const { createdAt } = version;

    return (
        <>
            <DateTime
                date={createdAt}
                format="table"
                data-testid="PoliciesTableCellTextComponent"
                data-id="Tji75cBN"
            />
            <br />
            <Text
                size="100"
                data-testid="PoliciesTableCellTextComponent"
                data-id="Tji75cBN"
            >
                {getVersionType(version)}
            </Text>
        </>
    );
};
