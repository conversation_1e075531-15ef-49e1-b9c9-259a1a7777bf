import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { handleRestorePolicy } from '../helpers/handle-restore-policy.helper';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableArchivedActionsCell = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    return (
        <SchemaDropdown
            isIconOnly
            startIconName="Action"
            level="tertiary"
            colorScheme="neutral"
            label="Archive policy actions"
            data-testid="PoliciesTableArchivedActionsCell"
            data-id={row.original.id.toString()}
            items={[
                {
                    id: 'policy-restore-button',
                    label: 'Restore policy',
                    type: 'item',
                    startIconName: 'Sync',
                    onSelect: handleRestorePolicy(row),
                },
            ]}
        />
    );
};
