import { isEmpty } from 'lodash-es';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableCellSLAComponent = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const isMonitored =
        !isEmpty(row.original.policyP3MatrixSLAs) ||
        !isEmpty(row.original.policyWeekTimeFrameSLAs) ||
        !isEmpty(row.original.policyGracePeriodSLAs);

    return (
        <Text
            shouldWrap
            align="left"
            size="100"
            type="body"
            data-testid="PoliciesTableCellSLAComponent"
            data-id="SiuaHPlb"
        >
            {isMonitored
                ? t({
                      message: 'Monitored by <PERSON><PERSON>',
                      comment: 'SLA status when policy is monitored by <PERSON><PERSON>',
                  })
                : t({
                      message: 'None',
                      comment: 'SLA status when policy has no SLA monitoring',
                  })}
        </Text>
    );
};
