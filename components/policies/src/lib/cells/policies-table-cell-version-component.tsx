import { isNil } from 'lodash-es';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { t } from '@globals/i18n/macro';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const PoliciesTableCellVersionComponent = ({
    row,
}: PoliciesTableCellProps): React.JSX.Element => {
    const { version, isDraft, latestDraftVersionId } = row.original;

    const isNewPolicy = latestDraftVersionId === version.id;

    if (isNil(version.formatted)) {
        return <EmptyValue label="-" />;
    }

    return (
        <Stack
            gap="md"
            data-testid="PoliciesTableCellVersionComponent"
            data-id="WBOkx9Q4"
        >
            {!isNewPolicy && (
                <Metadata
                    colorScheme="neutral"
                    label={`V${version.formatted}`}
                    type="tag"
                    data-testid="PoliciesTableCellVersionComponent"
                    data-id="WBOkx9Q4"
                />
            )}
            {isDraft && (
                <Metadata
                    colorScheme="warning"
                    type="tag"
                    data-testid="PoliciesTableCellVersionComponentDraft"
                    data-id="WBOkx9Q4"
                    label={t({
                        message: 'Draft',
                        comment: 'Label for draft policy versions',
                    })}
                />
            )}
        </Stack>
    );
};
