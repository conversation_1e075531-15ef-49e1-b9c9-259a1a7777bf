import { sharedPoliciesMutationController } from '@controllers/policies';
import { t } from '@globals/i18n/macro';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const handleArchivePolicy = (
    row: PoliciesTableCellProps['row'],
): (() => void) => {
    return () => {
        const { id, name } = row.original;

        openConfirmationModal({
            title: t`Archive Policy`,
            body: t`Are you sure you want to archive "${name}"? This action can be undone by restoring the policy from the archive.`,
            confirmText: t`Archive`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                sharedPoliciesMutationController.archivePolicy(id);
                closeConfirmationModal();
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    };
};
