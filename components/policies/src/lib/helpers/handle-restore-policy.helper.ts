import { sharedPoliciesMutationController } from '@controllers/policies';
import type { OnSelect } from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const handleRestorePolicy = (
    row: PoliciesTableCellProps['row'],
): OnSelect =>
    action(() => {
        const { id, name } = row.original;

        openConfirmationModal({
            title: t`Restore Policy`,
            body: t`Are you sure you want to restore "${name}"? This will move the policy back to the active policies list.`,
            confirmText: t`Restore`,
            cancelText: t`Cancel`,
            type: 'primary',
            onConfirm: () => {
                sharedPoliciesMutationController.restorePolicy(id);
                closeConfirmationModal();
            },
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    });
