import { isNil } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import type { OnSelect } from '@cosmos/components/schema-dropdown';
import { policiesControllerGetCurrentPublishedPolicyPdfDownloadUrlOptions } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { action, ObservedQuery, when } from '@globals/mobx';
import { downloadFileFromSignedUrl } from '@helpers/download-file';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const handleViewPolicyPdf = (
    row: PoliciesTableCellProps['row'],
): OnSelect =>
    action(async () => {
        const { id, version } = row.original;

        // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition -- version can be null in some cases
        if (!version) {
            return;
        }

        try {
            // Create a query to get the PDF download URL using the API SDK
            const pdfQuery = new ObservedQuery(
                policiesControllerGetCurrentPublishedPolicyPdfDownloadUrlOptions,
            );

            // Load the PDF download URL
            pdfQuery.load({
                path: { id: Number(id) },
            });

            // Wait for the query to complete
            await when(() => !pdfQuery.isLoading);

            if (pdfQuery.error) {
                throw pdfQuery.error;
            }

            const signedUrl = pdfQuery.data?.signedUrl;

            if (isNil(signedUrl)) {
                throw new Error(
                    t({
                        message: 'Missing url from download policy data',
                        comment: 'Error when the url generated is missing',
                    }),
                );
            }

            // Use the helper to download the file from signed URL
            downloadFileFromSignedUrl(signedUrl);
        } catch (error) {
            console.error('Error downloading policy PDF:', error);

            snackbarController.addSnackbar({
                id: 'policy-pdf-download-error',
                props: {
                    title: t({
                        message: 'Unable to download policy.',
                        comment: 'Error message when unable to download policy',
                    }),
                    severity: 'critical',
                    closeButtonAriaLabel: t({
                        message: 'Close',
                        comment: 'Close button label',
                    }),
                },
            });
        }
    });
