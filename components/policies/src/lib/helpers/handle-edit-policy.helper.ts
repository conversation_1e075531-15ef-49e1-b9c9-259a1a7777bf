import type { OnSelect } from '@cosmos/components/schema-dropdown';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import type { PoliciesTableCellProps } from '../types/policies.types';

export const handleEditPolicy = (
    row: PoliciesTableCellProps['row'],
    navigate: (path: string) => void,
): OnSelect =>
    action(() => {
        const { id } = row.original;
        const { currentWorkspace } = sharedWorkspacesController;

        if (!currentWorkspace) {
            return;
        }

        // Navigate to policy builder
        navigate(
            `/workspaces/${currentWorkspace.id}/governance/policies/builder/${id}/overview`,
        );
    });
