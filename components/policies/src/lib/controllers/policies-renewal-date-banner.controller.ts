import { isUndefined } from 'lodash-es';
import { makeAutoObservable, runInAction } from '@globals/mobx';

const RENEWAL_DATE_BANNER_STORAGE_KEY = 'multiverse-renewal-date-banner-hidden';

/**
 * Controller for managing the policies renewal date banner visibility state.
 * Follows the multiverse MVC pattern with MobX reactive state management.
 */
class PoliciesRenewalDateBannerController {
    #isHidden = false;

    constructor() {
        makeAutoObservable(this);
        this.#initializeFromStorage();
    }

    /**
     * Initialize the banner visibility state from localStorage.
     * Handles SSR by checking for window availability.
     */
    #initializeFromStorage(): void {
        if (isUndefined(window)) {
            return;
        }

        try {
            const storedValue = localStorage.getItem(
                RENEWAL_DATE_BANNER_STORAGE_KEY,
            );

            this.#isHidden = storedValue === 'true';
        } catch (error) {
            // Gracefully handle localStorage errors (e.g., in private browsing mode)
            console.warn(
                'Failed to read banner state from localStorage:',
                error,
            );
        }
    }

    /**
     * Whether the banner should be visible to the user.
     */
    get isVisible(): boolean {
        return !this.#isHidden;
    }

    /**
     * Hide the banner and persist the preference to localStorage.
     * Uses MobX action for proper state management.
     */
    handleClose(): void {
        runInAction(() => {
            this.#isHidden = true;
        });

        if (!isUndefined(window)) {
            try {
                localStorage.setItem(RENEWAL_DATE_BANNER_STORAGE_KEY, 'true');
            } catch (error) {
                // Gracefully handle localStorage errors
                console.warn(
                    'Failed to save banner state to localStorage:',
                    error,
                );
            }
        }
    }

    /**
     * Reset the banner visibility (useful for testing or admin purposes).
     */
    resetBanner(): void {
        runInAction(() => {
            this.#isHidden = false;
        });

        if (!isUndefined(window)) {
            try {
                localStorage.removeItem(RENEWAL_DATE_BANNER_STORAGE_KEY);
            } catch (error) {
                console.warn(
                    'Failed to reset banner state in localStorage:',
                    error,
                );
            }
        }
    }
}

/**
 * Shared singleton instance following multiverse pattern.
 * Export as sharedXxxController for consistent naming.
 */
export const sharedPoliciesRenewalDateBannerController =
    new PoliciesRenewalDateBannerController();
