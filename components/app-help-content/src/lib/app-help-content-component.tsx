import { styled } from 'styled-components';
import { ListBox } from '@cosmos/components/list-box';
import { dimension40x, dimensionLg } from '@cosmos/constants/tokens';

const StyledListBoxWrapperDiv = styled.div`
    width: calc(2 * ${dimension40x});
    padding: ${dimensionLg};
`;

interface Props {
    'aria-labelledby': string;
}

export const AppHelpContentComponent = ({
    'aria-labelledby': ariaLabelledBy,
}: Props): React.JSX.Element => {
    return (
        <StyledListBoxWrapperDiv
            data-testid="AppHelpContentComponent"
            data-id="xpbkeUE3"
        >
            <ListBox
                aria-labelledby={ariaLabelledBy}
                data-id="listbox"
                emptyState="No items were found"
                id="listbox"
                loaderLabel="Loading"
                role="listbox"
                items={[
                    {
                        groupHeader: 'Talk to an actual human',
                        items: [
                            {
                                id: 'using-drata-question-option',
                                label: 'I have a question about using <PERSON><PERSON>',
                                startIconName: 'Information',
                            },
                            {
                                id: 'compliance-audit-question-option',
                                label: 'I have a compliance or audit question',
                                startIconName: 'Frameworks',
                            },
                            {
                                id: 'drata-support-remote-access-option',
                                label: 'Grant Drata Support remote access',
                                startIconName: 'Support',
                            },
                        ],
                    },
                    {
                        groupHeader: 'Help and feedback',
                        items: [
                            {
                                id: 'explore-articles-option',
                                label: 'Help center',
                                description:
                                    'Explore our collection of hundreds of articles so you can use Drata like a pro',
                                startIconName: 'Help',
                            },
                            {
                                id: 'roadmap-ideas-option',
                                label: 'Roadmap, ideas and releases',
                                description:
                                    'Preview upcoming features, submit ideas, and explore new features',
                                startIconName: 'Lightbulb',
                            },
                        ],
                    },
                ]}
            />
        </StyledListBoxWrapperDiv>
    );
};
