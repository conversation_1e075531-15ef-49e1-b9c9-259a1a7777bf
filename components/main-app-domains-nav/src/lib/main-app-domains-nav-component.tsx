import { isEmpty } from 'lodash-es';
import { routeController } from '@controllers/route';
import type { IconName } from '@cosmos/components/icon';
import { observer } from '@globals/mobx';
import { NavAppLinkUi } from '@ui/nav-app-link';
import styles from './main-app-domains-nav-component.module.css';

export const MainAppDomainsNavComponent = observer((): React.JSX.Element => {
    const { domainsNav, userPartOfUrl } = routeController;

    const { domainsOrder = [], domains = {} } = domainsNav?.navigation ?? {};

    if (isEmpty(domainsOrder)) {
        return <>{null}</>;
    }

    return (
        <div
            data-testid="MainAppDomainsNavComponent"
            data-id="XonB268I"
            className={styles['app-nav']}
        >
            {domainsOrder.map((domainId) => {
                const {
                    label: domainLabel,
                    topicsOrder,
                    topics,
                } = domains[domainId];

                return (
                    <nav
                        aria-label={domainLabel}
                        data-id="OaHfamoS"
                        key={domainId}
                        className={styles['app-nav__navigation']}
                    >
                        <ul>
                            {topicsOrder.map((topicId) => {
                                const { id, label, icon, topicPath } =
                                    topics[topicId];

                                const key = `primary-nav-link-${id}`;

                                return (
                                    <li key={key} data-id="pUR259dk">
                                        <NavAppLinkUi
                                            shouldShowTooltip
                                            label={label}
                                            href={`${userPartOfUrl}/${topicPath}`}
                                            type="collapsed"
                                            // TODO: This typing should get fixed at the source when it is ready
                                            startIconName={icon as IconName}
                                        />
                                    </li>
                                );
                            })}
                        </ul>
                    </nav>
                );
            })}
        </div>
    );
});
