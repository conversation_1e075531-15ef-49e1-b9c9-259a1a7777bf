import { Metadata } from '@cosmos/components/metadata';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const IdentityMfaCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { hasMfa } = original;

    return (
        <Metadata
            data-id="Vn4nMNZR"
            type="status"
            label={hasMfa ? 'Enabled' : 'Not enabled'}
            data-testid="IdentityMfaCell"
            colorScheme={hasMfa ? 'success' : 'critical'}
            iconName={hasMfa ? 'CheckCircle' : 'Cancel'}
        />
    );
};
