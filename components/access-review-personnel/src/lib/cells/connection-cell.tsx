import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const ConnectionCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { clientId, clientAlias } = original;

    return (
        <Stack
            data-id="yrXwgfno"
            direction="column"
            gap="2x"
            data-testid="ConnectionCell"
        >
            {clientId && <Text size="200">{clientId}</Text>}
            {clientAlias && <Text size="200">{`(${clientAlias})`}</Text>}
        </Stack>
    );
};
