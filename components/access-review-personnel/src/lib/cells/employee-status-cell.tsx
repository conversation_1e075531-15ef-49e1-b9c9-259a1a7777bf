import { Text } from '@cosmos/components/text';
import { getEmployeeStatusLabel } from '../constants/access-review-cell.constant';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const EmployeeStatusCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { employmentStatus } = original;

    return (
        <Text data-testid="EmployeeStatusCell" data-id="JG3g9H-9">
            {getEmployeeStatusLabel(employmentStatus)}
        </Text>
    );
};
