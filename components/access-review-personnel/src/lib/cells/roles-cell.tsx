import { isArray, isEmpty, isNil, isObject } from 'lodash-es';
import { useRef, useState } from 'react';
import { Button } from '@cosmos/components/button';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { AppLink } from '@ui/app-link';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const RolesCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { roles, clientType, externalUserProfile } = original;
    const MINIMUM_VALUE = 1;
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);

    if (
        clientType === 'OKTA_IDENTITY' &&
        !isNil(externalUserProfile) &&
        isEmpty(roles)
    ) {
        return (
            <AppLink
                isExternal
                href={externalUserProfile[0]}
                label={t`View account details`}
                size="sm"
            />
        );
    }

    if (!isNil(roles) && isArray(roles)) {
        type Role = string | { name: string };
        const roleNames = (roles as unknown as Role[]).map((role) =>
            isObject(role) ? role.name : role,
        );

        const firstRole = isObject(roles[0])
            ? (roles[0] as unknown as { name: string }).name
            : roles[0];

        return roles.length > MINIMUM_VALUE ? (
            <>
                <Text type="body" size="200" as="span">
                    {isNil(firstRole) ? '-' : firstRole}
                </Text>
                <Button
                    ref={buttonRef}
                    level="tertiary"
                    colorScheme="neutral"
                    size="sm"
                    label={`+${roles.length - MINIMUM_VALUE}`}
                    data-id="roles-cell-button"
                    onClick={() => {
                        setIsPopoverOpen(true);
                    }}
                />
                <Popover
                    anchor={buttonRef.current}
                    isOpen={isPopoverOpen}
                    placement="bottom-start"
                    padding="md"
                    data-id="roles-cell-popover"
                    content={
                        <Stack
                            direction="column"
                            gap="xs"
                            data-id="roles-stack"
                        >
                            {roleNames.slice(1).map((roleName) => (
                                <Text
                                    key={roleName}
                                    type="body"
                                    size="200"
                                    data-id="role-text"
                                >
                                    {roleName}
                                </Text>
                            ))}
                        </Stack>
                    }
                    onDismiss={() => {
                        setIsPopoverOpen(false);
                    }}
                />
            </>
        ) : (
            <Text type="body" size="200">
                {isNil(firstRole) ? '-' : firstRole}
            </Text>
        );
    }

    return <EmptyStateTableCell data-id="qW2ZQjUr" data-testid="RolesCell" />;
};
