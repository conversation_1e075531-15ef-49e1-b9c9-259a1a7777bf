import { isNil } from 'lodash-es';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { getUserInitials } from '@helpers/user';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const PersonnelOwnerCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { user, username } = original;

    return (
        <AvatarIdentity
            secondaryLabel={isNil(user) ? '' : user.email}
            data-testid="PersonnelOwnerCell"
            data-id="OBQoq0mC"
            primaryLabel={
                isNil(user) ? username : `${user.firstName} ${user.lastName}`
            }
            imgSrc={
                isNil(user) || isNil(user.avatarUrl)
                    ? undefined
                    : user.avatarUrl
            }
            fallbackText={
                isNil(user)
                    ? getUserInitials({ firstName: username, lastName: '' })
                    : getUserInitials({
                          firstName: user.firstName,
                          lastName: user.lastName,
                      })
            }
        />
    );
};
