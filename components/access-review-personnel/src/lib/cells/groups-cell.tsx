import { isArray, isEmpty, isNil } from 'lodash-es';
import { useRef, useState } from 'react';
import { Button } from '@cosmos/components/button';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Popover } from '@cosmos/components/popover';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { AppLink } from '@ui/app-link';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const GroupsCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { groups, externalUserProfile, clientType } = original;
    const MINIMUM_VALUE = 1;
    const [isPopoverOpen, setIsPopoverOpen] = useState(false);
    const buttonRef = useRef<HTMLButtonElement>(null);

    if (
        clientType === 'OKTA_IDENTITY' &&
        !isNil(externalUserProfile) &&
        isEmpty(groups)
    ) {
        return (
            <AppLink
                isExternal
                href={externalUserProfile[0]}
                label={t`View account details`}
                size="sm"
            />
        );
    }
    if (isEmpty(groups)) {
        return (
            <EmptyStateTableCell data-id="qW2ZQjUr" data-testid="GroupsCell" />
        );
    }

    if (!isNil(groups) && isArray(groups)) {
        const firstGroup = groups[0];

        return groups.length > MINIMUM_VALUE ? (
            <>
                <Text type="body" size="200" as="span">
                    {firstGroup}
                </Text>
                <Button
                    ref={buttonRef}
                    level="tertiary"
                    colorScheme="neutral"
                    size="sm"
                    label={`+${groups.length - MINIMUM_VALUE}`}
                    data-id="groups-cell-button"
                    onClick={() => {
                        setIsPopoverOpen(true);
                    }}
                />
                <Popover
                    anchor={buttonRef.current}
                    isOpen={isPopoverOpen}
                    placement="bottom-start"
                    padding="md"
                    data-id="groups-cell-popover"
                    content={
                        <Stack direction="column" gap="xs">
                            {groups.slice(1).map((groupName) => (
                                <Text
                                    key={groupName}
                                    type="body"
                                    size="200"
                                    data-id="mByjjbCx"
                                >
                                    {groupName}
                                </Text>
                            ))}
                        </Stack>
                    }
                    onDismiss={() => {
                        setIsPopoverOpen(false);
                    }}
                />
            </>
        ) : (
            <Text
                type="body"
                size="200"
                data-id="qW2ZQjUr"
                data-testid="GroupsCell"
                as="p"
            >
                {firstGroup}
            </Text>
        );
    }

    return <EmptyStateTableCell data-id="qW2ZQjUr" data-testid="GroupsCell" />;
};
