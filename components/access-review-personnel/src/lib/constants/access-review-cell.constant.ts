import type { UserAccessReviewApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getEmployeeStatusLabel = (
    employeeStatus: UserAccessReviewApplicationResponseDto['employmentStatus'],
): string => {
    switch (employeeStatus) {
        case 'CURRENT_EMPLOYEE': {
            return t`Current employee`;
        }
        case 'FORMER_EMPLOYEE':
        case 'SPECIAL_FORMER_EMPLOYEE': {
            return t`Former employee`;
        }
        case 'CURRENT_CONTRACTOR': {
            return t`Current contractor`;
        }
        case 'FORMER_CONTRACTOR':
        case 'SPECIAL_FORMER_CONTRACTOR': {
            return t`Former contractor`;
        }
        case 'OUT_OF_SCOPE': {
            return t`Out of scope (ignore)`;
        }
        case 'UNKNOWN': {
            return t`Unknown`;
        }
        case 'FUTURE_HIRE': {
            return t`Future hire`;
        }
        case 'SERVICE_ACCOUNT': {
            return t`Out of scope (service account)`;
        }
        default: {
            return employeeStatus;
        }
    }
};
