import { noop } from 'lodash-es';
import { Button } from '@cosmos/components/button';
import { DatePickerField } from '@cosmos/components/date-picker-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';

const FORM_ID = 'addReviewPeriodForm';
const VIEW_ID = 'addReviewPeriod';

export const AccessReviewCreatePeriodComponent = (): React.JSX.Element => {
    return (
        <Stack
            gap="lg"
            direction="column"
            data-testid="AccessReviewCreatePeriodComponent"
            data-id="js7YUK6d"
        >
            <Text>
                <Trans>
                    Select the start and end dates for your company to perform
                    the next access review. Reviewers receive an email when you
                    add or remove them from a review.
                </Trans>
            </Text>
            <form id={FORM_ID} data-id={FORM_ID}>
                <Stack gap="lg" direction="column">
                    <DatePickerField
                        data-id={`${FORM_ID}-start-date`}
                        formId={`${FORM_ID}-start-date`}
                        label={t`Start date`}
                        name="startDate"
                        locale={'en-US'}
                        monthSelectionFieldLabel={t`Select Month`}
                        yearSelectionFieldLabel={t`Select Year`}
                        onChange={noop}
                    />
                    <DatePickerField
                        data-id={`${FORM_ID}-end-date`}
                        formId={`${FORM_ID}-end-date`}
                        name="endDate"
                        label={t`End date`}
                        locale={'en-US'}
                        monthSelectionFieldLabel={t`Select Month`}
                        yearSelectionFieldLabel={t`Select Year`}
                        onChange={noop}
                    />
                    <Stack gap="xl" direction="column" align="start">
                        <Button
                            type="submit"
                            label={t`Save review period`}
                            colorScheme="primary"
                            data-id={`${VIEW_ID}-${FORM_ID}-submitButton`}
                        />
                    </Stack>
                </Stack>
            </form>
        </Stack>
    );
};
