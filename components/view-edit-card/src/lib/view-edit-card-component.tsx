import { noop } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import type { Action } from '@cosmos/components/action-stack';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';

interface Props {
    title: string;
    readOnlyComponent: React.JSX.Element;
    editComponent: React.JSX.Element | null;
    onSave: () => Promise<boolean>;
    onEdit?: () => void;
    onCancel?: () => void;
}
export const ViewEditCardComponent = ({
    title,
    readOnlyComponent,
    editComponent,
    onSave,
    onEdit = noop,
    onCancel = noop,
}: Props): React.JSX.Element => {
    const [isEditing, setIsEditing] = useState(false);

    const handleEdit = useCallback(() => {
        onEdit();
        setIsEditing(true);
    }, [onEdit]);

    const handleSave = useCallback(async () => {
        try {
            const isValid = await onSave();

            // Keep edit mode open if onSave returns false
            // This prevents form validation errors from being hidden
            if (isValid) {
                setIsEditing(false);
            }
        } catch (error) {
            console.error('Error saving:', error);
        }
    }, [onSave]);

    const handleCancel = useCallback(() => {
        onCancel();
        setIsEditing(false);
    }, [onCancel]);

    const EDIT_ACTIONS: Action[] = useMemo(
        () => [
            {
                id: 'view-edit-card-save-btn',
                actionType: 'button',
                typeProps: {
                    label: t`Save`,
                    'data-id': 'view-edit-card-save-btn',
                    onClick: handleSave,
                },
            },
            {
                id: 'view-edit-card-cancel-btn',
                actionType: 'button',
                typeProps: {
                    label: t`Cancel`,
                    'data-id': 'view-edit-card-cancel-btn',
                    onClick: handleCancel,
                    level: 'secondary',
                },
            },
        ],
        [handleCancel, handleSave],
    );

    const READ_ONLY_ACTIONS: Action[] = useMemo(
        () => [
            {
                id: 'view-edit-card-edit-btn',
                actionType: 'button',
                typeProps: {
                    label: t`Edit`,
                    'data-id': 'view-edit-card-edit-btn',
                    onClick: handleEdit,
                    level: 'secondary',
                },
            },
        ],
        [handleEdit],
    );

    const actions: Action[] = useMemo(() => {
        if (!editComponent) {
            return [];
        }

        return isEditing ? EDIT_ACTIONS : READ_ONLY_ACTIONS;
    }, [editComponent, isEditing, EDIT_ACTIONS, READ_ONLY_ACTIONS]);

    return (
        <Card
            title={title}
            size="lg"
            actions={actions}
            isEditMode={isEditing}
            body={<Box>{isEditing ? editComponent : readOnlyComponent}</Box>}
            data-testid="ViewEditCardComponent"
            data-id="oudFIgCW"
        />
    );
};
